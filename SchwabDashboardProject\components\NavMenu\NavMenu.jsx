"use client";

import { useRouter } from "next/navigation";
import AuthButton from "./AuthButton";

import { motion, useAnimationControls, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import NavLink from "./NavLink";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  ChartBarIcon,
  ChartPieIcon,
  DocumentCheckIcon,
  Square2StackIcon,
  UserIcon,
  HomeIcon,
  ClipboardDocumentListIcon,
  BriefcaseIcon,
  ArrowTrendingUpIcon,
  AdjustmentsHorizontalIcon,
  TableCellsIcon,
  ShieldCheckIcon,
} from "@heroicons/react/24/outline";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import ProjectLink from "./ProjectLink";
import ProjectNav from "./ProjectNav";
import ThemeToggle from "../ThemeToggle";
import { useSession, signOut } from "next-auth/react";

const containerVariants = {
  close: {
    width: "5rem",
    transition: {
      type: "spring",
      damping: 15,
      duration: 0.5,
    },
  },
  open: {
    width: "16rem",
    transition: {
      type: "spring",
      damping: 15,
      duration: 0.5,
    },
  },
};

const svgVariants = {
  close: {
    rotate: 360,
  },
  open: {
    rotate: 180,
  },
};

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const { data: session } = useSession();

  const containerControls = useAnimationControls();
  const svgControls = useAnimationControls();

  useEffect(() => {
    if (isOpen) {
      containerControls.start("open");
      svgControls.start("open");
    } else {
      containerControls.start("close");
      svgControls.start("close");
    }
  }, [isOpen]);

  const handleOpenClose = () => {
    setIsOpen(!isOpen);
    setSelectedProject(null);
  };


  // Profile dropdown state
  const [showProfile, setShowProfile] = useState(false);

  return (
    <>
      <motion.nav
        variants={containerVariants}
        animate={containerControls}
        initial="close"
        className="bg-neutral-900 dark:bg-neutral-950 flex flex-col z-10 gap-10 p-5 fixed top-0 left-0 h-full shadow shadow-neutral-600 dark:shadow-neutral-800"
      >

        <div className="flex flex-row w-full justify-between place-items-center relative">
          {/* Profile Button with label */}
          <TooltipProvider delayDuration={20}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className={`w-10 h-10 flex items-center justify-center rounded-full focus:outline-none border-none bg-transparent p-0 ${isOpen ? 'pl-2 pr-4 py-1 w-auto' : ''}`}
                  onClick={() => setShowProfile(true)}
                  aria-label="Profile"
                  tabIndex={0}
                >
                  <UserIcon className="w-8 h-8 text-blue-400" />
                  {isOpen && <span className="ml-2 text-base font-medium text-neutral-200">Profile</span>}
                </button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <span>Profile</span>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Profile Dialog */}
          <Dialog open={showProfile} onOpenChange={setShowProfile}>
            <DialogContent className="max-w-md mx-auto">
              <DialogHeader>
                <DialogTitle>Profile</DialogTitle>
              </DialogHeader>
              <div className="flex flex-col items-center gap-3 mb-2">
                <UserIcon className="w-14 h-14 text-blue-400" />
                <div className="text-center">
                  <div className="font-semibold text-lg text-neutral-800 dark:text-neutral-100">{session?.user?.name || session?.user?.email || 'Not signed in'}</div>
                  <div className="text-xs text-neutral-500 dark:text-neutral-400">{session?.user?.email}</div>
                </div>
              </div>
              <div className="text-sm text-neutral-700 dark:text-neutral-300 mb-2">
                <div><span className="font-medium">Role:</span> {session?.user?.role || 'unknown'}</div>
              </div>
              <DialogFooter className="flex flex-col gap-2">
                <button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded"
                  onClick={() => {
                    setShowProfile(false);
                    console.log("Signing out...");
                    localStorage.clear();
                    sessionStorage.clear();
                    document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
                    document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
                    document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;

                    // Call NextAuth signOut to properly log out
                    signOut({ callbackUrl: '/generalLogin' });
                  }}
                >Sign Out</button>
                <button
                  className="w-full bg-neutral-200 dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 font-semibold py-2 rounded"
                  onClick={() => setShowProfile(false)}
                >Close</button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <TooltipProvider delayDuration={20}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="p-1 rounded-full flex"
                  onClick={() => handleOpenClose()}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1}
                    stroke="currentColor"
                    className="w-8 h-8 stroke-neutral-200"
                  >
                    <motion.path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      variants={svgVariants}
                      animate={svgControls}
                      d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                      transition={{
                        duration: 0.5,
                        ease: "easeInOut",
                      }}
                    />
                  </svg>
                </button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{isOpen ? "Collapse Menu" : "Expand Menu"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>



        <div className="flex flex-col gap-2">
          {/* Admin link only for admins */}
          {session?.user?.role?.toLowerCase() === "admin" && (
            <NavLink
              name="Admin"
              showName={isOpen}
              href="/admin"
              onClick={() => {
                if (isOpen) handleOpenClose();
              }}
            >
              <ShieldCheckIcon className="stroke-inherit w-8 min-w-8 h-8 text-blue-400" />
            </NavLink>
          )}
          <NavLink name="Home" showName={isOpen} href="/" onClick={() => {if (isOpen) handleOpenClose();}} >
            <HomeIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8"  />
          </NavLink>

          <NavLink
            name="Accounts Summary"
            showName={isOpen}
            href="/accountsSummary"
            onClick={() => {if (isOpen) handleOpenClose();}}
          >
            <ChartPieIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>

          <NavLink name="P & L Tracker" showName={isOpen} href="/plTracker" onClick={() => {if (isOpen) handleOpenClose();}}>
            <ChartBarIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>

          <NavLink name="Events Log" showName={isOpen} href="/eventsLog" onClick={() => {if (isOpen) handleOpenClose();}}>
            <ClipboardDocumentListIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>

          <NavLink name="File Handler" showName={isOpen} href="/fileHandler" onClick={() => {if (isOpen) handleOpenClose();}}>
            <BriefcaseIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>

          <NavLink name="Saved Pairs" showName={isOpen} href="/savedPairs" onClick={() => {if (isOpen) handleOpenClose();}}>
            <TableCellsIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>
        </div>
        <div className="flex flex-col gap-2">
          <div>
            <h2 className="">WB</h2>
          </div>
          <NavLink name="WB Dashboard" showName={isOpen} href="/Strategies/WB/dashboard" onClick={() => {if (isOpen) handleOpenClose();}}>
            <ArrowTrendingUpIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>
          <NavLink name="WB Configuration" showName={isOpen} href="/Strategies/WB/configuration" onClick={() => {if (isOpen) handleOpenClose();}}>
            <AdjustmentsHorizontalIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>

          <div>
            <h2>SCALP</h2>
          </div>
          <NavLink name="SCALP Dashboard" showName={isOpen} href="/Strategies/Scalp/dashboard" onClick={() => {if (isOpen) handleOpenClose();}}>
            <ArrowTrendingUpIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>
          <NavLink name="SCALP Configuration" showName={isOpen} href="/Strategies/Scalp/configuration" onClick={() => {if (isOpen) handleOpenClose();}}>
            <AdjustmentsHorizontalIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
          </NavLink>
        </div>
        <div className="flex flex-col gap-2 mt-auto">
          <ThemeToggle iconOnly={!isOpen} />
        </div>
      </motion.nav>
      <AnimatePresence>
        {selectedProject && (
          <ProjectNav
            selectedProject={selectedProject}
            setSelectedProject={setSelectedProject}
            isOpen={isOpen}
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default Navigation;
