"use client";

import { signIn, signOut, useSession } from "next-auth/react";
import {
  getAuthorizationCodeURL,
  storeAuthorizationCode,
} from "../../actions/schwabAccess";

export default function AuthButton() {
  // NEW ATTEMPT
  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        window.location.href = await getAuthorizationCodeURL();
        return;
      }
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  // OLD ATTEMPT
  // const handleLogInToSchwab = async () => {
  //   const authorizationCodeUrl = await getAuthorizationCodeURL();
  //   console.log("backend Authorization Code URL: " + authorizationCodeUrl);
  //   const newTab = window.open(authorizationCodeUrl, "_blank");

  //   const checkTab = setInterval(() => {
  //     try {
  //       if (newTab.closed) {
  //         clearInterval(checkTab);
  //         console.log("Tab closed by user");
  //       } else if (newTab.location.href.includes("code=")) {
  //         const urlParams = new URLSearchParams(
  //           new URL(newTab.location.href).search
  //         );
  //         const authorizationCode = urlParams.get("code");
  //         console.log("Received authorization code: " + authorizationCode);
  //         newTab.close();
  //         clearInterval(checkTab);
  //         // Here you can store the authorization code if needed
  //         // await storeAuthorizationCode(authorizationCode);
  //       }
  //     } catch (error) {
  //       // Ignore cross-origin errors until the tab reaches a URL with the authorization code
  //     }
  //   }, 1000);
  // };

  // const { data: session } = useSession();

  // if (session) {
  //   return (
  //     <>
  //       Signed in as {session?.user?.name} <br />
  //       <button
  //         className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded flex items-center"
  //         onClick={() => signOut()}
  //       >
  //         Sign out
  //       </button>
  //     </>
  //   );
  // }

  return (
    <>
      <button
        className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-1 px-2 rounded flex items-center transition-colors"
        // onClick={() => signIn("Schwab")}
        onClick={() => handleLogInToSchwab()}
      >
        Sign in
      </button>
    </>
  );
}
