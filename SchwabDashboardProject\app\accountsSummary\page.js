"use client";


import ProtectedRoute from "../../components/ProtectedRoute";
import AccountBalancesCard from "./AccountBalancesCard";
import PositionsCard from "./PositionsCard";
import OrdersCard from "./OrdersCard";
import TransactionsCard from "./TransactionsCard";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { useEndpointAppContext } from "@/components/EndpointAppContext";
import DateRangeSelector from "@/components/DateRangeSelector";

export default function AccountsSummary() {
  const [focused, setFocused] = useState("balances");

  // Get date range context for header display
  const {
    transactionsDateRange,
    ordersDateRange,
    updateTransactionsDateRange,
    updateOrdersDateRange
  } = useEndpointAppContext();
  const cards = [
    {
      key: "balances",
      title: "Account Balances",
      color: "from-blue-600 to-indigo-700 dark:from-blue-700 dark:to-indigo-800",
      component: <AccountBalancesCard />,
    },
    {
      key: "positions",
      title: "Positions",
      color: "from-green-600 to-teal-700 dark:from-green-700 dark:to-teal-800",
      component: <PositionsCard />,
    },
    {
      key: "orders",
      title: "Orders",
      color: "from-amber-500 to-orange-600 dark:from-amber-600 dark:to-orange-700",
      component: <OrdersCard />,
    },
    {
      key: "transactions",
      title: "Transactions",
      color: "from-purple-600 to-pink-700 dark:from-purple-700 dark:to-pink-800",
      component: <TransactionsCard />,
    },
  ];

  const focusedCard = cards.find((c) => c.key === focused);
  const smallCards = cards.filter((c) => c.key !== focused);

  return (
    <ProtectedRoute>
<div className="min-h-screen h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 flex flex-col">
        <div className="w-full flex-1 flex flex-col">
          {/* Header section */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-3 px-4 shadow-md dark:shadow-indigo-950 mb-3">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-bold">Accounts Summary</h1>
                <p className="text-blue-100 text-sm">View your account balances, positions, orders, and transactions</p>
              </div>

              {/* Date Range Selector for Orders/Transactions */}
              {focused === 'orders' && (
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium text-amber-100">
                    Orders:
                  </span>
                  <DateRangeSelector
                    selectedRange={ordersDateRange}
                    onRangeChange={updateOrdersDateRange}
                  />
                </div>
              )}

              {focused === 'transactions' && (
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium text-purple-100">
                    Transactions:
                  </span>
                  <DateRangeSelector
                    selectedRange={transactionsDateRange}
                    onRangeChange={updateTransactionsDateRange}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Animated Cards Layout - Full Height */}
          <div className="flex flex-col gap-4 px-6 flex-1 min-h-0 pb-6">
            {/* Top row: small cards (1/3 height) */}
            <div className="flex gap-4 mb-2 h-1/3 min-h-[180px] max-h-[33vh]">
              {smallCards.map((card) => (
                <motion.div
                  key={card.key}
                  layout
                  whileHover={{ scale: 1.04 }}
                  className="flex-1 min-w-0 cursor-pointer rounded-xl shadow-md dark:shadow-gray-900 bg-white dark:bg-gray-800 overflow-hidden flex flex-col transition-all duration-300 border-2 border-transparent hover:border-indigo-400"
                  onClick={() => setFocused(card.key)}
                >
                  <div className={`bg-gradient-to-r ${card.color} py-2 px-4`}>
                    <h2 className="text-lg font-semibold text-white select-none">{card.title}</h2>
                  </div>
                  <div className="flex-1 p-2 overflow-auto">
                    {card.component}
                  </div>
                </motion.div>
              ))}
            </div>
            {/* Focused card: large and animated (2/3 height) */}
            <AnimatePresence mode="wait">
              <motion.div
                key={focusedCard.key}
                layout
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 40 }}
                transition={{ duration: 0.4 }}
                className="max-w-full self-stretch bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-gray-900 overflow-hidden flex flex-col flex-1 min-h-0"
                style={{ minHeight: 0, maxHeight: '67vh' }}
              >
                <div
                  className={`bg-gradient-to-r ${focusedCard.color} py-3 px-6 cursor-pointer group transition-all duration-300`}
                  onClick={() => {
                    // Optionally collapse or do nothing
                  }}
                >
                  <h2 className="text-xl font-bold text-white group-hover:underline select-none">
                    {focusedCard.title}
                  </h2>
                </div>
                <div className="flex-1 min-h-0 p-4 overflow-auto">
                  {focusedCard.component}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
