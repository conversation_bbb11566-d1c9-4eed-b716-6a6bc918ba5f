"use server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { saveTradingPairs, getTradingPairs, getUserByEmail } from "@/lib/prisma-dal";
import { validateInput, tradingPairsSchema, sanitizeObject } from "@/lib/validation";

export async function POST(request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user from the database
    const user = await getUserByEmail(session.user.email);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Use user.id which is already set in getUserByEmail
    const userId = user.id;
    console.log("User ID for saving pairs:", userId);

    // Parse the request body
    const rawData = await request.json();

    // Validate the trading pairs data with a lightweight schema
    const validation = validateInput(tradingPairsSchema, rawData);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid trading pairs data",
          details: validation.errors
        },
        { status: 400 }
      );
    }

    const { pairs } = validation.data;

    // Sanitize each pair to remove potentially harmful content
    const sanitizedPairs = pairs.map(pair => ({
      pairKey: pair.pairKey || null,
      status: pair.status || "",
      shortComponent: sanitizeObject(pair.shortComponent || {}),
      longComponent: sanitizeObject(pair.longComponent || {}),
      combinedPNL: pair.combinedPNL || "0"
    }));

    console.log("Sanitized pairs sample:", sanitizedPairs.length > 0 ? {
      pairKey: sanitizedPairs[0].pairKey,
      status: sanitizedPairs[0].status,
      combinedPNL: sanitizedPairs[0].combinedPNL
    } : "No pairs");

    try {
      // Save the pairs to MongoDB using Prisma
      const result = await saveTradingPairs(sanitizedPairs, userId);

      return NextResponse.json({
        success: true,
        message: `${result.insertedCount} pairs saved successfully. Previous pairs were overwritten.`,
        result
      });
    } catch (error) {
      console.error("Error in saveTradingPairs:", error);

      // Provide a more detailed error message
      return NextResponse.json(
        {
          error: "Failed to save pairs",
          details: error.message,
          code: error.code || "UNKNOWN_ERROR"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error saving pairs:", error);
    return NextResponse.json(
      { error: "Failed to save pairs", details: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user from the database
    const user = await getUserByEmail(session.user.email);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Use user.id which is already set in getUserByEmail
    const userId = user.id;
    console.log("User ID for fetching pairs:", userId);

    // Get the status from the query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");

    // Validate status if provided
    if (status && !['WB_LoadedPairs', 'WB_OpenPositions', 'WB_ClosedPositions'].includes(status)) {
      return NextResponse.json(
        {
          error: "Invalid status parameter",
          message: "Status must be either 'WB_LoadedPairs' or 'WB_OpenPositions' or 'WB_ClosedPositions'"
        },
        { status: 400 }
      );
    }

    // Get the pairs from MongoDB using Prisma
    const pairs = await getTradingPairs(userId, status);

    return NextResponse.json({ pairs });
  } catch (error) {
    console.error("Error fetching pairs:", error);
    return NextResponse.json(
      { error: "Failed to fetch pairs", details: error.message },
      { status: 500 }
    );
  }
}
