"use client";

import { createContext, useState } from "react";

export const AppContext = createContext();

export const AppProvider = ({ children }) => {
  const [data, setData] = useState({
    /* Your initial app-wide data */

    StrategyConfig: {
      name: "WB",
      description: "This is the configuration for the WB strategy",
      pairCandidates: [
        {
          ID: "P1",
          Strategy: "WB",
          PairID: "WB-P1-T1",
          Symbol: "KIM",
          Side: "long",
          Quantity: 100,
          ExDivDate: "2023-01-01",
          Spread: 0.01,
        },
        {
          ID: "P2",
          Strategy: "WB",
          PairID: "WB-P2-T1",
          Symbol: "AAPL",
          Side: "long",
          Quantity: 100,
          ExDivDate: "2023-01-01",
          Spread: 0.01,
        },
      ],
      settings: {
        setting1: "value1",
        setting2: "value2",
      },
    },

    PLTracking: { plTracking: ["pl1", "pl2"] },
    EventsLog: { eventsLog: ["event1", "event2"] },
    Test: ["test1", "test2"],
  });

  return (
    <AppContext.Provider value={{ data, setData }}>
      {children}
    </AppContext.Provider>
  );
};
