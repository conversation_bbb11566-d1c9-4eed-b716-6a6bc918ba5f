import axios from "axios";
import { retrieveAccessToken } from "./schwabAccess";

const TraderAPIbaseURL = "https://api.schwabapi.com/trader/v1";


//ACCOUNTS

export async function getAccountNumbers() {
  const accessToken = await retrieveAccessToken();
  const endpoint = "/accounts/accountNumbers";
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get list of account numbers and their encrypted values`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getLinkedAccounts() {
  const accessToken = await retrieveAccessToken();
  const endpoint ="/accounts?fields=positions";
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get linked account(s) balances and positions for the logged in user.`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getAccount(accountNumber) {
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}`;
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get a specific account balance and positions for the logged in user.`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//ORDERS

export async function getOrders(accountNumber) {
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/orders`;
  const url = `${TraderAPIbaseURL + endpoint}?fromEnteredTime=${encodeURIComponent(fromEnteredTime)}&toEnteredTime=${encodeURIComponent(toEnteredTime)}`
  console.log(
    `GET ${
      url
    } - Get all orders for a specific account.`
  );
  const response = await axios.get(url, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function placeOrder(accountNumber, order) { //test it, it requires the "request body"
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/orders`;
  console.log(
    `POST ${TraderAPIbaseURL + endpoint} - Place order for a specific account.`
  );
  const response = await axios.post(TraderAPIbaseURL + endpoint, order, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getOrder(accountNumber, orderId) {
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get a specific order by its ID, for a specific account`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function cancelOrder(accountNumber, orderId) {
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;
  console.log(
    `DELETE ${
      TraderAPIbaseURL + endpoint
    } - Cancel an order for a specific account`
  );
  const response = await axios.delete(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function replaceOrder(accountNumber, orderId, order) { //test it, it requires the "request body"
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;
  console.log(
    `PUT ${TraderAPIbaseURL + endpoint} - Replace order for a specific account`
  );
  const response = await axios.put(TraderAPIbaseURL + endpoint, order, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getAllOrders(fromEnteredTime, toEnteredTime) {
  const accessToken = await retrieveAccessToken();
  const endpoint = "/orders";
  const url = `${TraderAPIbaseURL + endpoint}?fromEnteredTime=${encodeURIComponent(fromEnteredTime)}&toEnteredTime=${encodeURIComponent(toEnteredTime)}`
  const response = await axios.get(url, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function previewOrder(accountNumber, order) { //test it, it requires the "request body"
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/previewOrder`;
  console.log(
    `POST ${
      TraderAPIbaseURL + endpoint
    } - Preview order for a specific account. **Coming Soon**.`
  );
  const response = await axios.post(TraderAPIbaseURL + endpoint, order, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//TRANSACTIONS

export async function getTransactions(accountNumber, startDate, endDate, types) { //here we need to add the date range(startDate, endDate, types parameters)
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/transactions?startDate=${startDate}&endDate=${endDate}&types=${types}`; //encrypted id is required
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get all transactions information for a specific account.`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getTransaction(
  accountNumber,
  transactionId,
) {
  const accessToken = await retrieveAccessToken();
  const endpoint = `/accounts/${accountNumber}/transactions/${transactionId}`;
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get specific transaction information for a specific account`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//USER PREFERENCE

export async function getUserPreference() {
  const accessToken = await retrieveAccessToken();
  const endpoint = "/userPreference";
  console.log(
    `GET ${
      TraderAPIbaseURL + endpoint
    } - Get user preference information for the logged in user.`
  );
  const response = await axios.get(TraderAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}
