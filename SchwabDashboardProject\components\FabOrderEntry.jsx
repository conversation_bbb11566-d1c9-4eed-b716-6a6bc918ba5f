"use client";
import { PlusIcon } from "@heroicons/react/24/solid";
import OrderEntryDialog from "./OrderEntryDialog";
import ProtectedRoute from "./ProtectedRoute";
import { useSession } from "next-auth/react";

export default function FabOrderEntry() {
  const { status } = useSession();
  if (status !== "authenticated") return null;

  return (
    <ProtectedRoute>
      <div className="fixed top-3 right-3 z-50">
        <OrderEntryDialog>
          <button
            className="flex items-center gap-2 px-5 py-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg font-bold text-lg transition-all focus:outline-none focus:ring-2 focus:ring-blue-400"
            aria-label="Order Entry"
          >
            <PlusIcon className="h-6 w-6" />
            Order Entry
          </button>
        </OrderEntryDialog>
      </div>
    </ProtectedRoute>
  );
}
