/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/get-saved-symbols/route";
exports.ids = ["app/api/get-saved-symbols/route"];
exports.modules = {

/***/ "(rsc)/./actions/schwabAccess.js":
/*!*********************************!*\
  !*** ./actions/schwabAccess.js ***!
  \*********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getAccounts: () => (/* binding */ getAccounts),\n/* harmony export */   getAuthorizationCodeCallbackHandler: () => (/* binding */ getAuthorizationCodeCallbackHandler),\n/* harmony export */   getAuthorizationCodeURL: () => (/* binding */ getAuthorizationCodeURL),\n/* harmony export */   getMarketData: () => (/* binding */ getMarketData),\n/* harmony export */   makeApiCall: () => (/* binding */ makeApiCall),\n/* harmony export */   refreshToken: () => (/* binding */ refreshToken),\n/* harmony export */   retrieveAccessToken: () => (/* binding */ retrieveAccessToken),\n/* harmony export */   retrieveAuthorizationCode: () => (/* binding */ retrieveAuthorizationCode),\n/* harmony export */   retrieveCorrelId: () => (/* binding */ retrieveCorrelId),\n/* harmony export */   retrieveCustomerId: () => (/* binding */ retrieveCustomerId),\n/* harmony export */   retrieveRefreshToken: () => (/* binding */ retrieveRefreshToken),\n/* harmony export */   storeAccessToken: () => (/* binding */ storeAccessToken),\n/* harmony export */   storeAccessTokenFirstLogin: () => (/* binding */ storeAccessTokenFirstLogin),\n/* harmony export */   storeAuthorizationCode: () => (/* binding */ storeAuthorizationCode),\n/* harmony export */   storeCorrelId: () => (/* binding */ storeCorrelId),\n/* harmony export */   storeCustomerId: () => (/* binding */ storeCustomerId),\n/* harmony export */   storeRefreshToken: () => (/* binding */ storeRefreshToken)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _schwabTraderAPIactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./schwabTraderAPIactions */ \"(rsc)/./actions/schwabTraderAPIactions.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00123b7f590fca2fea846ce8bb090c8b4135e9de5d\":\"getAccessToken\",\"00610d6e30a540305699b3726edfa456794a935d4e\":\"retrieveAuthorizationCode\",\"00972f29e10d6cd18dd1265b60067912bb6a7a1c2b\":\"getAuthorizationCodeURL\",\"00a9e585e1dc35eb977066b761d49f1a23fdc16c93\":\"storeCustomerId\",\"00bf6df4de4448c4552c6f62dbf4ba66009dcad250\":\"storeCorrelId\",\"00eda339ddc9f1e862059f7d997de8e23129bed7ef\":\"refreshToken\",\"4019d9b6d929f38fb47a1d4a69423a6d858847b0f3\":\"storeRefreshToken\",\"404f3e3ad78ac2de2901424a14b5c7fe022ce0b303\":\"storeAccessTokenFirstLogin\",\"4075df18647696b0765b2b5d66cadca4e16796a875\":\"retrieveRefreshToken\",\"409be9f1ad10c10b018021cd0e5c43e04f6a52ec55\":\"storeAccessToken\",\"40c21480db71c85b8af6ed1b0041f5c14845668e80\":\"retrieveAccessToken\",\"40d7abcf2a47f382acdd16645f2339ee46da14ba01\":\"retrieveCorrelId\",\"40f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb\":\"retrieveCustomerId\",\"40fb93412cb8910c389b9bef1a01cf17e6bb15379b\":\"storeAuthorizationCode\",\"60325ece34f85a6ed2aff537378b9ed92812b4c97b\":\"getAuthorizationCodeCallbackHandler\",\"78feca9946baeb980c93461938e15da67fb1f1530b\":\"makeApiCall\",\"7f276eddd716d85ac6647acaf92f150beea2a1298d\":\"getMarketData\",\"7f8d47bfa3f1bd78bb4909dc7270562779ecb727b3\":\"getAccounts\"} */ \n\n\n\n\nconst clientId = \"kjWwcw6OlltgtuYXrd27YpAcGGN7VImN\";\nconst clientSecret = \"6PeG9sko8DNuYFh0\";\nconst callbackUrl = \"https://127.0.0.1:3000/oauth/schwab\";\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\nasync function getAuthorizationCodeURL() {\n    try {\n        // Generate a random state parameter for CSRF protection\n        // We'll skip this for now since we're having issues with it\n        // const state = crypto.randomBytes(32).toString('hex');\n        // // Store the state in a cookie for verification when the user returns\n        // const cookiesObj = await cookies();\n        // cookiesObj.set(\"schwab_oauth_state\", state, {\n        //   httpOnly: true,\n        //   secure: process.env.NODE_ENV === \"production\",\n        //   sameSite: \"lax\",\n        //   maxAge: 60 * 10, // 10 minutes\n        //   path: \"/\",\n        // });\n        // Construct the authorization URL without the state parameter for now\n        const authorizationCodeUrl = `https://api.schwabapi.com/v1/oauth/authorize?client_id=${clientId}&redirect_uri=${callbackUrl}`;\n        console.log(\"Generated authorization URL\");\n        return authorizationCodeUrl;\n    } catch (error) {\n        console.error(\"Error generating authorization URL:\", error);\n        throw error;\n    }\n}\nasync function getAuthorizationCodeCallbackHandler(code, state) {\n    try {\n        console.log(\"Processing OAuth callback with code\");\n        if (!code) {\n            console.error(\"No authorization code provided\");\n            return {\n                success: false,\n                error: \"No authorization code provided\"\n            };\n        }\n        // Only verify state if it's not the bypass value\n        if (state !== \"bypass_state_check\") {\n            if (!state) {\n                console.error(\"No state parameter provided\");\n                return {\n                    success: false,\n                    error: \"No state parameter provided\"\n                };\n            }\n            // Verify the state parameter to prevent CSRF attacks\n            const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n            const storedState = cookiesObj.get(\"schwab_oauth_state\")?.value;\n            if (!storedState || state !== storedState) {\n                console.error(\"Invalid state parameter\");\n                return {\n                    success: false,\n                    error: \"Invalid state parameter\"\n                };\n            }\n            // Clear the state cookie\n            cookiesObj.delete(\"schwab_oauth_state\");\n        } else {\n            console.log(\"Bypassing state parameter check\");\n        }\n        // Store the authorization code for later use\n        const storeResult = await storeAuthorizationCode(code);\n        if (!storeResult) {\n            console.error(\"Failed to store authorization code\");\n            return {\n                success: false,\n                error: \"Failed to store authorization code\"\n            };\n        }\n        console.log(\"Authorization code stored successfully\");\n        // Get the access token\n        const tokenResult = await getAccessToken();\n        if (!tokenResult) {\n            console.error(\"Failed to get access token\");\n            return {\n                success: false,\n                error: \"Failed to get access token\"\n            };\n        }\n        console.log(\"Access token retrieved successfully\");\n        return {\n            success: true,\n            message: \"Authorization successful\",\n            hasAccessToken: !!tokenResult.access_token,\n            hasRefreshToken: !!tokenResult.refresh_token\n        };\n    } catch (error) {\n        console.error(\"Error in authorization code callback:\", error);\n        return {\n            success: false,\n            error: error.message || \"Unknown error\"\n        };\n    }\n}\nasync function getAccessToken() {\n    try {\n        console.log(\"Getting access token...\");\n        const authorizationCode = await retrieveAuthorizationCode();\n        if (!authorizationCode) {\n            console.error(\"No authorization code found in cookies\");\n            // Try to refresh the token instead of throwing an error\n            const refreshedToken = await refreshToken();\n            if (refreshedToken) {\n                console.log(\"Successfully refreshed token instead of getting a new one\");\n                return refreshedToken;\n            }\n            // If we can't refresh, return null instead of throwing\n            console.log(\"Could not refresh token either, returning null\");\n            return null;\n        }\n        // Check for client credentials\n        if (!clientId || !clientSecret) {\n            console.error(\"Missing client credentials\");\n            return null;\n        }\n        // Create the authorization header\n        const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString(\"base64\");\n        // Ensure the code is properly encoded\n        const encodedCode = encodeURIComponent(authorizationCode);\n        const encodedCallbackUrl = encodeURIComponent(callbackUrl);\n        // Set up the request\n        const accessTokenUrl = \"https://api.schwabapi.com/v1/oauth/token\";\n        const headers = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"Authorization\": `Basic ${authHeader}`\n        };\n        // Create the request body\n        const body = `grant_type=authorization_code&code=${encodedCode}&redirect_uri=${encodedCallbackUrl}`;\n        console.log(\"Making token request to:\", accessTokenUrl);\n        console.log(\"Using authorization code:\", authorizationCode.substring(0, 10) + \"...\");\n        // Make the request\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(accessTokenUrl, body, {\n            headers\n        });\n        // Process the response\n        if (response.status === 200) {\n            console.log(\"Token response status:\", response.status);\n            const accessTokenResponse = response.data;\n            console.log(\"Access token retrieved successfully\");\n            // Store the tokens\n            if (accessTokenResponse.access_token) {\n                await storeAccessToken(accessTokenResponse);\n                console.log(\"Access token stored successfully\");\n            }\n            if (accessTokenResponse.refresh_token) {\n                await storeRefreshToken(accessTokenResponse.refresh_token);\n                console.log(\"Refresh token stored successfully\");\n            }\n            return accessTokenResponse;\n        } else {\n            console.error(\"Unexpected response status:\", response.status);\n            return null;\n        }\n    } catch (error) {\n        console.error(\"Error in getAccessToken:\", error.response?.status);\n        console.error(\"Error details:\", error.response?.data);\n        // Return null instead of throwing\n        return null;\n    }\n}\n// async function getAuthorizationCode() {\n// console.log(\"Received request with method:\", req.method);\n// if (req.method === \"GET\") {\n//   const authorizationCode = req.query.code;\n//   console.log(\"Received authorization code: \" + authorizationCode);\n//   if (authorizationCode) {\n//     await storeAuthorizationCode(authorizationCode);\n//     res.redirect(\"/dashboard\");\n//   } else {\n//     res.status(400).send(\"No authorization code provided\");\n//   }\n// } else {\n//   res.status(405).send(\"Method not allowed\");\n// }\n// }\n// async function login() {\n//   try {\n//     const accessToken = await getAccessToken();\n//     // Store the access token for later use\n//     await storeAccessToken(accessTokenResponse.access_token); //valid for 30 minutes\n//     await storeRefreshToken(accessTokenResponse.refresh_token); //valid for 7 days\n//     return { accessToken };\n//   } catch (error) {\n//     console.error(error);\n//     throw error;\n//   }\n// }\nasync function refreshToken() {\n    try {\n        console.log(\"Refreshing access token...\");\n        const refreshToken = await retrieveRefreshToken(true);\n        if (!refreshToken) {\n            console.log(\"No refresh token found. User might need to log in again.\");\n            return null;\n        }\n        const accessTokenUrl = \"https://api.schwabapi.com/v1/oauth/token\";\n        // Create the Authorization header with Basic Auth\n        const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString(\"base64\");\n        const headers = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"Authorization\": `Basic ${authHeader}`\n        };\n        const body = `grant_type=refresh_token&refresh_token=${refreshToken}`;\n        const response = await fetch(accessTokenUrl, {\n            method: \"POST\",\n            headers,\n            body\n        });\n        if (!response.ok) {\n            const errorResponse = await response.json();\n            console.error(`Error refreshing token: ${response.status} - ${errorResponse.error_description || JSON.stringify(errorResponse)}`);\n            return null;\n        }\n        const newAccessTokenResponse = await response.json();\n        console.log(\"New access token retrieved:\", newAccessTokenResponse.access_token);\n        // Store the new access token\n        await storeAccessToken(newAccessTokenResponse);\n        // Store the new refresh token (only if present)\n        if (newAccessTokenResponse.refresh_token) {\n            await storeRefreshToken(newAccessTokenResponse.refresh_token);\n        }\n        return newAccessTokenResponse;\n    } catch (error) {\n        console.error(\"Error in refreshToken:\", error.message);\n        return null;\n    }\n}\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n// Helper functions to store and retrieve authorization code and access token\nasync function storeAuthorizationCode(code) {\n    try {\n        if (!code) {\n            console.error(\"No authorization code provided\");\n            return false;\n        }\n        // Clean the code if it contains URL-encoded characters\n        const cleanCode = code.replace(/%40/g, '@');\n        console.log(`Storing authorization code: ${cleanCode.substring(0, 10)}...`);\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        // Set the cookie with a longer expiration (2 hours) to ensure we have time to use it\n        cookiesObj.set(\"authorization_code\", cleanCode, {\n            maxAge: 7200,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        // Verify the code was stored\n        const storedCode = cookiesObj.get(\"authorization_code\")?.value;\n        if (storedCode) {\n            console.log(`Authorization code stored successfully: ${storedCode.substring(0, 10)}...`);\n            return true;\n        } else {\n            console.error(\"Failed to store authorization code in cookies\");\n            return false;\n        }\n    } catch (error) {\n        console.error(\"Error storing authorization code:\", error);\n        return false;\n    }\n}\nasync function retrieveAuthorizationCode() {\n    try {\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const authcode = cookiesObj.get(\"authorization_code\")?.value;\n        if (!authcode) {\n            console.log(\"No authorization code found in cookies\");\n            return null;\n        }\n        console.log(`Retrieved authorization code from cookies: ${authcode.substring(0, 10)}...`);\n        return authcode;\n    } catch (error) {\n        console.error(\"Error in retrieveAuthorizationCode:\", error);\n        return null;\n    }\n}\nasync function storeAccessToken(token) {\n    try {\n        if (!token || !token.access_token) {\n            console.error(\"No access token provided or invalid token format\");\n            return false;\n        }\n        console.log(\"Storing access token:\", token.access_token.substring(0, 10) + \"...\");\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        cookiesObj.set(\"access_token\", token.access_token, {\n            maxAge: 1800,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error storing access token:\", error);\n        return false;\n    }\n}\n///////////////////////////////////////////////////////////////////////////////////////\n//this is a duplicate code, until we find the solution for the accessToken problem in the AccessToken function\nasync function storeAccessTokenFirstLogin(token) {\n    try {\n        if (!token) {\n            console.error(\"No access token provided\");\n            return false;\n        }\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        cookiesObj.set(\"access_token\", token, {\n            maxAge: 1800,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error storing first login access token:\", error);\n        return false;\n    }\n}\n///////////////////////////////////////////////////////////////////////////////////////\nasync function retrieveAccessToken(skipRefresh = false) {\n    try {\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        let accessToken = cookiesObj.get(\"access_token\")?.value;\n        // If we have an access token or we're skipping refresh, return it (or null)\n        if (accessToken || skipRefresh) {\n            return accessToken || null;\n        }\n        // Only attempt to refresh if we don't have a token and we're not skipping refresh\n        console.log(\"No access token found, attempting to refresh\");\n        try {\n            // Access token is not found, try to refresh\n            const refreshResult = await refreshToken();\n            if (refreshResult) {\n                // Try to get the access token again after refresh\n                const newCookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n                accessToken = newCookiesObj.get(\"access_token\")?.value;\n                if (accessToken) {\n                    console.log(\"Successfully refreshed and retrieved access token\");\n                } else {\n                    console.log(\"Refresh succeeded but no access token found\");\n                }\n            } else {\n                console.log(\"Token refresh failed\");\n            }\n        } catch (error) {\n            console.error(\"Error refreshing token:\", error.message);\n        }\n        return accessToken || null;\n    } catch (error) {\n        console.error(\"Error in retrieveAccessToken:\", error);\n        return null;\n    }\n}\nasync function storeRefreshToken(token) {\n    try {\n        if (!token) {\n            console.error(\"No refresh token provided\");\n            return false;\n        }\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        cookiesObj.set(\"refresh_token\", token, {\n            maxAge: 604800,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error storing refresh token:\", error);\n        return false;\n    }\n}\nasync function retrieveRefreshToken(suppressLog = false) {\n    try {\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const refreshTokenValue = cookiesObj.get(\"refresh_token\")?.value;\n        if (!refreshTokenValue && !suppressLog) {\n            console.log(\"No refresh token found\");\n        }\n        return refreshTokenValue || null;\n    } catch (error) {\n        console.error(\"Error in retrieveRefreshToken:\", error);\n        return null;\n    }\n}\nasync function storeCorrelId() {\n    try {\n        const userPreference = await (0,_schwabTraderAPIactions__WEBPACK_IMPORTED_MODULE_3__.getUserPreference)();\n        const schwabClientCorrelId = userPreference.streamerInfo?.[0]?.schwabClientCorrelId;\n        if (!schwabClientCorrelId) {\n            console.error(\"No correlation ID found in user preferences\");\n            return false;\n        }\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        cookiesObj.set(\"client_correlId\", schwabClientCorrelId, {\n            maxAge: 86400,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error storing correlation ID:\", error);\n        return false;\n    }\n}\nasync function retrieveCorrelId(suppressLog = false) {\n    try {\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const correlId = cookiesObj.get(\"client_correlId\")?.value;\n        if (!correlId && !suppressLog) {\n            console.log(\"No client correlId found\");\n        }\n        return correlId || null;\n    } catch (error) {\n        console.error(\"Error in retrieveCorrelId:\", error);\n        return null;\n    }\n}\nasync function storeCustomerId() {\n    try {\n        const userPreference = await (0,_schwabTraderAPIactions__WEBPACK_IMPORTED_MODULE_3__.getUserPreference)();\n        const schwabClientCustomerId = userPreference.streamerInfo?.[0]?.schwabClientCustomerId;\n        if (!schwabClientCustomerId) {\n            console.error(\"No customer ID found in user preferences\");\n            return false;\n        }\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        cookiesObj.set(\"client_customerId\", schwabClientCustomerId, {\n            maxAge: 86400,\n            path: '/',\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: 'strict'\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error storing customer ID:\", error);\n        return false;\n    }\n}\nasync function retrieveCustomerId(suppressLog = false) {\n    try {\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const customerId = cookiesObj.get(\"client_customerId\")?.value;\n        if (!customerId && !suppressLog) {\n            console.log(\"No client customerId found\");\n        }\n        return customerId || null;\n    } catch (error) {\n        console.error(\"Error in retrieveCustomerId:\", error);\n        return null;\n    }\n}\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n//EXAMPLE API CALL WITH TOKEN - Generic helper function\nasync function makeApiCall(endpoint, method = \"GET\", body = null, skipRefresh = false) {\n    try {\n        const accessToken = await retrieveAccessToken(skipRefresh); // Retrieve the stored access token\n        if (!accessToken) {\n            console.log(\"No access token available for API call\");\n            return {\n                error: \"Authentication required\"\n            };\n        }\n        const headers = {\n            \"Authorization\": `Bearer ${accessToken}`,\n            \"Content-Type\": \"application/json\"\n        };\n        const options = {\n            method,\n            headers\n        };\n        if (body && (method === \"POST\" || method === \"PUT\" || method === \"PATCH\")) {\n            options.body = JSON.stringify(body);\n        }\n        const response = await fetch(endpoint, options);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`API call failed: ${response.status} ${response.statusText}`, errorText);\n            return {\n                error: \"API call failed\",\n                status: response.status,\n                statusText: response.statusText,\n                details: errorText\n            };\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error in makeApiCall:\", error);\n        return {\n            error: error.message || \"Unknown error\"\n        };\n    }\n}\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\nconst getAccounts = async (skipRefresh = false)=>{\n    try {\n        let accessToken = await retrieveAccessToken(skipRefresh);\n        console.log(\"Using access token for accounts API call:\", accessToken ? \"Yes\" : \"No\");\n        if (!accessToken) {\n            return {\n                error: \"Not connected to Schwab\",\n                message: \"Please connect your Schwab account first\",\n                needsConnection: true\n            };\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://api.schwabapi.com/trader/v1/accounts\", {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        return response.data;\n    } catch (error) {\n        console.error(\"Error fetching accounts:\", error);\n        // Check if it's an authentication error\n        if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n            return {\n                error: \"Authentication failed\",\n                message: \"Your Schwab connection has expired. Please reconnect.\",\n                needsConnection: true\n            };\n        }\n        return {\n            error: \"Failed to fetch accounts\",\n            message: error.message || \"An unknown error occurred\",\n            needsConnection: false\n        };\n    }\n};\nconst getMarketData = async (symbol, skipRefresh = false)=>{\n    try {\n        let accessToken = await retrieveAccessToken(skipRefresh);\n        console.log(\"Using access token for market data API call:\", accessToken ? \"Yes\" : \"No\");\n        if (!accessToken) {\n            return {\n                error: \"Not connected to Schwab\",\n                message: \"Please connect your Schwab account first\",\n                needsConnection: true\n            };\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(`https://api.schwabapi.com/v1/markets/data/${symbol}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        return response.data;\n    } catch (error) {\n        console.error(\"Error fetching market data:\", error);\n        // Check if it's an authentication error\n        if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n            return {\n                error: \"Authentication failed\",\n                message: \"Your Schwab connection has expired. Please reconnect.\",\n                needsConnection: true\n            };\n        }\n        return {\n            error: \"Failed to fetch market data\",\n            message: error.message || \"An unknown error occurred\",\n            needsConnection: false\n        };\n    }\n}; // export {\n //   getAccounts,\n //   getMarketData,\n //   getAuthorizationCodeURL,\n //   getAuthorizationCodeCallbackHandler,\n //   getAccessToken,\n //   storeAuthorizationCode,\n //   retrieveAuthorizationCode,\n //   storeAccessToken,\n //   retrieveAccessToken,\n //   storeRefreshToken,\n //   retrieveRefreshToken,\n //   refreshToken,\n // };\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_5__.ensureServerEntryExports)([\n    getAuthorizationCodeURL,\n    getAuthorizationCodeCallbackHandler,\n    getAccessToken,\n    refreshToken,\n    storeAuthorizationCode,\n    retrieveAuthorizationCode,\n    storeAccessToken,\n    storeAccessTokenFirstLogin,\n    retrieveAccessToken,\n    storeRefreshToken,\n    retrieveRefreshToken,\n    storeCorrelId,\n    retrieveCorrelId,\n    storeCustomerId,\n    retrieveCustomerId,\n    makeApiCall,\n    getAccounts,\n    getMarketData\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getAuthorizationCodeURL, \"00972f29e10d6cd18dd1265b60067912bb6a7a1c2b\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getAuthorizationCodeCallbackHandler, \"60325ece34f85a6ed2aff537378b9ed92812b4c97b\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getAccessToken, \"00123b7f590fca2fea846ce8bb090c8b4135e9de5d\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(refreshToken, \"00eda339ddc9f1e862059f7d997de8e23129bed7ef\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeAuthorizationCode, \"40fb93412cb8910c389b9bef1a01cf17e6bb15379b\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(retrieveAuthorizationCode, \"00610d6e30a540305699b3726edfa456794a935d4e\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeAccessToken, \"409be9f1ad10c10b018021cd0e5c43e04f6a52ec55\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeAccessTokenFirstLogin, \"404f3e3ad78ac2de2901424a14b5c7fe022ce0b303\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(retrieveAccessToken, \"40c21480db71c85b8af6ed1b0041f5c14845668e80\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeRefreshToken, \"4019d9b6d929f38fb47a1d4a69423a6d858847b0f3\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(retrieveRefreshToken, \"4075df18647696b0765b2b5d66cadca4e16796a875\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeCorrelId, \"00bf6df4de4448c4552c6f62dbf4ba66009dcad250\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(retrieveCorrelId, \"40d7abcf2a47f382acdd16645f2339ee46da14ba01\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(storeCustomerId, \"00a9e585e1dc35eb977066b761d49f1a23fdc16c93\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(retrieveCustomerId, \"40f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(makeApiCall, \"78feca9946baeb980c93461938e15da67fb1f1530b\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getAccounts, \"7f8d47bfa3f1bd78bb4909dc7270562779ecb727b3\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getMarketData, \"7f276eddd716d85ac6647acaf92f150beea2a1298d\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hY3Rpb25zL3NjaHdhYkFjY2Vzcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUwQjtBQUNhO0FBQ3NCO0FBRzdELE1BQU1HLFdBQVdDLGtDQUFzQztBQUN2RCxNQUFNRyxlQUFlSCxrQkFBcUM7QUFDMUQsTUFBTUssY0FBY0wscUNBQTBDO0FBRTlELG9JQUFvSTtBQUNwSSxvSUFBb0k7QUFDcEksb0lBQW9JO0FBRTdILGVBQWVPO0lBQ3BCLElBQUk7UUFDRix3REFBd0Q7UUFDeEQsNERBQTREO1FBQzVELHdEQUF3RDtRQUV4RCx3RUFBd0U7UUFDeEUsc0NBQXNDO1FBQ3RDLGdEQUFnRDtRQUNoRCxvQkFBb0I7UUFDcEIsbURBQW1EO1FBQ25ELHFCQUFxQjtRQUNyQixtQ0FBbUM7UUFDbkMsZUFBZTtRQUNmLE1BQU07UUFFTixzRUFBc0U7UUFDdEUsTUFBTUMsdUJBQXVCLENBQUMsdURBQXVELEVBQUVULFNBQVMsY0FBYyxFQUFFTSxhQUFhO1FBRTdISSxRQUFRQyxHQUFHLENBQUM7UUFDWixPQUFPRjtJQUNULEVBQUUsT0FBT0csT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxNQUFNQTtJQUNSO0FBQ0Y7QUFFTyxlQUFlQyxvQ0FBb0NDLElBQUksRUFBRUMsS0FBSztJQUNuRSxJQUFJO1FBQ0ZMLFFBQVFDLEdBQUcsQ0FBQztRQUVaLElBQUksQ0FBQ0csTUFBTTtZQUNUSixRQUFRRSxLQUFLLENBQUM7WUFDZCxPQUFPO2dCQUFFSSxTQUFTO2dCQUFPSixPQUFPO1lBQWlDO1FBQ25FO1FBRUEsaURBQWlEO1FBQ2pELElBQUlHLFVBQVUsc0JBQXNCO1lBQ2xDLElBQUksQ0FBQ0EsT0FBTztnQkFDVkwsUUFBUUUsS0FBSyxDQUFDO2dCQUNkLE9BQU87b0JBQUVJLFNBQVM7b0JBQU9KLE9BQU87Z0JBQThCO1lBQ2hFO1lBRUEscURBQXFEO1lBQ3JELE1BQU1LLGFBQWEsTUFBTW5CLHFEQUFPQTtZQUNoQyxNQUFNb0IsY0FBY0QsV0FBV0UsR0FBRyxDQUFDLHVCQUF1QkM7WUFFMUQsSUFBSSxDQUFDRixlQUFlSCxVQUFVRyxhQUFhO2dCQUN6Q1IsUUFBUUUsS0FBSyxDQUFDO2dCQUNkLE9BQU87b0JBQUVJLFNBQVM7b0JBQU9KLE9BQU87Z0JBQTBCO1lBQzVEO1lBRUEseUJBQXlCO1lBQ3pCSyxXQUFXSSxNQUFNLENBQUM7UUFDcEIsT0FBTztZQUNMWCxRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLDZDQUE2QztRQUM3QyxNQUFNVyxjQUFjLE1BQU1DLHVCQUF1QlQ7UUFFakQsSUFBSSxDQUFDUSxhQUFhO1lBQ2hCWixRQUFRRSxLQUFLLENBQUM7WUFDZCxPQUFPO2dCQUFFSSxTQUFTO2dCQUFPSixPQUFPO1lBQXFDO1FBQ3ZFO1FBRUFGLFFBQVFDLEdBQUcsQ0FBQztRQUVaLHVCQUF1QjtRQUN2QixNQUFNYSxjQUFjLE1BQU1DO1FBRTFCLElBQUksQ0FBQ0QsYUFBYTtZQUNoQmQsUUFBUUUsS0FBSyxDQUFDO1lBQ2QsT0FBTztnQkFBRUksU0FBUztnQkFBT0osT0FBTztZQUE2QjtRQUMvRDtRQUVBRixRQUFRQyxHQUFHLENBQUM7UUFFWixPQUFPO1lBQ0xLLFNBQVM7WUFDVFUsU0FBUztZQUNUQyxnQkFBZ0IsQ0FBQyxDQUFDSCxZQUFZSSxZQUFZO1lBQzFDQyxpQkFBaUIsQ0FBQyxDQUFDTCxZQUFZTSxhQUFhO1FBQzlDO0lBQ0YsRUFBRSxPQUFPbEIsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMseUNBQXlDQTtRQUN2RCxPQUFPO1lBQUVJLFNBQVM7WUFBT0osT0FBT0EsTUFBTWMsT0FBTyxJQUFJO1FBQWdCO0lBQ25FO0FBQ0Y7QUFFTyxlQUFlRDtJQUNwQixJQUFJO1FBQ0ZmLFFBQVFDLEdBQUcsQ0FBQztRQUNaLE1BQU1vQixvQkFBb0IsTUFBTUM7UUFFaEMsSUFBSSxDQUFDRCxtQkFBbUI7WUFDdEJyQixRQUFRRSxLQUFLLENBQUM7WUFDZCx3REFBd0Q7WUFDeEQsTUFBTXFCLGlCQUFpQixNQUFNQztZQUM3QixJQUFJRCxnQkFBZ0I7Z0JBQ2xCdkIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU9zQjtZQUNUO1lBQ0EsdURBQXVEO1lBQ3ZEdkIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNUO1FBRUEsK0JBQStCO1FBQy9CLElBQUksQ0FBQ1gsWUFBWSxDQUFDSSxjQUFjO1lBQzlCTSxRQUFRRSxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7UUFFQSxrQ0FBa0M7UUFDbEMsTUFBTXVCLGFBQWFDLE9BQU9DLElBQUksQ0FBQyxHQUFHckMsU0FBUyxDQUFDLEVBQUVJLGNBQWMsRUFBRWtDLFFBQVEsQ0FBQztRQUV2RSxzQ0FBc0M7UUFDdEMsTUFBTUMsY0FBY0MsbUJBQW1CVDtRQUN2QyxNQUFNVSxxQkFBcUJELG1CQUFtQmxDO1FBRTlDLHFCQUFxQjtRQUNyQixNQUFNb0MsaUJBQWlCO1FBQ3ZCLE1BQU1DLFVBQVU7WUFDZCxnQkFBZ0I7WUFDaEIsaUJBQWlCLENBQUMsTUFBTSxFQUFFUixZQUFZO1FBQ3hDO1FBRUEsMEJBQTBCO1FBQzFCLE1BQU1TLE9BQU8sQ0FBQyxtQ0FBbUMsRUFBRUwsWUFBWSxjQUFjLEVBQUVFLG9CQUFvQjtRQUVuRy9CLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEIrQjtRQUN4Q2hDLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJvQixrQkFBa0JjLFNBQVMsQ0FBQyxHQUFHLE1BQU07UUFFOUUsbUJBQW1CO1FBQ25CLE1BQU1DLFdBQVcsTUFBTWpELDZDQUFLQSxDQUFDa0QsSUFBSSxDQUFDTCxnQkFBZ0JFLE1BQU07WUFBRUQ7UUFBUTtRQUVsRSx1QkFBdUI7UUFDdkIsSUFBSUcsU0FBU0UsTUFBTSxLQUFLLEtBQUs7WUFDM0J0QyxRQUFRQyxHQUFHLENBQUMsMEJBQTBCbUMsU0FBU0UsTUFBTTtZQUVyRCxNQUFNQyxzQkFBc0JILFNBQVNJLElBQUk7WUFDekN4QyxRQUFRQyxHQUFHLENBQUM7WUFFWixtQkFBbUI7WUFDbkIsSUFBSXNDLG9CQUFvQnJCLFlBQVksRUFBRTtnQkFDcEMsTUFBTXVCLGlCQUFpQkY7Z0JBQ3ZCdkMsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7WUFFQSxJQUFJc0Msb0JBQW9CbkIsYUFBYSxFQUFFO2dCQUNyQyxNQUFNc0Isa0JBQWtCSCxvQkFBb0JuQixhQUFhO2dCQUN6RHBCLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1lBRUEsT0FBT3NDO1FBQ1QsT0FBTztZQUNMdkMsUUFBUUUsS0FBSyxDQUFDLCtCQUErQmtDLFNBQVNFLE1BQU07WUFDNUQsT0FBTztRQUNUO0lBQ0YsRUFBRSxPQUFPcEMsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsNEJBQTRCQSxNQUFNa0MsUUFBUSxFQUFFRTtRQUMxRHRDLFFBQVFFLEtBQUssQ0FBQyxrQkFBa0JBLE1BQU1rQyxRQUFRLEVBQUVJO1FBQ2hELGtDQUFrQztRQUNsQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLDBDQUEwQztBQUMxQyw0REFBNEQ7QUFDNUQsOEJBQThCO0FBQzlCLDhDQUE4QztBQUM5QyxzRUFBc0U7QUFDdEUsNkJBQTZCO0FBQzdCLHVEQUF1RDtBQUN2RCxrQ0FBa0M7QUFDbEMsYUFBYTtBQUNiLDhEQUE4RDtBQUM5RCxNQUFNO0FBQ04sV0FBVztBQUNYLGdEQUFnRDtBQUNoRCxJQUFJO0FBQ0osSUFBSTtBQUVKLDJCQUEyQjtBQUMzQixVQUFVO0FBQ1Ysa0RBQWtEO0FBQ2xELDhDQUE4QztBQUM5Qyx1RkFBdUY7QUFDdkYscUZBQXFGO0FBQ3JGLDhCQUE4QjtBQUM5QixzQkFBc0I7QUFDdEIsNEJBQTRCO0FBQzVCLG1CQUFtQjtBQUNuQixNQUFNO0FBQ04sSUFBSTtBQUVHLGVBQWVoQjtJQUNwQixJQUFJO1FBQ0Z4QixRQUFRQyxHQUFHLENBQUM7UUFFWixNQUFNdUIsZUFBZSxNQUFNbUIscUJBQXFCO1FBQ2hELElBQUksQ0FBQ25CLGNBQWM7WUFDakJ4QixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPO1FBQ1Q7UUFFQSxNQUFNK0IsaUJBQWlCO1FBRXZCLGtEQUFrRDtRQUNsRCxNQUFNUCxhQUFhQyxPQUFPQyxJQUFJLENBQUMsR0FBR3JDLFNBQVMsQ0FBQyxFQUFFSSxjQUFjLEVBQUVrQyxRQUFRLENBQUM7UUFDdkUsTUFBTUssVUFBVTtZQUNkLGdCQUFnQjtZQUNoQixpQkFBaUIsQ0FBQyxNQUFNLEVBQUVSLFlBQVk7UUFDeEM7UUFFQSxNQUFNUyxPQUFPLENBQUMsdUNBQXVDLEVBQUVWLGNBQWM7UUFFckUsTUFBTVksV0FBVyxNQUFNUSxNQUFNWixnQkFBZ0I7WUFDM0NhLFFBQVE7WUFDUlo7WUFDQUM7UUFDRjtRQUVBLElBQUksQ0FBQ0UsU0FBU1UsRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLGdCQUFnQixNQUFNWCxTQUFTWSxJQUFJO1lBQ3pDaEQsUUFBUUUsS0FBSyxDQUFDLENBQUMsd0JBQXdCLEVBQUVrQyxTQUFTRSxNQUFNLENBQUMsR0FBRyxFQUFFUyxjQUFjRSxpQkFBaUIsSUFBSUMsS0FBS0MsU0FBUyxDQUFDSixnQkFBZ0I7WUFDaEksT0FBTztRQUNUO1FBRUEsTUFBTUsseUJBQXlCLE1BQU1oQixTQUFTWSxJQUFJO1FBQ2xEaEQsUUFBUUMsR0FBRyxDQUFDLCtCQUErQm1ELHVCQUF1QmxDLFlBQVk7UUFFOUUsNkJBQTZCO1FBQzdCLE1BQU11QixpQkFBaUJXO1FBRXZCLGdEQUFnRDtRQUNoRCxJQUFJQSx1QkFBdUJoQyxhQUFhLEVBQUU7WUFDeEMsTUFBTXNCLGtCQUFrQlUsdUJBQXVCaEMsYUFBYTtRQUM5RDtRQUVBLE9BQU9nQztJQUNULEVBQUUsT0FBT2xELE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLDBCQUEwQkEsTUFBTWMsT0FBTztRQUNyRCxPQUFPO0lBQ1Q7QUFDRjtBQUdBLG9JQUFvSTtBQUVwSSw2RUFBNkU7QUFDdEUsZUFBZUgsdUJBQXVCVCxJQUFJO0lBQy9DLElBQUk7UUFDRixJQUFJLENBQUNBLE1BQU07WUFDVEosUUFBUUUsS0FBSyxDQUFDO1lBQ2QsT0FBTztRQUNUO1FBRUEsdURBQXVEO1FBQ3ZELE1BQU1tRCxZQUFZakQsS0FBS2tELE9BQU8sQ0FBQyxRQUFRO1FBRXZDdEQsUUFBUUMsR0FBRyxDQUFDLENBQUMsNEJBQTRCLEVBQUVvRCxVQUFVbEIsU0FBUyxDQUFDLEdBQUcsSUFBSSxHQUFHLENBQUM7UUFFMUUsTUFBTTVCLGFBQWEsTUFBTW5CLHFEQUFPQTtRQUVoQyxxRkFBcUY7UUFDckZtQixXQUFXZ0QsR0FBRyxDQUFDLHNCQUFzQkYsV0FBVztZQUM5Q0csUUFBUTtZQUNSQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsUUFBUXBFLGtCQUF5QjtZQUNqQ3FFLFVBQVU7UUFDWjtRQUVBLDZCQUE2QjtRQUM3QixNQUFNQyxhQUFhdEQsV0FBV0UsR0FBRyxDQUFDLHVCQUF1QkM7UUFFekQsSUFBSW1ELFlBQVk7WUFDZDdELFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdDQUF3QyxFQUFFNEQsV0FBVzFCLFNBQVMsQ0FBQyxHQUFHLElBQUksR0FBRyxDQUFDO1lBQ3ZGLE9BQU87UUFDVCxPQUFPO1lBQ0xuQyxRQUFRRSxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7SUFDRixFQUFFLE9BQU9BLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBTztJQUNUO0FBQ0Y7QUFFTyxlQUFlb0I7SUFDcEIsSUFBSTtRQUNGLE1BQU1mLGFBQWEsTUFBTW5CLHFEQUFPQTtRQUNoQyxNQUFNMEUsV0FBV3ZELFdBQVdFLEdBQUcsQ0FBQyx1QkFBdUJDO1FBRXZELElBQUksQ0FBQ29ELFVBQVU7WUFDYjlELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVDtRQUVBRCxRQUFRQyxHQUFHLENBQUMsQ0FBQywyQ0FBMkMsRUFBRTZELFNBQVMzQixTQUFTLENBQUMsR0FBRyxJQUFJLEdBQUcsQ0FBQztRQUN4RixPQUFPMkI7SUFDVCxFQUFFLE9BQU81RCxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3JELE9BQU87SUFDVDtBQUNGO0FBRU8sZUFBZXVDLGlCQUFpQnNCLEtBQUs7SUFDMUMsSUFBSTtRQUNGLElBQUksQ0FBQ0EsU0FBUyxDQUFDQSxNQUFNN0MsWUFBWSxFQUFFO1lBQ2pDbEIsUUFBUUUsS0FBSyxDQUFDO1lBQ2QsT0FBTztRQUNUO1FBRUFGLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUI4RCxNQUFNN0MsWUFBWSxDQUFDaUIsU0FBUyxDQUFDLEdBQUcsTUFBTTtRQUMzRSxNQUFNNUIsYUFBYSxNQUFNbkIscURBQU9BO1FBQ2hDbUIsV0FBV2dELEdBQUcsQ0FBQyxnQkFBZ0JRLE1BQU03QyxZQUFZLEVBQUU7WUFDakRzQyxRQUFRO1lBQ1JDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRcEUsa0JBQXlCO1lBQ2pDcUUsVUFBVTtRQUNaO1FBQ0EsT0FBTztJQUNULEVBQUUsT0FBTzFELE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBTztJQUNUO0FBQ0Y7QUFDQSx1RkFBdUY7QUFDdkYsOEdBQThHO0FBQ3ZHLGVBQWU4RCwyQkFBMkJELEtBQUs7SUFDcEQsSUFBSTtRQUNGLElBQUksQ0FBQ0EsT0FBTztZQUNWL0QsUUFBUUUsS0FBSyxDQUFDO1lBQ2QsT0FBTztRQUNUO1FBRUEsTUFBTUssYUFBYSxNQUFNbkIscURBQU9BO1FBQ2hDbUIsV0FBV2dELEdBQUcsQ0FBQyxnQkFBZ0JRLE9BQU87WUFDcENQLFFBQVE7WUFDUkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLFFBQVFwRSxrQkFBeUI7WUFDakNxRSxVQUFVO1FBQ1o7UUFDQSxPQUFPO0lBQ1QsRUFBRSxPQUFPMUQsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsMkNBQTJDQTtRQUN6RCxPQUFPO0lBQ1Q7QUFDRjtBQUNBLHVGQUF1RjtBQUVoRixlQUFlK0Qsb0JBQW9CQyxjQUFjLEtBQUs7SUFDM0QsSUFBSTtRQUNGLE1BQU0zRCxhQUFhLE1BQU1uQixxREFBT0E7UUFDaEMsSUFBSStFLGNBQWM1RCxXQUFXRSxHQUFHLENBQUMsaUJBQWlCQztRQUVsRCw0RUFBNEU7UUFDNUUsSUFBSXlELGVBQWVELGFBQWE7WUFDOUIsT0FBT0MsZUFBZTtRQUN4QjtRQUVBLGtGQUFrRjtRQUNsRm5FLFFBQVFDLEdBQUcsQ0FBQztRQUNaLElBQUk7WUFDRiw0Q0FBNEM7WUFDNUMsTUFBTW1FLGdCQUFnQixNQUFNNUM7WUFDNUIsSUFBSTRDLGVBQWU7Z0JBQ2pCLGtEQUFrRDtnQkFDbEQsTUFBTUMsZ0JBQWdCLE1BQU1qRixxREFBT0E7Z0JBQ25DK0UsY0FBY0UsY0FBYzVELEdBQUcsQ0FBQyxpQkFBaUJDO2dCQUVqRCxJQUFJeUQsYUFBYTtvQkFDZm5FLFFBQVFDLEdBQUcsQ0FBQztnQkFDZCxPQUFPO29CQUNMRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMRCxRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsMkJBQTJCQSxNQUFNYyxPQUFPO1FBQ3hEO1FBRUEsT0FBT21ELGVBQWU7SUFDeEIsRUFBRSxPQUFPakUsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxPQUFPO0lBQ1Q7QUFDRjtBQUVPLGVBQWV3QyxrQkFBa0JxQixLQUFLO0lBQzNDLElBQUk7UUFDRixJQUFJLENBQUNBLE9BQU87WUFDVi9ELFFBQVFFLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVDtRQUVBLE1BQU1LLGFBQWEsTUFBTW5CLHFEQUFPQTtRQUNoQ21CLFdBQVdnRCxHQUFHLENBQUMsaUJBQWlCUSxPQUFPO1lBQ3JDUCxRQUFRO1lBQ1JDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRcEUsa0JBQXlCO1lBQ2pDcUUsVUFBVTtRQUNaO1FBQ0EsT0FBTztJQUNULEVBQUUsT0FBTzFELE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNUO0FBQ0Y7QUFFTyxlQUFleUMscUJBQXFCMkIsY0FBYyxLQUFLO0lBQzVELElBQUk7UUFDRixNQUFNL0QsYUFBYSxNQUFNbkIscURBQU9BO1FBQ2hDLE1BQU1tRixvQkFBb0JoRSxXQUFXRSxHQUFHLENBQUMsa0JBQWtCQztRQUMzRCxJQUFJLENBQUM2RCxxQkFBcUIsQ0FBQ0QsYUFBYTtZQUN0Q3RFLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBQ0EsT0FBT3NFLHFCQUFxQjtJQUM5QixFQUFFLE9BQU9yRSxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2hELE9BQU87SUFDVDtBQUNGO0FBRU8sZUFBZXNFO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxpQkFBaUIsTUFBTXBGLDBFQUFpQkE7UUFDOUMsTUFBTXFGLHVCQUF1QkQsZUFBZUUsWUFBWSxFQUFFLENBQUMsRUFBRSxFQUFFRDtRQUUvRCxJQUFJLENBQUNBLHNCQUFzQjtZQUN6QjFFLFFBQVFFLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVDtRQUVBLE1BQU1LLGFBQWEsTUFBTW5CLHFEQUFPQTtRQUNoQ21CLFdBQVdnRCxHQUFHLENBQUMsbUJBQW1CbUIsc0JBQXNCO1lBQ3REbEIsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsUUFBUXBFLGtCQUF5QjtZQUNqQ3FFLFVBQVU7UUFDWjtRQUNBLE9BQU87SUFDVCxFQUFFLE9BQU8xRCxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQy9DLE9BQU87SUFDVDtBQUNGO0FBRU8sZUFBZTBFLGlCQUFpQk4sY0FBYyxLQUFLO0lBQ3hELElBQUk7UUFDRixNQUFNL0QsYUFBYSxNQUFNbkIscURBQU9BO1FBQ2hDLE1BQU15RixXQUFXdEUsV0FBV0UsR0FBRyxDQUFDLG9CQUFvQkM7UUFDcEQsSUFBSSxDQUFDbUUsWUFBWSxDQUFDUCxhQUFhO1lBQzdCdEUsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7UUFDQSxPQUFPNEUsWUFBWTtJQUNyQixFQUFFLE9BQU8zRSxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE9BQU87SUFDVDtBQUNGO0FBRU8sZUFBZTRFO0lBQ3BCLElBQUk7UUFDRixNQUFNTCxpQkFBaUIsTUFBTXBGLDBFQUFpQkE7UUFDOUMsTUFBTTBGLHlCQUF5Qk4sZUFBZUUsWUFBWSxFQUFFLENBQUMsRUFBRSxFQUFFSTtRQUVqRSxJQUFJLENBQUNBLHdCQUF3QjtZQUMzQi9FLFFBQVFFLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVDtRQUVBLE1BQU1LLGFBQWEsTUFBTW5CLHFEQUFPQTtRQUNoQ21CLFdBQVdnRCxHQUFHLENBQUMscUJBQXFCd0Isd0JBQXdCO1lBQzFEdkIsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsUUFBUXBFLGtCQUF5QjtZQUNqQ3FFLFVBQVU7UUFDWjtRQUNBLE9BQU87SUFDVCxFQUFFLE9BQU8xRCxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE9BQU87SUFDVDtBQUNGO0FBRU8sZUFBZThFLG1CQUFtQlYsY0FBYyxLQUFLO0lBQzFELElBQUk7UUFDRixNQUFNL0QsYUFBYSxNQUFNbkIscURBQU9BO1FBQ2hDLE1BQU02RixhQUFhMUUsV0FBV0UsR0FBRyxDQUFDLHNCQUFzQkM7UUFDeEQsSUFBSSxDQUFDdUUsY0FBYyxDQUFDWCxhQUFhO1lBQy9CdEUsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7UUFDQSxPQUFPZ0YsY0FBYztJQUN2QixFQUFFLE9BQU8vRSxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU87SUFDVDtBQUNGO0FBRUEsb0lBQW9JO0FBQ3BJLG9JQUFvSTtBQUVwSSx1REFBdUQ7QUFDaEQsZUFBZWdGLFlBQVlDLFFBQVEsRUFBRXRDLFNBQVMsS0FBSyxFQUFFWCxPQUFPLElBQUksRUFBRWdDLGNBQWMsS0FBSztJQUMxRixJQUFJO1FBQ0YsTUFBTUMsY0FBYyxNQUFNRixvQkFBb0JDLGNBQWMsbUNBQW1DO1FBRS9GLElBQUksQ0FBQ0MsYUFBYTtZQUNoQm5FLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87Z0JBQUVDLE9BQU87WUFBMEI7UUFDNUM7UUFFQSxNQUFNK0IsVUFBVTtZQUNkLGlCQUFpQixDQUFDLE9BQU8sRUFBRWtDLGFBQWE7WUFDeEMsZ0JBQWdCO1FBQ2xCO1FBRUEsTUFBTWlCLFVBQVU7WUFDZHZDO1lBQ0FaO1FBQ0Y7UUFFQSxJQUFJQyxRQUFTVyxDQUFBQSxXQUFXLFVBQVVBLFdBQVcsU0FBU0EsV0FBVyxPQUFNLEdBQUk7WUFDekV1QyxRQUFRbEQsSUFBSSxHQUFHZ0IsS0FBS0MsU0FBUyxDQUFDakI7UUFDaEM7UUFFQSxNQUFNRSxXQUFXLE1BQU1RLE1BQU11QyxVQUFVQztRQUV2QyxJQUFJLENBQUNoRCxTQUFTVSxFQUFFLEVBQUU7WUFDaEIsTUFBTXVDLFlBQVksTUFBTWpELFNBQVNrRCxJQUFJO1lBQ3JDdEYsUUFBUUUsS0FBSyxDQUFDLENBQUMsaUJBQWlCLEVBQUVrQyxTQUFTRSxNQUFNLENBQUMsQ0FBQyxFQUFFRixTQUFTbUQsVUFBVSxFQUFFLEVBQUVGO1lBQzVFLE9BQU87Z0JBQ0xuRixPQUFPO2dCQUNQb0MsUUFBUUYsU0FBU0UsTUFBTTtnQkFDdkJpRCxZQUFZbkQsU0FBU21ELFVBQVU7Z0JBQy9CQyxTQUFTSDtZQUNYO1FBQ0Y7UUFFQSxPQUFPLE1BQU1qRCxTQUFTWSxJQUFJO0lBQzVCLEVBQUUsT0FBTzlDLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBTztZQUFFQSxPQUFPQSxNQUFNYyxPQUFPLElBQUk7UUFBZ0I7SUFDbkQ7QUFDRjtBQUVBLG9JQUFvSTtBQUU3SCxNQUFNeUUsY0FBYyxPQUFPdkIsY0FBYyxLQUFLO0lBQ25ELElBQUk7UUFDRixJQUFJQyxjQUFjLE1BQU1GLG9CQUFvQkM7UUFDNUNsRSxRQUFRQyxHQUFHLENBQUMsNkNBQTZDa0UsY0FBYyxRQUFRO1FBRS9FLElBQUksQ0FBQ0EsYUFBYTtZQUNoQixPQUFPO2dCQUNMakUsT0FBTztnQkFDUGMsU0FBUztnQkFDVDBFLGlCQUFpQjtZQUNuQjtRQUNGO1FBRUEsTUFBTXRELFdBQVcsTUFBTWpELDZDQUFLQSxDQUFDc0IsR0FBRyxDQUM5QixnREFDQTtZQUNFd0IsU0FBUztnQkFDUDBELGVBQWUsQ0FBQyxPQUFPLEVBQUV4QixhQUFhO1lBQ3hDO1FBQ0Y7UUFFRixPQUFPL0IsU0FBU0ksSUFBSTtJQUN0QixFQUFFLE9BQU90QyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyw0QkFBNEJBO1FBRTFDLHdDQUF3QztRQUN4QyxJQUFJQSxNQUFNa0MsUUFBUSxJQUFLbEMsQ0FBQUEsTUFBTWtDLFFBQVEsQ0FBQ0UsTUFBTSxLQUFLLE9BQU9wQyxNQUFNa0MsUUFBUSxDQUFDRSxNQUFNLEtBQUssR0FBRSxHQUFJO1lBQ3RGLE9BQU87Z0JBQ0xwQyxPQUFPO2dCQUNQYyxTQUFTO2dCQUNUMEUsaUJBQWlCO1lBQ25CO1FBQ0Y7UUFFQSxPQUFPO1lBQ0x4RixPQUFPO1lBQ1BjLFNBQVNkLE1BQU1jLE9BQU8sSUFBSTtZQUMxQjBFLGlCQUFpQjtRQUNuQjtJQUNGO0FBQ0YsRUFBRTtBQUVLLE1BQU1FLGdCQUFnQixPQUFPQyxRQUFRM0IsY0FBYyxLQUFLO0lBQzdELElBQUk7UUFDRixJQUFJQyxjQUFjLE1BQU1GLG9CQUFvQkM7UUFDNUNsRSxRQUFRQyxHQUFHLENBQUMsZ0RBQWdEa0UsY0FBYyxRQUFRO1FBRWxGLElBQUksQ0FBQ0EsYUFBYTtZQUNoQixPQUFPO2dCQUNMakUsT0FBTztnQkFDUGMsU0FBUztnQkFDVDBFLGlCQUFpQjtZQUNuQjtRQUNGO1FBRUEsTUFBTXRELFdBQVcsTUFBTWpELDZDQUFLQSxDQUFDc0IsR0FBRyxDQUM5QixDQUFDLDBDQUEwQyxFQUFFb0YsUUFBUSxFQUNyRDtZQUNFNUQsU0FBUztnQkFDUDBELGVBQWUsQ0FBQyxPQUFPLEVBQUV4QixhQUFhO1lBQ3hDO1FBQ0Y7UUFFRixPQUFPL0IsU0FBU0ksSUFBSTtJQUN0QixFQUFFLE9BQU90QyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQywrQkFBK0JBO1FBRTdDLHdDQUF3QztRQUN4QyxJQUFJQSxNQUFNa0MsUUFBUSxJQUFLbEMsQ0FBQUEsTUFBTWtDLFFBQVEsQ0FBQ0UsTUFBTSxLQUFLLE9BQU9wQyxNQUFNa0MsUUFBUSxDQUFDRSxNQUFNLEtBQUssR0FBRSxHQUFJO1lBQ3RGLE9BQU87Z0JBQ0xwQyxPQUFPO2dCQUNQYyxTQUFTO2dCQUNUMEUsaUJBQWlCO1lBQ25CO1FBQ0Y7UUFFQSxPQUFPO1lBQ0x4RixPQUFPO1lBQ1BjLFNBQVNkLE1BQU1jLE9BQU8sSUFBSTtZQUMxQjBFLGlCQUFpQjtRQUNuQjtJQUNGO0FBQ0YsRUFBRSxDQUVGLFdBQVc7Q0FDWCxpQkFBaUI7Q0FDakIsbUJBQW1CO0NBQ25CLDZCQUE2QjtDQUM3Qix5Q0FBeUM7Q0FDekMsb0JBQW9CO0NBQ3BCLDRCQUE0QjtDQUM1QiwrQkFBK0I7Q0FDL0Isc0JBQXNCO0NBQ3RCLHlCQUF5QjtDQUN6Qix1QkFBdUI7Q0FDdkIsMEJBQTBCO0NBQzFCLGtCQUFrQjtDQUNsQixLQUFLOzs7SUEvb0JpQjVGO0lBMkJBSztJQStEQVk7SUEyR0FTO0lBdURBWDtJQXVDQVM7SUFrQkFtQjtJQXdCQXVCO0lBdUJBQztJQXVDQXZCO0lBc0JBQztJQWNBNkI7SUF5QkFJO0lBY0FFO0lBeUJBRTtJQWtCQUU7SUE2Q1RPO0lBMENBRzs7QUF4bEJTOUYsMEZBQUFBLENBQUFBO0FBMkJBSywwRkFBQUEsQ0FBQUE7QUErREFZLDBGQUFBQSxDQUFBQTtBQTJHQVMsMEZBQUFBLENBQUFBO0FBdURBWCwwRkFBQUEsQ0FBQUE7QUF1Q0FTLDBGQUFBQSxDQUFBQTtBQWtCQW1CLDBGQUFBQSxDQUFBQTtBQXdCQXVCLDBGQUFBQSxDQUFBQTtBQXVCQUMsMEZBQUFBLENBQUFBO0FBdUNBdkIsMEZBQUFBLENBQUFBO0FBc0JBQywwRkFBQUEsQ0FBQUE7QUFjQTZCLDBGQUFBQSxDQUFBQTtBQXlCQUksMEZBQUFBLENBQUFBO0FBY0FFLDBGQUFBQSxDQUFBQTtBQXlCQUUsMEZBQUFBLENBQUFBO0FBa0JBRSwwRkFBQUEsQ0FBQUE7QUE2Q1RPLDBGQUFBQSxDQUFBQTtBQTBDQUcsMEZBQUFBLENBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVsbGVuXFxPbmVEcml2ZVxcRGVza3RvcFxcRGFzaGJvYXJkXFxTY2h3YWJEYXNoYm9hcmRQcm9qZWN0XFxhY3Rpb25zXFxzY2h3YWJBY2Nlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc2VydmVyXCI7XHJcblxyXG5pbXBvcnQgYXhpb3MgZnJvbSBcImF4aW9zXCI7XHJcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tIFwibmV4dC9oZWFkZXJzXCI7XHJcbmltcG9ydCB7IGdldFVzZXJQcmVmZXJlbmNlIH0gZnJvbSBcIi4vc2Nod2FiVHJhZGVyQVBJYWN0aW9uc1wiO1xyXG5cclxuXHJcbmNvbnN0IGNsaWVudElkID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0NIV0FCX0FQUF9LRVk7XHJcbmNvbnN0IGNsaWVudFNlY3JldCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NDSFdBQl9TRUNSRVQ7XHJcbmNvbnN0IGNhbGxiYWNrVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0NIV0FCX0NBTExCQUNLVVJMO1xyXG5cclxuLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBdXRob3JpemF0aW9uQ29kZVVSTCgpIHtcclxuICB0cnkge1xyXG4gICAgLy8gR2VuZXJhdGUgYSByYW5kb20gc3RhdGUgcGFyYW1ldGVyIGZvciBDU1JGIHByb3RlY3Rpb25cclxuICAgIC8vIFdlJ2xsIHNraXAgdGhpcyBmb3Igbm93IHNpbmNlIHdlJ3JlIGhhdmluZyBpc3N1ZXMgd2l0aCBpdFxyXG4gICAgLy8gY29uc3Qgc3RhdGUgPSBjcnlwdG8ucmFuZG9tQnl0ZXMoMzIpLnRvU3RyaW5nKCdoZXgnKTtcclxuXHJcbiAgICAvLyAvLyBTdG9yZSB0aGUgc3RhdGUgaW4gYSBjb29raWUgZm9yIHZlcmlmaWNhdGlvbiB3aGVuIHRoZSB1c2VyIHJldHVybnNcclxuICAgIC8vIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICAvLyBjb29raWVzT2JqLnNldChcInNjaHdhYl9vYXV0aF9zdGF0ZVwiLCBzdGF0ZSwge1xyXG4gICAgLy8gICBodHRwT25seTogdHJ1ZSxcclxuICAgIC8vICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAvLyAgIHNhbWVTaXRlOiBcImxheFwiLFxyXG4gICAgLy8gICBtYXhBZ2U6IDYwICogMTAsIC8vIDEwIG1pbnV0ZXNcclxuICAgIC8vICAgcGF0aDogXCIvXCIsXHJcbiAgICAvLyB9KTtcclxuXHJcbiAgICAvLyBDb25zdHJ1Y3QgdGhlIGF1dGhvcml6YXRpb24gVVJMIHdpdGhvdXQgdGhlIHN0YXRlIHBhcmFtZXRlciBmb3Igbm93XHJcbiAgICBjb25zdCBhdXRob3JpemF0aW9uQ29kZVVybCA9IGBodHRwczovL2FwaS5zY2h3YWJhcGkuY29tL3YxL29hdXRoL2F1dGhvcml6ZT9jbGllbnRfaWQ9JHtjbGllbnRJZH0mcmVkaXJlY3RfdXJpPSR7Y2FsbGJhY2tVcmx9YDtcclxuXHJcbiAgICBjb25zb2xlLmxvZyhcIkdlbmVyYXRlZCBhdXRob3JpemF0aW9uIFVSTFwiKTtcclxuICAgIHJldHVybiBhdXRob3JpemF0aW9uQ29kZVVybDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGdlbmVyYXRpbmcgYXV0aG9yaXphdGlvbiBVUkw6XCIsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEF1dGhvcml6YXRpb25Db2RlQ2FsbGJhY2tIYW5kbGVyKGNvZGUsIHN0YXRlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnNvbGUubG9nKFwiUHJvY2Vzc2luZyBPQXV0aCBjYWxsYmFjayB3aXRoIGNvZGVcIik7XHJcblxyXG4gICAgaWYgKCFjb2RlKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBhdXRob3JpemF0aW9uIGNvZGUgcHJvdmlkZWRcIik7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJObyBhdXRob3JpemF0aW9uIGNvZGUgcHJvdmlkZWRcIiB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE9ubHkgdmVyaWZ5IHN0YXRlIGlmIGl0J3Mgbm90IHRoZSBieXBhc3MgdmFsdWVcclxuICAgIGlmIChzdGF0ZSAhPT0gXCJieXBhc3Nfc3RhdGVfY2hlY2tcIikge1xyXG4gICAgICBpZiAoIXN0YXRlKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIk5vIHN0YXRlIHBhcmFtZXRlciBwcm92aWRlZFwiKTtcclxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IFwiTm8gc3RhdGUgcGFyYW1ldGVyIHByb3ZpZGVkXCIgfTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmVyaWZ5IHRoZSBzdGF0ZSBwYXJhbWV0ZXIgdG8gcHJldmVudCBDU1JGIGF0dGFja3NcclxuICAgICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgICAgY29uc3Qgc3RvcmVkU3RhdGUgPSBjb29raWVzT2JqLmdldChcInNjaHdhYl9vYXV0aF9zdGF0ZVwiKT8udmFsdWU7XHJcblxyXG4gICAgICBpZiAoIXN0b3JlZFN0YXRlIHx8IHN0YXRlICE9PSBzdG9yZWRTdGF0ZSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJJbnZhbGlkIHN0YXRlIHBhcmFtZXRlclwiKTtcclxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IFwiSW52YWxpZCBzdGF0ZSBwYXJhbWV0ZXJcIiB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDbGVhciB0aGUgc3RhdGUgY29va2llXHJcbiAgICAgIGNvb2tpZXNPYmouZGVsZXRlKFwic2Nod2FiX29hdXRoX3N0YXRlXCIpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5sb2coXCJCeXBhc3Npbmcgc3RhdGUgcGFyYW1ldGVyIGNoZWNrXCIpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFN0b3JlIHRoZSBhdXRob3JpemF0aW9uIGNvZGUgZm9yIGxhdGVyIHVzZVxyXG4gICAgY29uc3Qgc3RvcmVSZXN1bHQgPSBhd2FpdCBzdG9yZUF1dGhvcml6YXRpb25Db2RlKGNvZGUpO1xyXG5cclxuICAgIGlmICghc3RvcmVSZXN1bHQpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBzdG9yZSBhdXRob3JpemF0aW9uIGNvZGVcIik7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJGYWlsZWQgdG8gc3RvcmUgYXV0aG9yaXphdGlvbiBjb2RlXCIgfTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhcIkF1dGhvcml6YXRpb24gY29kZSBzdG9yZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG5cclxuICAgIC8vIEdldCB0aGUgYWNjZXNzIHRva2VuXHJcbiAgICBjb25zdCB0b2tlblJlc3VsdCA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XHJcblxyXG4gICAgaWYgKCF0b2tlblJlc3VsdCkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGdldCBhY2Nlc3MgdG9rZW5cIik7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJGYWlsZWQgdG8gZ2V0IGFjY2VzcyB0b2tlblwiIH07XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coXCJBY2Nlc3MgdG9rZW4gcmV0cmlldmVkIHN1Y2Nlc3NmdWxseVwiKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBtZXNzYWdlOiBcIkF1dGhvcml6YXRpb24gc3VjY2Vzc2Z1bFwiLFxyXG4gICAgICBoYXNBY2Nlc3NUb2tlbjogISF0b2tlblJlc3VsdC5hY2Nlc3NfdG9rZW4sXHJcbiAgICAgIGhhc1JlZnJlc2hUb2tlbjogISF0b2tlblJlc3VsdC5yZWZyZXNoX3Rva2VuXHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gYXV0aG9yaXphdGlvbiBjb2RlIGNhbGxiYWNrOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgXCJVbmtub3duIGVycm9yXCIgfTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBY2Nlc3NUb2tlbigpIHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coXCJHZXR0aW5nIGFjY2VzcyB0b2tlbi4uLlwiKTtcclxuICAgIGNvbnN0IGF1dGhvcml6YXRpb25Db2RlID0gYXdhaXQgcmV0cmlldmVBdXRob3JpemF0aW9uQ29kZSgpO1xyXG5cclxuICAgIGlmICghYXV0aG9yaXphdGlvbkNvZGUpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIGF1dGhvcml6YXRpb24gY29kZSBmb3VuZCBpbiBjb29raWVzXCIpO1xyXG4gICAgICAvLyBUcnkgdG8gcmVmcmVzaCB0aGUgdG9rZW4gaW5zdGVhZCBvZiB0aHJvd2luZyBhbiBlcnJvclxyXG4gICAgICBjb25zdCByZWZyZXNoZWRUb2tlbiA9IGF3YWl0IHJlZnJlc2hUb2tlbigpO1xyXG4gICAgICBpZiAocmVmcmVzaGVkVG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIlN1Y2Nlc3NmdWxseSByZWZyZXNoZWQgdG9rZW4gaW5zdGVhZCBvZiBnZXR0aW5nIGEgbmV3IG9uZVwiKTtcclxuICAgICAgICByZXR1cm4gcmVmcmVzaGVkVG9rZW47XHJcbiAgICAgIH1cclxuICAgICAgLy8gSWYgd2UgY2FuJ3QgcmVmcmVzaCwgcmV0dXJuIG51bGwgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkNvdWxkIG5vdCByZWZyZXNoIHRva2VuIGVpdGhlciwgcmV0dXJuaW5nIG51bGxcIik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGZvciBjbGllbnQgY3JlZGVudGlhbHNcclxuICAgIGlmICghY2xpZW50SWQgfHwgIWNsaWVudFNlY3JldCkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTWlzc2luZyBjbGllbnQgY3JlZGVudGlhbHNcIik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENyZWF0ZSB0aGUgYXV0aG9yaXphdGlvbiBoZWFkZXJcclxuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSBCdWZmZXIuZnJvbShgJHtjbGllbnRJZH06JHtjbGllbnRTZWNyZXR9YCkudG9TdHJpbmcoXCJiYXNlNjRcIik7XHJcblxyXG4gICAgLy8gRW5zdXJlIHRoZSBjb2RlIGlzIHByb3Blcmx5IGVuY29kZWRcclxuICAgIGNvbnN0IGVuY29kZWRDb2RlID0gZW5jb2RlVVJJQ29tcG9uZW50KGF1dGhvcml6YXRpb25Db2RlKTtcclxuICAgIGNvbnN0IGVuY29kZWRDYWxsYmFja1VybCA9IGVuY29kZVVSSUNvbXBvbmVudChjYWxsYmFja1VybCk7XHJcblxyXG4gICAgLy8gU2V0IHVwIHRoZSByZXF1ZXN0XHJcbiAgICBjb25zdCBhY2Nlc3NUb2tlblVybCA9IFwiaHR0cHM6Ly9hcGkuc2Nod2FiYXBpLmNvbS92MS9vYXV0aC90b2tlblwiO1xyXG4gICAgY29uc3QgaGVhZGVycyA9IHtcclxuICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIixcclxuICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCYXNpYyAke2F1dGhIZWFkZXJ9YFxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBDcmVhdGUgdGhlIHJlcXVlc3QgYm9keVxyXG4gICAgY29uc3QgYm9keSA9IGBncmFudF90eXBlPWF1dGhvcml6YXRpb25fY29kZSZjb2RlPSR7ZW5jb2RlZENvZGV9JnJlZGlyZWN0X3VyaT0ke2VuY29kZWRDYWxsYmFja1VybH1gO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiTWFraW5nIHRva2VuIHJlcXVlc3QgdG86XCIsIGFjY2Vzc1Rva2VuVXJsKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVXNpbmcgYXV0aG9yaXphdGlvbiBjb2RlOlwiLCBhdXRob3JpemF0aW9uQ29kZS5zdWJzdHJpbmcoMCwgMTApICsgXCIuLi5cIik7XHJcblxyXG4gICAgLy8gTWFrZSB0aGUgcmVxdWVzdFxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KGFjY2Vzc1Rva2VuVXJsLCBib2R5LCB7IGhlYWRlcnMgfSk7XHJcblxyXG4gICAgLy8gUHJvY2VzcyB0aGUgcmVzcG9uc2VcclxuICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlRva2VuIHJlc3BvbnNlIHN0YXR1czpcIiwgcmVzcG9uc2Uuc3RhdHVzKTtcclxuXHJcbiAgICAgIGNvbnN0IGFjY2Vzc1Rva2VuUmVzcG9uc2UgPSByZXNwb25zZS5kYXRhO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkFjY2VzcyB0b2tlbiByZXRyaWV2ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG5cclxuICAgICAgLy8gU3RvcmUgdGhlIHRva2Vuc1xyXG4gICAgICBpZiAoYWNjZXNzVG9rZW5SZXNwb25zZS5hY2Nlc3NfdG9rZW4pIHtcclxuICAgICAgICBhd2FpdCBzdG9yZUFjY2Vzc1Rva2VuKGFjY2Vzc1Rva2VuUmVzcG9uc2UpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQWNjZXNzIHRva2VuIHN0b3JlZCBzdWNjZXNzZnVsbHlcIik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChhY2Nlc3NUb2tlblJlc3BvbnNlLnJlZnJlc2hfdG9rZW4pIHtcclxuICAgICAgICBhd2FpdCBzdG9yZVJlZnJlc2hUb2tlbihhY2Nlc3NUb2tlblJlc3BvbnNlLnJlZnJlc2hfdG9rZW4pO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiUmVmcmVzaCB0b2tlbiBzdG9yZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gYWNjZXNzVG9rZW5SZXNwb25zZTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIHJlc3BvbnNlIHN0YXR1czpcIiwgcmVzcG9uc2Uuc3RhdHVzKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBpbiBnZXRBY2Nlc3NUb2tlbjpcIiwgZXJyb3IucmVzcG9uc2U/LnN0YXR1cyk7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGV0YWlsczpcIiwgZXJyb3IucmVzcG9uc2U/LmRhdGEpO1xyXG4gICAgLy8gUmV0dXJuIG51bGwgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBhc3luYyBmdW5jdGlvbiBnZXRBdXRob3JpemF0aW9uQ29kZSgpIHtcclxuLy8gY29uc29sZS5sb2coXCJSZWNlaXZlZCByZXF1ZXN0IHdpdGggbWV0aG9kOlwiLCByZXEubWV0aG9kKTtcclxuLy8gaWYgKHJlcS5tZXRob2QgPT09IFwiR0VUXCIpIHtcclxuLy8gICBjb25zdCBhdXRob3JpemF0aW9uQ29kZSA9IHJlcS5xdWVyeS5jb2RlO1xyXG4vLyAgIGNvbnNvbGUubG9nKFwiUmVjZWl2ZWQgYXV0aG9yaXphdGlvbiBjb2RlOiBcIiArIGF1dGhvcml6YXRpb25Db2RlKTtcclxuLy8gICBpZiAoYXV0aG9yaXphdGlvbkNvZGUpIHtcclxuLy8gICAgIGF3YWl0IHN0b3JlQXV0aG9yaXphdGlvbkNvZGUoYXV0aG9yaXphdGlvbkNvZGUpO1xyXG4vLyAgICAgcmVzLnJlZGlyZWN0KFwiL2Rhc2hib2FyZFwiKTtcclxuLy8gICB9IGVsc2Uge1xyXG4vLyAgICAgcmVzLnN0YXR1cyg0MDApLnNlbmQoXCJObyBhdXRob3JpemF0aW9uIGNvZGUgcHJvdmlkZWRcIik7XHJcbi8vICAgfVxyXG4vLyB9IGVsc2Uge1xyXG4vLyAgIHJlcy5zdGF0dXMoNDA1KS5zZW5kKFwiTWV0aG9kIG5vdCBhbGxvd2VkXCIpO1xyXG4vLyB9XHJcbi8vIH1cclxuXHJcbi8vIGFzeW5jIGZ1bmN0aW9uIGxvZ2luKCkge1xyXG4vLyAgIHRyeSB7XHJcbi8vICAgICBjb25zdCBhY2Nlc3NUb2tlbiA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XHJcbi8vICAgICAvLyBTdG9yZSB0aGUgYWNjZXNzIHRva2VuIGZvciBsYXRlciB1c2VcclxuLy8gICAgIGF3YWl0IHN0b3JlQWNjZXNzVG9rZW4oYWNjZXNzVG9rZW5SZXNwb25zZS5hY2Nlc3NfdG9rZW4pOyAvL3ZhbGlkIGZvciAzMCBtaW51dGVzXHJcbi8vICAgICBhd2FpdCBzdG9yZVJlZnJlc2hUb2tlbihhY2Nlc3NUb2tlblJlc3BvbnNlLnJlZnJlc2hfdG9rZW4pOyAvL3ZhbGlkIGZvciA3IGRheXNcclxuLy8gICAgIHJldHVybiB7IGFjY2Vzc1Rva2VuIH07XHJcbi8vICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuLy8gICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xyXG4vLyAgICAgdGhyb3cgZXJyb3I7XHJcbi8vICAgfVxyXG4vLyB9XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVmcmVzaFRva2VuKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zb2xlLmxvZyhcIlJlZnJlc2hpbmcgYWNjZXNzIHRva2VuLi4uXCIpO1xyXG5cclxuICAgIGNvbnN0IHJlZnJlc2hUb2tlbiA9IGF3YWl0IHJldHJpZXZlUmVmcmVzaFRva2VuKHRydWUpO1xyXG4gICAgaWYgKCFyZWZyZXNoVG9rZW4pIHtcclxuICAgICAgY29uc29sZS5sb2coXCJObyByZWZyZXNoIHRva2VuIGZvdW5kLiBVc2VyIG1pZ2h0IG5lZWQgdG8gbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgYWNjZXNzVG9rZW5VcmwgPSBcImh0dHBzOi8vYXBpLnNjaHdhYmFwaS5jb20vdjEvb2F1dGgvdG9rZW5cIjtcclxuXHJcbiAgICAvLyBDcmVhdGUgdGhlIEF1dGhvcml6YXRpb24gaGVhZGVyIHdpdGggQmFzaWMgQXV0aFxyXG4gICAgY29uc3QgYXV0aEhlYWRlciA9IEJ1ZmZlci5mcm9tKGAke2NsaWVudElkfToke2NsaWVudFNlY3JldH1gKS50b1N0cmluZyhcImJhc2U2NFwiKTtcclxuICAgIGNvbnN0IGhlYWRlcnMgPSB7XHJcbiAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkXCIsXHJcbiAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmFzaWMgJHthdXRoSGVhZGVyfWAsIC8vIEJhc2ljIEF1dGhlbnRpY2F0aW9uXHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGJvZHkgPSBgZ3JhbnRfdHlwZT1yZWZyZXNoX3Rva2VuJnJlZnJlc2hfdG9rZW49JHtyZWZyZXNoVG9rZW59YDtcclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGFjY2Vzc1Rva2VuVXJsLCB7XHJcbiAgICAgIG1ldGhvZDogXCJQT1NUXCIsXHJcbiAgICAgIGhlYWRlcnMsXHJcbiAgICAgIGJvZHksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yUmVzcG9uc2UgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHJlZnJlc2hpbmcgdG9rZW46ICR7cmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3JSZXNwb25zZS5lcnJvcl9kZXNjcmlwdGlvbiB8fCBKU09OLnN0cmluZ2lmeShlcnJvclJlc3BvbnNlKX1gKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbmV3QWNjZXNzVG9rZW5SZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgIGNvbnNvbGUubG9nKFwiTmV3IGFjY2VzcyB0b2tlbiByZXRyaWV2ZWQ6XCIsIG5ld0FjY2Vzc1Rva2VuUmVzcG9uc2UuYWNjZXNzX3Rva2VuKTtcclxuXHJcbiAgICAvLyBTdG9yZSB0aGUgbmV3IGFjY2VzcyB0b2tlblxyXG4gICAgYXdhaXQgc3RvcmVBY2Nlc3NUb2tlbihuZXdBY2Nlc3NUb2tlblJlc3BvbnNlKTtcclxuXHJcbiAgICAvLyBTdG9yZSB0aGUgbmV3IHJlZnJlc2ggdG9rZW4gKG9ubHkgaWYgcHJlc2VudClcclxuICAgIGlmIChuZXdBY2Nlc3NUb2tlblJlc3BvbnNlLnJlZnJlc2hfdG9rZW4pIHtcclxuICAgICAgYXdhaXQgc3RvcmVSZWZyZXNoVG9rZW4obmV3QWNjZXNzVG9rZW5SZXNwb25zZS5yZWZyZXNoX3Rva2VuKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbmV3QWNjZXNzVG9rZW5SZXNwb25zZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJlZnJlc2hUb2tlbjpcIiwgZXJyb3IubWVzc2FnZSk7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcblxyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbnMgdG8gc3RvcmUgYW5kIHJldHJpZXZlIGF1dGhvcml6YXRpb24gY29kZSBhbmQgYWNjZXNzIHRva2VuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdG9yZUF1dGhvcml6YXRpb25Db2RlKGNvZGUpIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCFjb2RlKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBhdXRob3JpemF0aW9uIGNvZGUgcHJvdmlkZWRcIik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhbiB0aGUgY29kZSBpZiBpdCBjb250YWlucyBVUkwtZW5jb2RlZCBjaGFyYWN0ZXJzXHJcbiAgICBjb25zdCBjbGVhbkNvZGUgPSBjb2RlLnJlcGxhY2UoLyU0MC9nLCAnQCcpO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKGBTdG9yaW5nIGF1dGhvcml6YXRpb24gY29kZTogJHtjbGVhbkNvZGUuc3Vic3RyaW5nKDAsIDEwKX0uLi5gKTtcclxuXHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG5cclxuICAgIC8vIFNldCB0aGUgY29va2llIHdpdGggYSBsb25nZXIgZXhwaXJhdGlvbiAoMiBob3VycykgdG8gZW5zdXJlIHdlIGhhdmUgdGltZSB0byB1c2UgaXRcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiYXV0aG9yaXphdGlvbl9jb2RlXCIsIGNsZWFuQ29kZSwge1xyXG4gICAgICBtYXhBZ2U6IDcyMDAsICAvLyAyIGhvdXJzIGluIHNlY29uZHNcclxuICAgICAgcGF0aDogJy8nLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiAnc3RyaWN0J1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gVmVyaWZ5IHRoZSBjb2RlIHdhcyBzdG9yZWRcclxuICAgIGNvbnN0IHN0b3JlZENvZGUgPSBjb29raWVzT2JqLmdldChcImF1dGhvcml6YXRpb25fY29kZVwiKT8udmFsdWU7XHJcblxyXG4gICAgaWYgKHN0b3JlZENvZGUpIHtcclxuICAgICAgY29uc29sZS5sb2coYEF1dGhvcml6YXRpb24gY29kZSBzdG9yZWQgc3VjY2Vzc2Z1bGx5OiAke3N0b3JlZENvZGUuc3Vic3RyaW5nKDAsIDEwKX0uLi5gKTtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIHN0b3JlIGF1dGhvcml6YXRpb24gY29kZSBpbiBjb29raWVzXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdG9yaW5nIGF1dGhvcml6YXRpb24gY29kZTpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJldHJpZXZlQXV0aG9yaXphdGlvbkNvZGUoKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb25zdCBhdXRoY29kZSA9IGNvb2tpZXNPYmouZ2V0KFwiYXV0aG9yaXphdGlvbl9jb2RlXCIpPy52YWx1ZTtcclxuXHJcbiAgICBpZiAoIWF1dGhjb2RlKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTm8gYXV0aG9yaXphdGlvbiBjb2RlIGZvdW5kIGluIGNvb2tpZXNcIik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKGBSZXRyaWV2ZWQgYXV0aG9yaXphdGlvbiBjb2RlIGZyb20gY29va2llczogJHthdXRoY29kZS5zdWJzdHJpbmcoMCwgMTApfS4uLmApO1xyXG4gICAgcmV0dXJuIGF1dGhjb2RlO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gcmV0cmlldmVBdXRob3JpemF0aW9uQ29kZTpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3RvcmVBY2Nlc3NUb2tlbih0b2tlbikge1xyXG4gIHRyeSB7XHJcbiAgICBpZiAoIXRva2VuIHx8ICF0b2tlbi5hY2Nlc3NfdG9rZW4pIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIGFjY2VzcyB0b2tlbiBwcm92aWRlZCBvciBpbnZhbGlkIHRva2VuIGZvcm1hdFwiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiU3RvcmluZyBhY2Nlc3MgdG9rZW46XCIsIHRva2VuLmFjY2Vzc190b2tlbi5zdWJzdHJpbmcoMCwgMTApICsgXCIuLi5cIik7XHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgY29va2llc09iai5zZXQoXCJhY2Nlc3NfdG9rZW5cIiwgdG9rZW4uYWNjZXNzX3Rva2VuLCB7XHJcbiAgICAgIG1heEFnZTogMTgwMCxcclxuICAgICAgcGF0aDogJy8nLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiAnc3RyaWN0J1xyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN0b3JpbmcgYWNjZXNzIHRva2VuOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG4vL3RoaXMgaXMgYSBkdXBsaWNhdGUgY29kZSwgdW50aWwgd2UgZmluZCB0aGUgc29sdXRpb24gZm9yIHRoZSBhY2Nlc3NUb2tlbiBwcm9ibGVtIGluIHRoZSBBY2Nlc3NUb2tlbiBmdW5jdGlvblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3RvcmVBY2Nlc3NUb2tlbkZpcnN0TG9naW4odG9rZW4pIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCF0b2tlbikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTm8gYWNjZXNzIHRva2VuIHByb3ZpZGVkXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiYWNjZXNzX3Rva2VuXCIsIHRva2VuLCB7XHJcbiAgICAgIG1heEFnZTogMTgwMCxcclxuICAgICAgcGF0aDogJy8nLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiAnc3RyaWN0J1xyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN0b3JpbmcgZmlyc3QgbG9naW4gYWNjZXNzIHRva2VuOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJldHJpZXZlQWNjZXNzVG9rZW4oc2tpcFJlZnJlc2ggPSBmYWxzZSkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgbGV0IGFjY2Vzc1Rva2VuID0gY29va2llc09iai5nZXQoXCJhY2Nlc3NfdG9rZW5cIik/LnZhbHVlO1xyXG5cclxuICAgIC8vIElmIHdlIGhhdmUgYW4gYWNjZXNzIHRva2VuIG9yIHdlJ3JlIHNraXBwaW5nIHJlZnJlc2gsIHJldHVybiBpdCAob3IgbnVsbClcclxuICAgIGlmIChhY2Nlc3NUb2tlbiB8fCBza2lwUmVmcmVzaCkge1xyXG4gICAgICByZXR1cm4gYWNjZXNzVG9rZW4gfHwgbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBPbmx5IGF0dGVtcHQgdG8gcmVmcmVzaCBpZiB3ZSBkb24ndCBoYXZlIGEgdG9rZW4gYW5kIHdlJ3JlIG5vdCBza2lwcGluZyByZWZyZXNoXHJcbiAgICBjb25zb2xlLmxvZyhcIk5vIGFjY2VzcyB0b2tlbiBmb3VuZCwgYXR0ZW1wdGluZyB0byByZWZyZXNoXCIpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQWNjZXNzIHRva2VuIGlzIG5vdCBmb3VuZCwgdHJ5IHRvIHJlZnJlc2hcclxuICAgICAgY29uc3QgcmVmcmVzaFJlc3VsdCA9IGF3YWl0IHJlZnJlc2hUb2tlbigpO1xyXG4gICAgICBpZiAocmVmcmVzaFJlc3VsdCkge1xyXG4gICAgICAgIC8vIFRyeSB0byBnZXQgdGhlIGFjY2VzcyB0b2tlbiBhZ2FpbiBhZnRlciByZWZyZXNoXHJcbiAgICAgICAgY29uc3QgbmV3Q29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgICAgICBhY2Nlc3NUb2tlbiA9IG5ld0Nvb2tpZXNPYmouZ2V0KFwiYWNjZXNzX3Rva2VuXCIpPy52YWx1ZTtcclxuXHJcbiAgICAgICAgaWYgKGFjY2Vzc1Rva2VuKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlN1Y2Nlc3NmdWxseSByZWZyZXNoZWQgYW5kIHJldHJpZXZlZCBhY2Nlc3MgdG9rZW5cIik7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwiUmVmcmVzaCBzdWNjZWVkZWQgYnV0IG5vIGFjY2VzcyB0b2tlbiBmb3VuZFwiKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJUb2tlbiByZWZyZXNoIGZhaWxlZFwiKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHJlZnJlc2hpbmcgdG9rZW46XCIsIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhY2Nlc3NUb2tlbiB8fCBudWxsO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gcmV0cmlldmVBY2Nlc3NUb2tlbjpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3RvcmVSZWZyZXNoVG9rZW4odG9rZW4pIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCF0b2tlbikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTm8gcmVmcmVzaCB0b2tlbiBwcm92aWRlZFwiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb29raWVzT2JqLnNldChcInJlZnJlc2hfdG9rZW5cIiwgdG9rZW4sIHtcclxuICAgICAgbWF4QWdlOiA2MDQ4MDAsXHJcbiAgICAgIHBhdGg6ICcvJyxcclxuICAgICAgaHR0cE9ubHk6IHRydWUsXHJcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG4gICAgICBzYW1lU2l0ZTogJ3N0cmljdCdcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdG9yaW5nIHJlZnJlc2ggdG9rZW46XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZXRyaWV2ZVJlZnJlc2hUb2tlbihzdXBwcmVzc0xvZyA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb25zdCByZWZyZXNoVG9rZW5WYWx1ZSA9IGNvb2tpZXNPYmouZ2V0KFwicmVmcmVzaF90b2tlblwiKT8udmFsdWU7XHJcbiAgICBpZiAoIXJlZnJlc2hUb2tlblZhbHVlICYmICFzdXBwcmVzc0xvZykge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIHJlZnJlc2ggdG9rZW4gZm91bmRcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gcmVmcmVzaFRva2VuVmFsdWUgfHwgbnVsbDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJldHJpZXZlUmVmcmVzaFRva2VuOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdG9yZUNvcnJlbElkKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB1c2VyUHJlZmVyZW5jZSA9IGF3YWl0IGdldFVzZXJQcmVmZXJlbmNlKCk7XHJcbiAgICBjb25zdCBzY2h3YWJDbGllbnRDb3JyZWxJZCA9IHVzZXJQcmVmZXJlbmNlLnN0cmVhbWVySW5mbz8uWzBdPy5zY2h3YWJDbGllbnRDb3JyZWxJZDtcclxuXHJcbiAgICBpZiAoIXNjaHdhYkNsaWVudENvcnJlbElkKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBjb3JyZWxhdGlvbiBJRCBmb3VuZCBpbiB1c2VyIHByZWZlcmVuY2VzXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiY2xpZW50X2NvcnJlbElkXCIsIHNjaHdhYkNsaWVudENvcnJlbElkLCB7XHJcbiAgICAgIG1heEFnZTogODY0MDAsXHJcbiAgICAgIHBhdGg6ICcvJyxcclxuICAgICAgaHR0cE9ubHk6IHRydWUsXHJcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG4gICAgICBzYW1lU2l0ZTogJ3N0cmljdCdcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdG9yaW5nIGNvcnJlbGF0aW9uIElEOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmV0cmlldmVDb3JyZWxJZChzdXBwcmVzc0xvZyA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb25zdCBjb3JyZWxJZCA9IGNvb2tpZXNPYmouZ2V0KFwiY2xpZW50X2NvcnJlbElkXCIpPy52YWx1ZTtcclxuICAgIGlmICghY29ycmVsSWQgJiYgIXN1cHByZXNzTG9nKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTm8gY2xpZW50IGNvcnJlbElkIGZvdW5kXCIpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGNvcnJlbElkIHx8IG51bGw7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBpbiByZXRyaWV2ZUNvcnJlbElkOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdG9yZUN1c3RvbWVySWQoKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHVzZXJQcmVmZXJlbmNlID0gYXdhaXQgZ2V0VXNlclByZWZlcmVuY2UoKTtcclxuICAgIGNvbnN0IHNjaHdhYkNsaWVudEN1c3RvbWVySWQgPSB1c2VyUHJlZmVyZW5jZS5zdHJlYW1lckluZm8/LlswXT8uc2Nod2FiQ2xpZW50Q3VzdG9tZXJJZDtcclxuXHJcbiAgICBpZiAoIXNjaHdhYkNsaWVudEN1c3RvbWVySWQpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIGN1c3RvbWVyIElEIGZvdW5kIGluIHVzZXIgcHJlZmVyZW5jZXNcIik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgY29va2llc09iai5zZXQoXCJjbGllbnRfY3VzdG9tZXJJZFwiLCBzY2h3YWJDbGllbnRDdXN0b21lcklkLCB7XHJcbiAgICAgIG1heEFnZTogODY0MDAsXHJcbiAgICAgIHBhdGg6ICcvJyxcclxuICAgICAgaHR0cE9ubHk6IHRydWUsXHJcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG4gICAgICBzYW1lU2l0ZTogJ3N0cmljdCdcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdG9yaW5nIGN1c3RvbWVyIElEOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmV0cmlldmVDdXN0b21lcklkKHN1cHByZXNzTG9nID0gZmFsc2UpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvbnN0IGN1c3RvbWVySWQgPSBjb29raWVzT2JqLmdldChcImNsaWVudF9jdXN0b21lcklkXCIpPy52YWx1ZTtcclxuICAgIGlmICghY3VzdG9tZXJJZCAmJiAhc3VwcHJlc3NMb2cpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJObyBjbGllbnQgY3VzdG9tZXJJZCBmb3VuZFwiKTtcclxuICAgIH1cclxuICAgIHJldHVybiBjdXN0b21lcklkIHx8IG51bGw7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBpbiByZXRyaWV2ZUN1c3RvbWVySWQ6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG5cclxuLy9FWEFNUExFIEFQSSBDQUxMIFdJVEggVE9LRU4gLSBHZW5lcmljIGhlbHBlciBmdW5jdGlvblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbWFrZUFwaUNhbGwoZW5kcG9pbnQsIG1ldGhvZCA9IFwiR0VUXCIsIGJvZHkgPSBudWxsLCBza2lwUmVmcmVzaCA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGFjY2Vzc1Rva2VuID0gYXdhaXQgcmV0cmlldmVBY2Nlc3NUb2tlbihza2lwUmVmcmVzaCk7IC8vIFJldHJpZXZlIHRoZSBzdG9yZWQgYWNjZXNzIHRva2VuXHJcblxyXG4gICAgaWYgKCFhY2Nlc3NUb2tlbikge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIGFjY2VzcyB0b2tlbiBhdmFpbGFibGUgZm9yIEFQSSBjYWxsXCIpO1xyXG4gICAgICByZXR1cm4geyBlcnJvcjogXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiIH07XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGVhZGVycyA9IHtcclxuICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHthY2Nlc3NUb2tlbn1gLFxyXG4gICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIlxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBvcHRpb25zID0ge1xyXG4gICAgICBtZXRob2QsXHJcbiAgICAgIGhlYWRlcnNcclxuICAgIH07XHJcblxyXG4gICAgaWYgKGJvZHkgJiYgKG1ldGhvZCA9PT0gXCJQT1NUXCIgfHwgbWV0aG9kID09PSBcIlBVVFwiIHx8IG1ldGhvZCA9PT0gXCJQQVRDSFwiKSkge1xyXG4gICAgICBvcHRpb25zLmJvZHkgPSBKU09OLnN0cmluZ2lmeShib2R5KTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCBvcHRpb25zKTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcclxuICAgICAgY29uc29sZS5lcnJvcihgQVBJIGNhbGwgZmFpbGVkOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWAsIGVycm9yVGV4dCk7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiQVBJIGNhbGwgZmFpbGVkXCIsXHJcbiAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXHJcbiAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcclxuICAgICAgICBkZXRhaWxzOiBlcnJvclRleHRcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gbWFrZUFwaUNhbGw6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiB7IGVycm9yOiBlcnJvci5tZXNzYWdlIHx8IFwiVW5rbm93biBlcnJvclwiIH07XHJcbiAgfVxyXG59XHJcblxyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbmV4cG9ydCBjb25zdCBnZXRBY2NvdW50cyA9IGFzeW5jIChza2lwUmVmcmVzaCA9IGZhbHNlKSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGxldCBhY2Nlc3NUb2tlbiA9IGF3YWl0IHJldHJpZXZlQWNjZXNzVG9rZW4oc2tpcFJlZnJlc2gpO1xyXG4gICAgY29uc29sZS5sb2coXCJVc2luZyBhY2Nlc3MgdG9rZW4gZm9yIGFjY291bnRzIEFQSSBjYWxsOlwiLCBhY2Nlc3NUb2tlbiA/IFwiWWVzXCIgOiBcIk5vXCIpO1xyXG5cclxuICAgIGlmICghYWNjZXNzVG9rZW4pIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBlcnJvcjogXCJOb3QgY29ubmVjdGVkIHRvIFNjaHdhYlwiLFxyXG4gICAgICAgIG1lc3NhZ2U6IFwiUGxlYXNlIGNvbm5lY3QgeW91ciBTY2h3YWIgYWNjb3VudCBmaXJzdFwiLFxyXG4gICAgICAgIG5lZWRzQ29ubmVjdGlvbjogdHJ1ZVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxyXG4gICAgICBcImh0dHBzOi8vYXBpLnNjaHdhYmFwaS5jb20vdHJhZGVyL3YxL2FjY291bnRzXCIsXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuICAgICAgICB9LFxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBhY2NvdW50czpcIiwgZXJyb3IpO1xyXG5cclxuICAgIC8vIENoZWNrIGlmIGl0J3MgYW4gYXV0aGVudGljYXRpb24gZXJyb3JcclxuICAgIGlmIChlcnJvci5yZXNwb25zZSAmJiAoZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDEgfHwgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDMpKSB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiQXV0aGVudGljYXRpb24gZmFpbGVkXCIsXHJcbiAgICAgICAgbWVzc2FnZTogXCJZb3VyIFNjaHdhYiBjb25uZWN0aW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgcmVjb25uZWN0LlwiLFxyXG4gICAgICAgIG5lZWRzQ29ubmVjdGlvbjogdHJ1ZVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBhY2NvdW50c1wiLFxyXG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIHx8IFwiQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZFwiLFxyXG4gICAgICBuZWVkc0Nvbm5lY3Rpb246IGZhbHNlXHJcbiAgICB9O1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRNYXJrZXREYXRhID0gYXN5bmMgKHN5bWJvbCwgc2tpcFJlZnJlc2ggPSBmYWxzZSkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBsZXQgYWNjZXNzVG9rZW4gPSBhd2FpdCByZXRyaWV2ZUFjY2Vzc1Rva2VuKHNraXBSZWZyZXNoKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVXNpbmcgYWNjZXNzIHRva2VuIGZvciBtYXJrZXQgZGF0YSBBUEkgY2FsbDpcIiwgYWNjZXNzVG9rZW4gPyBcIlllc1wiIDogXCJOb1wiKTtcclxuXHJcbiAgICBpZiAoIWFjY2Vzc1Rva2VuKSB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiTm90IGNvbm5lY3RlZCB0byBTY2h3YWJcIixcclxuICAgICAgICBtZXNzYWdlOiBcIlBsZWFzZSBjb25uZWN0IHlvdXIgU2Nod2FiIGFjY291bnQgZmlyc3RcIixcclxuICAgICAgICBuZWVkc0Nvbm5lY3Rpb246IHRydWVcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcclxuICAgICAgYGh0dHBzOi8vYXBpLnNjaHdhYmFwaS5jb20vdjEvbWFya2V0cy9kYXRhLyR7c3ltYm9sfWAsXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuICAgICAgICB9LFxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBtYXJrZXQgZGF0YTpcIiwgZXJyb3IpO1xyXG5cclxuICAgIC8vIENoZWNrIGlmIGl0J3MgYW4gYXV0aGVudGljYXRpb24gZXJyb3JcclxuICAgIGlmIChlcnJvci5yZXNwb25zZSAmJiAoZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDEgfHwgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDMpKSB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiQXV0aGVudGljYXRpb24gZmFpbGVkXCIsXHJcbiAgICAgICAgbWVzc2FnZTogXCJZb3VyIFNjaHdhYiBjb25uZWN0aW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgcmVjb25uZWN0LlwiLFxyXG4gICAgICAgIG5lZWRzQ29ubmVjdGlvbjogdHJ1ZVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBtYXJrZXQgZGF0YVwiLFxyXG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIHx8IFwiQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZFwiLFxyXG4gICAgICBuZWVkc0Nvbm5lY3Rpb246IGZhbHNlXHJcbiAgICB9O1xyXG4gIH1cclxufTtcclxuXHJcbi8vIGV4cG9ydCB7XHJcbi8vICAgZ2V0QWNjb3VudHMsXHJcbi8vICAgZ2V0TWFya2V0RGF0YSxcclxuLy8gICBnZXRBdXRob3JpemF0aW9uQ29kZVVSTCxcclxuLy8gICBnZXRBdXRob3JpemF0aW9uQ29kZUNhbGxiYWNrSGFuZGxlcixcclxuLy8gICBnZXRBY2Nlc3NUb2tlbixcclxuLy8gICBzdG9yZUF1dGhvcml6YXRpb25Db2RlLFxyXG4vLyAgIHJldHJpZXZlQXV0aG9yaXphdGlvbkNvZGUsXHJcbi8vICAgc3RvcmVBY2Nlc3NUb2tlbixcclxuLy8gICByZXRyaWV2ZUFjY2Vzc1Rva2VuLFxyXG4vLyAgIHN0b3JlUmVmcmVzaFRva2VuLFxyXG4vLyAgIHJldHJpZXZlUmVmcmVzaFRva2VuLFxyXG4vLyAgIHJlZnJlc2hUb2tlbixcclxuLy8gfTtcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwiY29va2llcyIsImdldFVzZXJQcmVmZXJlbmNlIiwiY2xpZW50SWQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU0NIV0FCX0FQUF9LRVkiLCJjbGllbnRTZWNyZXQiLCJORVhUX1BVQkxJQ19TQ0hXQUJfU0VDUkVUIiwiY2FsbGJhY2tVcmwiLCJORVhUX1BVQkxJQ19TQ0hXQUJfQ0FMTEJBQ0tVUkwiLCJnZXRBdXRob3JpemF0aW9uQ29kZVVSTCIsImF1dGhvcml6YXRpb25Db2RlVXJsIiwiY29uc29sZSIsImxvZyIsImVycm9yIiwiZ2V0QXV0aG9yaXphdGlvbkNvZGVDYWxsYmFja0hhbmRsZXIiLCJjb2RlIiwic3RhdGUiLCJzdWNjZXNzIiwiY29va2llc09iaiIsInN0b3JlZFN0YXRlIiwiZ2V0IiwidmFsdWUiLCJkZWxldGUiLCJzdG9yZVJlc3VsdCIsInN0b3JlQXV0aG9yaXphdGlvbkNvZGUiLCJ0b2tlblJlc3VsdCIsImdldEFjY2Vzc1Rva2VuIiwibWVzc2FnZSIsImhhc0FjY2Vzc1Rva2VuIiwiYWNjZXNzX3Rva2VuIiwiaGFzUmVmcmVzaFRva2VuIiwicmVmcmVzaF90b2tlbiIsImF1dGhvcml6YXRpb25Db2RlIiwicmV0cmlldmVBdXRob3JpemF0aW9uQ29kZSIsInJlZnJlc2hlZFRva2VuIiwicmVmcmVzaFRva2VuIiwiYXV0aEhlYWRlciIsIkJ1ZmZlciIsImZyb20iLCJ0b1N0cmluZyIsImVuY29kZWRDb2RlIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiZW5jb2RlZENhbGxiYWNrVXJsIiwiYWNjZXNzVG9rZW5VcmwiLCJoZWFkZXJzIiwiYm9keSIsInN1YnN0cmluZyIsInJlc3BvbnNlIiwicG9zdCIsInN0YXR1cyIsImFjY2Vzc1Rva2VuUmVzcG9uc2UiLCJkYXRhIiwic3RvcmVBY2Nlc3NUb2tlbiIsInN0b3JlUmVmcmVzaFRva2VuIiwicmV0cmlldmVSZWZyZXNoVG9rZW4iLCJmZXRjaCIsIm1ldGhvZCIsIm9rIiwiZXJyb3JSZXNwb25zZSIsImpzb24iLCJlcnJvcl9kZXNjcmlwdGlvbiIsIkpTT04iLCJzdHJpbmdpZnkiLCJuZXdBY2Nlc3NUb2tlblJlc3BvbnNlIiwiY2xlYW5Db2RlIiwicmVwbGFjZSIsInNldCIsIm1heEFnZSIsInBhdGgiLCJodHRwT25seSIsInNlY3VyZSIsInNhbWVTaXRlIiwic3RvcmVkQ29kZSIsImF1dGhjb2RlIiwidG9rZW4iLCJzdG9yZUFjY2Vzc1Rva2VuRmlyc3RMb2dpbiIsInJldHJpZXZlQWNjZXNzVG9rZW4iLCJza2lwUmVmcmVzaCIsImFjY2Vzc1Rva2VuIiwicmVmcmVzaFJlc3VsdCIsIm5ld0Nvb2tpZXNPYmoiLCJzdXBwcmVzc0xvZyIsInJlZnJlc2hUb2tlblZhbHVlIiwic3RvcmVDb3JyZWxJZCIsInVzZXJQcmVmZXJlbmNlIiwic2Nod2FiQ2xpZW50Q29ycmVsSWQiLCJzdHJlYW1lckluZm8iLCJyZXRyaWV2ZUNvcnJlbElkIiwiY29ycmVsSWQiLCJzdG9yZUN1c3RvbWVySWQiLCJzY2h3YWJDbGllbnRDdXN0b21lcklkIiwicmV0cmlldmVDdXN0b21lcklkIiwiY3VzdG9tZXJJZCIsIm1ha2VBcGlDYWxsIiwiZW5kcG9pbnQiLCJvcHRpb25zIiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1c1RleHQiLCJkZXRhaWxzIiwiZ2V0QWNjb3VudHMiLCJuZWVkc0Nvbm5lY3Rpb24iLCJBdXRob3JpemF0aW9uIiwiZ2V0TWFya2V0RGF0YSIsInN5bWJvbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./actions/schwabAccess.js\n");

/***/ }),

/***/ "(rsc)/./actions/schwabTraderAPIactions.js":
/*!*******************************************!*\
  !*** ./actions/schwabTraderAPIactions.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   getAccount: () => (/* binding */ getAccount),\n/* harmony export */   getAccountNumbers: () => (/* binding */ getAccountNumbers),\n/* harmony export */   getAllOrders: () => (/* binding */ getAllOrders),\n/* harmony export */   getLinkedAccounts: () => (/* binding */ getLinkedAccounts),\n/* harmony export */   getOrder: () => (/* binding */ getOrder),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getTransaction: () => (/* binding */ getTransaction),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUserPreference: () => (/* binding */ getUserPreference),\n/* harmony export */   placeOrder: () => (/* binding */ placeOrder),\n/* harmony export */   previewOrder: () => (/* binding */ previewOrder),\n/* harmony export */   replaceOrder: () => (/* binding */ replaceOrder)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _schwabAccess__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schwabAccess */ \"(rsc)/./actions/schwabAccess.js\");\n\n\nconst TraderAPIbaseURL = \"https://api.schwabapi.com/trader/v1\";\n//ACCOUNTS\nasync function getAccountNumbers() {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = \"/accounts/accountNumbers\";\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get list of account numbers and their encrypted values`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function getLinkedAccounts() {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = \"/accounts?fields=positions\";\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get linked account(s) balances and positions for the logged in user.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function getAccount(accountNumber) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}`;\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get a specific account balance and positions for the logged in user.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\n//ORDERS\nasync function getOrders(accountNumber) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/orders`;\n    const url = `${TraderAPIbaseURL + endpoint}?fromEnteredTime=${encodeURIComponent(fromEnteredTime)}&toEnteredTime=${encodeURIComponent(toEnteredTime)}`;\n    console.log(`GET ${url} - Get all orders for a specific account.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(url, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function placeOrder(accountNumber, order) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/orders`;\n    console.log(`POST ${TraderAPIbaseURL + endpoint} - Place order for a specific account.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(TraderAPIbaseURL + endpoint, order, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function getOrder(accountNumber, orderId) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get a specific order by its ID, for a specific account`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function cancelOrder(accountNumber, orderId) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;\n    console.log(`DELETE ${TraderAPIbaseURL + endpoint} - Cancel an order for a specific account`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].delete(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function replaceOrder(accountNumber, orderId, order) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/orders/${orderId}`;\n    console.log(`PUT ${TraderAPIbaseURL + endpoint} - Replace order for a specific account`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].put(TraderAPIbaseURL + endpoint, order, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function getAllOrders(fromEnteredTime1, toEnteredTime1) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = \"/orders\";\n    const url = `${TraderAPIbaseURL + endpoint}?fromEnteredTime=${encodeURIComponent(fromEnteredTime1)}&toEnteredTime=${encodeURIComponent(toEnteredTime1)}`;\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(url, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function previewOrder(accountNumber, order) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/previewOrder`;\n    console.log(`POST ${TraderAPIbaseURL + endpoint} - Preview order for a specific account. **Coming Soon**.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(TraderAPIbaseURL + endpoint, order, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\n//TRANSACTIONS\nasync function getTransactions(accountNumber, startDate, endDate, types) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/transactions?startDate=${startDate}&endDate=${endDate}&types=${types}`; //encrypted id is required\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get all transactions information for a specific account.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\nasync function getTransaction(accountNumber, transactionId) {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = `/accounts/${accountNumber}/transactions/${transactionId}`;\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get specific transaction information for a specific account`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\n//USER PREFERENCE\nasync function getUserPreference() {\n    const accessToken = await (0,_schwabAccess__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken)();\n    const endpoint = \"/userPreference\";\n    console.log(`GET ${TraderAPIbaseURL + endpoint} - Get user preference information for the logged in user.`);\n    const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(TraderAPIbaseURL + endpoint, {\n        contentType: \"application/json\",\n        headers: {\n            \"Accept-Encoding\": \"application/json\",\n            Authorization: \"Bearer \" + accessToken\n        }\n    });\n    return response.data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./actions/schwabTraderAPIactions.js\n");

/***/ }),

/***/ "(rsc)/./app/api/get-saved-symbols/route.js":
/*!********************************************!*\
  !*** ./app/api/get-saved-symbols/route.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(rsc)/./actions/schwabAccess.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./lib/auth-options.js\");\n\n\n\n\nasync function GET() {\n    try {\n        // Get NextAuth session for user email\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get auth data\n        const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_1__.retrieveAccessToken)();\n        const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_1__.retrieveCustomerId)();\n        const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_1__.retrieveCorrelId)();\n        if (!accessToken || !customerId || !correlId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing authentication data\"\n            }, {\n                status: 401\n            });\n        }\n        // Call server endpoint to get saved symbols\n        const response = await fetch(\"https://localhost:3001/get-user-symbols\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"x-client-customerid\": customerId,\n                \"x-client-correlid\": correlId,\n                \"x-user-email\": session.user.email,\n                \"authorization\": `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to get saved symbols\"\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error getting saved symbols:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get saved symbols\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/get-saved-symbols/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth-options.js":
/*!*****************************!*\
  !*** ./lib/auth-options.js ***!
  \*****************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _rateLimit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rateLimit.js */ \"(rsc)/./lib/rateLimit.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma-dal.js */ \"(rsc)/./lib/prisma-dal.js\");\n\n\n\n\nconst CredentialsProvider = next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"] || next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__;\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET,\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    providers: [\n        CredentialsProvider({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\",\n                    required: true\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    required: true\n                }\n            },\n            async authorize (credentials, req) {\n                // Rate limit by IP address (or fallback to email if no IP)\n                let ip = null;\n                if (req && req.headers) {\n                    ip = req.headers[\"x-forwarded-for\"]?.split(\",\")[0]?.trim() || req.headers[\"x-real-ip\"] || req.socket?.remoteAddress;\n                }\n                // fallback to email if no IP (not ideal, but better than nothing)\n                const rateLimitKey = ip || (credentials?.email ? `email:${credentials.email}` : \"unknown\");\n                if ((0,_rateLimit_js__WEBPACK_IMPORTED_MODULE_0__.rateLimit)(rateLimitKey, 5, 60 * 1000)) {\n                    throw new Error(\"Too many login attempts. Please try again in a minute.\");\n                }\n                try {\n                    // Check if credentials are provided\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"Email and password are required\");\n                    }\n                    // Find the user using Prisma\n                    const user = await (0,_prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__.verifyUserCredentials)(credentials.email);\n                    if (!user) {\n                        console.log(\"User not found\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"User found:\", user.email);\n                    // Verify password\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        console.log(\"Invalid password\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"Password verified successfully\");\n                    // Return the user object\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name || user.email.split('@')[0],\n                        role: user.role || 'user'\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw error;\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/generalLogin\",\n        signOut: \"/generalLogin\",\n        error: \"/generalLogin\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user data to the token when signing in\n            if (user) {\n                token.id = user.id;\n                // Explicitly check for null/undefined and set to 'user' if so\n                token.role = user.role === undefined || user.role === null ? 'user' : user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Reject session if token is missing or expired\n            if (!token || !token.id || token.exp && Date.now() / 1000 > token.exp) {\n                return null;\n            }\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role === undefined || token.role === null ? 'user' : token.role;\n            }\n            return session;\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-options.js\n");

/***/ }),

/***/ "(rsc)/./lib/prisma-dal.js":
/*!***************************!*\
  !*** ./lib/prisma-dal.js ***!
  \***************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExcelData: () => (/* binding */ getExcelData),\n/* harmony export */   getTradingPairs: () => (/* binding */ getTradingPairs),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   saveExcelData: () => (/* binding */ saveExcelData),\n/* harmony export */   saveTradingPairs: () => (/* binding */ saveTradingPairs),\n/* harmony export */   verifyUserCredentials: () => (/* binding */ verifyUserCredentials)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\r\n * Prisma Data Access Layer (DAL)\r\n *\r\n * This file provides a layer of abstraction for database operations using Prisma.\r\n * It replaces the direct MongoDB operations in lib/mongodb.js.\r\n */ \n// Create a new PrismaClient instance\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n/**\r\n * User-related operations\r\n */ // Get a user by email\nasync function getUserByEmail(email) {\n    try {\n        console.log('Getting user by email:', email);\n        // Log available models\n        const availableModels = Object.keys(prisma).filter((key)=>!key.startsWith('_') && !key.startsWith('$') && typeof prisma[key] === 'object');\n        console.log('Available models:', availableModels);\n        // Determine which model to use for users\n        let userModel;\n        if (availableModels.includes('users')) {\n            userModel = prisma.users;\n        } else {\n            console.error('No user model found in Prisma client');\n            return null;\n        }\n        // Use the determined model\n        const user = await userModel.findUnique({\n            where: {\n                email\n            }\n        });\n        console.log('User found:', user ? 'Yes' : 'No');\n        if (user) {\n            // With Prisma, the id is already a string\n            // Just make sure we have a consistent property name\n            user.id = user.id || user._id;\n            // Log the user object for debugging\n            console.log('User object:', {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            });\n        }\n        return user;\n    } catch (error) {\n        console.error('Error getting user by email:', error);\n        return null;\n    }\n}\n// Verify user credentials\nasync function verifyUserCredentials(email) {\n    try {\n        const user = await getUserByEmail(email);\n        return user;\n    } catch (error) {\n        console.error('Error verifying user credentials:', error);\n        return null;\n    }\n}\n/**\r\n * Trading Pair operations\r\n */ // Save trading pairs\nasync function saveTradingPairs(pairs, userId) {\n    try {\n        console.log('saveTradingPairs called with pairs:', pairs.length);\n        console.log('saveTradingPairs called with userId:', userId);\n        // Delete all existing pairs for this user\n        await prisma.tradingPair.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Log a sample pair for debugging\n        if (pairs.length > 0) {\n            console.log('Sample pair to save:', {\n                pairKey: pairs[0].pairKey,\n                status: pairs[0].status,\n                combinedPNL: pairs[0].combinedPNL\n            });\n        }\n        // Create all new pairs - we need to handle each pair individually\n        // since createMany doesn't work well with JSON fields in MongoDB\n        let insertedCount = 0;\n        for (const pair of pairs){\n            try {\n                // For pairKey, ensure it's a string or null\n                const pairKeyValue = pair.pairKey ? String(pair.pairKey) : null;\n                // Transform the shortComponent to match the expected schema\n                const shortComponent = {\n                    dividendUserValue: parseInt(pair.shortComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.shortComponent?.dollarCost || 0),\n                    expectedQuantity: pair.shortComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.shortComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.shortComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.shortComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.shortComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.shortComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.shortComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.shortComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.shortComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.shortComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.shortComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.shortComponent?.formattedVolume || \"0\",\n                    id: pair.shortComponent?.id || \"0\",\n                    pnl: parseInt(pair.shortComponent?.pnl || 0),\n                    sectorValue: pair.shortComponent?.sectorValue || \"\",\n                    spreadUserValue: pair.shortComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.shortComponent?.spreadValue || 0,\n                    statusValue: pair.shortComponent?.statusValue || \"\",\n                    ticker: pair.shortComponent?.ticker || \"\"\n                };\n                // Transform the longComponent to match the expected schema\n                const longComponent = {\n                    dividendUserValue: parseInt(pair.longComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.longComponent?.dollarCost || 0),\n                    // expectedQuantity is a Json type in the schema\n                    expectedQuantity: pair.longComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.longComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.longComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.longComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.longComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.longComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.longComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.longComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.longComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.longComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.longComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.longComponent?.formattedVolume || \"0\",\n                    id: pair.longComponent?.id || \"0\",\n                    pnl: parseInt(pair.longComponent?.pnl || 0),\n                    sectorValue: pair.longComponent?.sectorValue || \"\",\n                    // spreadUserValue is a Json type in the schema\n                    spreadUserValue: pair.longComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.longComponent?.spreadValue || 0,\n                    statusValue: pair.longComponent?.statusValue || \"\",\n                    ticker: pair.longComponent?.ticker || \"\"\n                };\n                await prisma.tradingPair.create({\n                    data: {\n                        pairKey: pairKeyValue,\n                        status: pair.status || \"\",\n                        shortComponent: shortComponent,\n                        longComponent: longComponent,\n                        combinedPNL: pair.combinedPNL || \"0\",\n                        userId,\n                        createdAt: now,\n                        updatedAt: now\n                    }\n                });\n                insertedCount++;\n            } catch (error) {\n                console.error(`Error inserting pair:`, error);\n            // Continue with the next pair\n            }\n        }\n        const createdPairs = {\n            count: insertedCount\n        };\n        return {\n            success: true,\n            insertedCount: createdPairs.count,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving trading pairs:', error);\n        throw error;\n    }\n}\n// Get trading pairs\nasync function getTradingPairs(userId, status = null) {\n    try {\n        const whereClause = {\n            userId\n        };\n        if (status) {\n            whereClause.status = status;\n        }\n        const pairs = await prisma.tradingPair.findMany({\n            where: whereClause,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return pairs;\n    } catch (error) {\n        console.error('Error getting trading pairs:', error);\n        return [];\n    }\n}\n/**\r\n * Excel Data operations\r\n */ // Save Excel data\nasync function saveExcelData(excelData, userId) {\n    try {\n        // Delete all existing Excel data for this user\n        await prisma.excelData.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Create new Excel data\n        const createdExcelData = await prisma.excelData.create({\n            data: {\n                shortOpenTableData: excelData.shortOpenTableData || [],\n                shortLoadedTableData: excelData.shortLoadedTableData || [],\n                longOpenTableData: excelData.longOpenTableData || [],\n                longLoadedTableData: excelData.longLoadedTableData || [],\n                shortClosedTableData: excelData.shortClosedTableData || [],\n                longClosedTableData: excelData.longClosedTableData || [],\n                userId,\n                createdAt: now,\n                updatedAt: now\n            }\n        });\n        return {\n            success: true,\n            insertedId: createdExcelData.id,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving Excel data:', error);\n        throw error;\n    }\n}\n// Get Excel data\nasync function getExcelData(userId) {\n    try {\n        const excelData = await prisma.excelData.findFirst({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return excelData;\n    } catch (error) {\n        console.error('Error getting Excel data:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma-dal.js\n");

/***/ }),

/***/ "(rsc)/./lib/rateLimit.js":
/*!**************************!*\
  !*** ./lib/rateLimit.js ***!
  \**************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit)\n/* harmony export */ });\n// lib/rateLimit.js\n// Simple in-memory rate limiter for development/testing (not for production scale)\nconst store = new Map();\nfunction rateLimit(key, limit = 5, windowMs = 60 * 1000) {\n    const now = Date.now();\n    let entry = store.get(key);\n    if (!entry || now - entry.last > windowMs) {\n        entry = {\n            count: 1,\n            last: now\n        };\n    } else {\n        entry.count += 1;\n    }\n    store.set(key, entry);\n    return entry.count > limit;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcmF0ZUxpbWl0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtQkFBbUI7QUFDbkIsbUZBQW1GO0FBQ25GLE1BQU1BLFFBQVEsSUFBSUM7QUFFWCxTQUFTQyxVQUFVQyxHQUFHLEVBQUVDLFFBQVEsQ0FBQyxFQUFFQyxXQUFXLEtBQUssSUFBSTtJQUM1RCxNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO0lBQ3BCLElBQUlFLFFBQVFSLE1BQU1TLEdBQUcsQ0FBQ047SUFDdEIsSUFBSSxDQUFDSyxTQUFTRixNQUFNRSxNQUFNRSxJQUFJLEdBQUdMLFVBQVU7UUFDekNHLFFBQVE7WUFBRUcsT0FBTztZQUFHRCxNQUFNSjtRQUFJO0lBQ2hDLE9BQU87UUFDTEUsTUFBTUcsS0FBSyxJQUFJO0lBQ2pCO0lBQ0FYLE1BQU1ZLEdBQUcsQ0FBQ1QsS0FBS0s7SUFDZixPQUFPQSxNQUFNRyxLQUFLLEdBQUdQO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVsbGVuXFxPbmVEcml2ZVxcRGVza3RvcFxcRGFzaGJvYXJkXFxTY2h3YWJEYXNoYm9hcmRQcm9qZWN0XFxsaWJcXHJhdGVMaW1pdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBsaWIvcmF0ZUxpbWl0LmpzXHJcbi8vIFNpbXBsZSBpbi1tZW1vcnkgcmF0ZSBsaW1pdGVyIGZvciBkZXZlbG9wbWVudC90ZXN0aW5nIChub3QgZm9yIHByb2R1Y3Rpb24gc2NhbGUpXHJcbmNvbnN0IHN0b3JlID0gbmV3IE1hcCgpO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHJhdGVMaW1pdChrZXksIGxpbWl0ID0gNSwgd2luZG93TXMgPSA2MCAqIDEwMDApIHtcclxuICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gIGxldCBlbnRyeSA9IHN0b3JlLmdldChrZXkpO1xyXG4gIGlmICghZW50cnkgfHwgbm93IC0gZW50cnkubGFzdCA+IHdpbmRvd01zKSB7XHJcbiAgICBlbnRyeSA9IHsgY291bnQ6IDEsIGxhc3Q6IG5vdyB9O1xyXG4gIH0gZWxzZSB7XHJcbiAgICBlbnRyeS5jb3VudCArPSAxO1xyXG4gIH1cclxuICBzdG9yZS5zZXQoa2V5LCBlbnRyeSk7XHJcbiAgcmV0dXJuIGVudHJ5LmNvdW50ID4gbGltaXQ7XHJcbn1cclxuIl0sIm5hbWVzIjpbInN0b3JlIiwiTWFwIiwicmF0ZUxpbWl0Iiwia2V5IiwibGltaXQiLCJ3aW5kb3dNcyIsIm5vdyIsIkRhdGUiLCJlbnRyeSIsImdldCIsImxhc3QiLCJjb3VudCIsInNldCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/rateLimit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fget-saved-symbols%2Froute&page=%2Fapi%2Fget-saved-symbols%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-saved-symbols%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fget-saved-symbols%2Froute&page=%2Fapi%2Fget-saved-symbols%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-saved-symbols%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_get_saved_symbols_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/get-saved-symbols/route.js */ \"(rsc)/./app/api/get-saved-symbols/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/get-saved-symbols/route\",\n        pathname: \"/api/get-saved-symbols\",\n        filename: \"route\",\n        bundlePath: \"app/api/get-saved-symbols/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\api\\\\get-saved-symbols\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_get_saved_symbols_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fget-saved-symbols%2Froute&page=%2Fapi%2Fget-saved-symbols%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-saved-symbols%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Cactions%5C%5CschwabAccess.js%22%2C%5B%7B%22id%22%3A%2200123b7f590fca2fea846ce8bb090c8b4135e9de5d%22%2C%22exportedName%22%3A%22getAccessToken%22%7D%2C%7B%22id%22%3A%2200610d6e30a540305699b3726edfa456794a935d4e%22%2C%22exportedName%22%3A%22retrieveAuthorizationCode%22%7D%2C%7B%22id%22%3A%2200972f29e10d6cd18dd1265b60067912bb6a7a1c2b%22%2C%22exportedName%22%3A%22getAuthorizationCodeURL%22%7D%2C%7B%22id%22%3A%2200a9e585e1dc35eb977066b761d49f1a23fdc16c93%22%2C%22exportedName%22%3A%22storeCustomerId%22%7D%2C%7B%22id%22%3A%2200bf6df4de4448c4552c6f62dbf4ba66009dcad250%22%2C%22exportedName%22%3A%22storeCorrelId%22%7D%2C%7B%22id%22%3A%2200eda339ddc9f1e862059f7d997de8e23129bed7ef%22%2C%22exportedName%22%3A%22refreshToken%22%7D%2C%7B%22id%22%3A%224019d9b6d929f38fb47a1d4a69423a6d858847b0f3%22%2C%22exportedName%22%3A%22storeRefreshToken%22%7D%2C%7B%22id%22%3A%22404f3e3ad78ac2de2901424a14b5c7fe022ce0b303%22%2C%22exportedName%22%3A%22storeAccessTokenFirstLogin%22%7D%2C%7B%22id%22%3A%224075df18647696b0765b2b5d66cadca4e16796a875%22%2C%22exportedName%22%3A%22retrieveRefreshToken%22%7D%2C%7B%22id%22%3A%22409be9f1ad10c10b018021cd0e5c43e04f6a52ec55%22%2C%22exportedName%22%3A%22storeAccessToken%22%7D%2C%7B%22id%22%3A%2240c21480db71c85b8af6ed1b0041f5c14845668e80%22%2C%22exportedName%22%3A%22retrieveAccessToken%22%7D%2C%7B%22id%22%3A%2240d7abcf2a47f382acdd16645f2339ee46da14ba01%22%2C%22exportedName%22%3A%22retrieveCorrelId%22%7D%2C%7B%22id%22%3A%2240f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb%22%2C%22exportedName%22%3A%22retrieveCustomerId%22%7D%2C%7B%22id%22%3A%2240fb93412cb8910c389b9bef1a01cf17e6bb15379b%22%2C%22exportedName%22%3A%22storeAuthorizationCode%22%7D%2C%7B%22id%22%3A%2260325ece34f85a6ed2aff537378b9ed92812b4c97b%22%2C%22exportedName%22%3A%22getAuthorizationCodeCallbackHandler%22%7D%2C%7B%22id%22%3A%2278feca9946baeb980c93461938e15da67fb1f1530b%22%2C%22exportedName%22%3A%22makeApiCall%22%7D%2C%7B%22id%22%3A%227f276eddd716d85ac6647acaf92f150beea2a1298d%22%2C%22exportedName%22%3A%22getMarketData%22%7D%2C%7B%22id%22%3A%227f8d47bfa3f1bd78bb4909dc7270562779ecb727b3%22%2C%22exportedName%22%3A%22getAccounts%22%7D%5D%5D%5D&__client_imported__=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Cactions%5C%5CschwabAccess.js%22%2C%5B%7B%22id%22%3A%2200123b7f590fca2fea846ce8bb090c8b4135e9de5d%22%2C%22exportedName%22%3A%22getAccessToken%22%7D%2C%7B%22id%22%3A%2200610d6e30a540305699b3726edfa456794a935d4e%22%2C%22exportedName%22%3A%22retrieveAuthorizationCode%22%7D%2C%7B%22id%22%3A%2200972f29e10d6cd18dd1265b60067912bb6a7a1c2b%22%2C%22exportedName%22%3A%22getAuthorizationCodeURL%22%7D%2C%7B%22id%22%3A%2200a9e585e1dc35eb977066b761d49f1a23fdc16c93%22%2C%22exportedName%22%3A%22storeCustomerId%22%7D%2C%7B%22id%22%3A%2200bf6df4de4448c4552c6f62dbf4ba66009dcad250%22%2C%22exportedName%22%3A%22storeCorrelId%22%7D%2C%7B%22id%22%3A%2200eda339ddc9f1e862059f7d997de8e23129bed7ef%22%2C%22exportedName%22%3A%22refreshToken%22%7D%2C%7B%22id%22%3A%224019d9b6d929f38fb47a1d4a69423a6d858847b0f3%22%2C%22exportedName%22%3A%22storeRefreshToken%22%7D%2C%7B%22id%22%3A%22404f3e3ad78ac2de2901424a14b5c7fe022ce0b303%22%2C%22exportedName%22%3A%22storeAccessTokenFirstLogin%22%7D%2C%7B%22id%22%3A%224075df18647696b0765b2b5d66cadca4e16796a875%22%2C%22exportedName%22%3A%22retrieveRefreshToken%22%7D%2C%7B%22id%22%3A%22409be9f1ad10c10b018021cd0e5c43e04f6a52ec55%22%2C%22exportedName%22%3A%22storeAccessToken%22%7D%2C%7B%22id%22%3A%2240c21480db71c85b8af6ed1b0041f5c14845668e80%22%2C%22exportedName%22%3A%22retrieveAccessToken%22%7D%2C%7B%22id%22%3A%2240d7abcf2a47f382acdd16645f2339ee46da14ba01%22%2C%22exportedName%22%3A%22retrieveCorrelId%22%7D%2C%7B%22id%22%3A%2240f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb%22%2C%22exportedName%22%3A%22retrieveCustomerId%22%7D%2C%7B%22id%22%3A%2240fb93412cb8910c389b9bef1a01cf17e6bb15379b%22%2C%22exportedName%22%3A%22storeAuthorizationCode%22%7D%2C%7B%22id%22%3A%2260325ece34f85a6ed2aff537378b9ed92812b4c97b%22%2C%22exportedName%22%3A%22getAuthorizationCodeCallbackHandler%22%7D%2C%7B%22id%22%3A%2278feca9946baeb980c93461938e15da67fb1f1530b%22%2C%22exportedName%22%3A%22makeApiCall%22%7D%2C%7B%22id%22%3A%227f276eddd716d85ac6647acaf92f150beea2a1298d%22%2C%22exportedName%22%3A%22getMarketData%22%7D%2C%7B%22id%22%3A%227f8d47bfa3f1bd78bb4909dc7270562779ecb727b3%22%2C%22exportedName%22%3A%22getAccounts%22%7D%5D%5D%5D&__client_imported__=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00123b7f590fca2fea846ce8bb090c8b4135e9de5d\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.getAccessToken),\n/* harmony export */   \"00610d6e30a540305699b3726edfa456794a935d4e\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.retrieveAuthorizationCode),\n/* harmony export */   \"00972f29e10d6cd18dd1265b60067912bb6a7a1c2b\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.getAuthorizationCodeURL),\n/* harmony export */   \"00a9e585e1dc35eb977066b761d49f1a23fdc16c93\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeCustomerId),\n/* harmony export */   \"00bf6df4de4448c4552c6f62dbf4ba66009dcad250\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeCorrelId),\n/* harmony export */   \"00eda339ddc9f1e862059f7d997de8e23129bed7ef\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.refreshToken),\n/* harmony export */   \"4019d9b6d929f38fb47a1d4a69423a6d858847b0f3\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeRefreshToken),\n/* harmony export */   \"404f3e3ad78ac2de2901424a14b5c7fe022ce0b303\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeAccessTokenFirstLogin),\n/* harmony export */   \"4075df18647696b0765b2b5d66cadca4e16796a875\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.retrieveRefreshToken),\n/* harmony export */   \"409be9f1ad10c10b018021cd0e5c43e04f6a52ec55\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeAccessToken),\n/* harmony export */   \"40c21480db71c85b8af6ed1b0041f5c14845668e80\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.retrieveAccessToken),\n/* harmony export */   \"40d7abcf2a47f382acdd16645f2339ee46da14ba01\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.retrieveCorrelId),\n/* harmony export */   \"40f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.retrieveCustomerId),\n/* harmony export */   \"40fb93412cb8910c389b9bef1a01cf17e6bb15379b\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.storeAuthorizationCode),\n/* harmony export */   \"60325ece34f85a6ed2aff537378b9ed92812b4c97b\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.getAuthorizationCodeCallbackHandler),\n/* harmony export */   \"78feca9946baeb980c93461938e15da67fb1f1530b\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.makeApiCall),\n/* harmony export */   \"7f276eddd716d85ac6647acaf92f150beea2a1298d\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.getMarketData),\n/* harmony export */   \"7f8d47bfa3f1bd78bb4909dc7270562779ecb727b3\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__.getAccounts)\n/* harmony export */ });\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_actions_schwabAccess_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/schwabAccess.js */ \"(rsc)/./actions/schwabAccess.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Cactions%5C%5CschwabAccess.js%22%2C%5B%7B%22id%22%3A%2200123b7f590fca2fea846ce8bb090c8b4135e9de5d%22%2C%22exportedName%22%3A%22getAccessToken%22%7D%2C%7B%22id%22%3A%2200610d6e30a540305699b3726edfa456794a935d4e%22%2C%22exportedName%22%3A%22retrieveAuthorizationCode%22%7D%2C%7B%22id%22%3A%2200972f29e10d6cd18dd1265b60067912bb6a7a1c2b%22%2C%22exportedName%22%3A%22getAuthorizationCodeURL%22%7D%2C%7B%22id%22%3A%2200a9e585e1dc35eb977066b761d49f1a23fdc16c93%22%2C%22exportedName%22%3A%22storeCustomerId%22%7D%2C%7B%22id%22%3A%2200bf6df4de4448c4552c6f62dbf4ba66009dcad250%22%2C%22exportedName%22%3A%22storeCorrelId%22%7D%2C%7B%22id%22%3A%2200eda339ddc9f1e862059f7d997de8e23129bed7ef%22%2C%22exportedName%22%3A%22refreshToken%22%7D%2C%7B%22id%22%3A%224019d9b6d929f38fb47a1d4a69423a6d858847b0f3%22%2C%22exportedName%22%3A%22storeRefreshToken%22%7D%2C%7B%22id%22%3A%22404f3e3ad78ac2de2901424a14b5c7fe022ce0b303%22%2C%22exportedName%22%3A%22storeAccessTokenFirstLogin%22%7D%2C%7B%22id%22%3A%224075df18647696b0765b2b5d66cadca4e16796a875%22%2C%22exportedName%22%3A%22retrieveRefreshToken%22%7D%2C%7B%22id%22%3A%22409be9f1ad10c10b018021cd0e5c43e04f6a52ec55%22%2C%22exportedName%22%3A%22storeAccessToken%22%7D%2C%7B%22id%22%3A%2240c21480db71c85b8af6ed1b0041f5c14845668e80%22%2C%22exportedName%22%3A%22retrieveAccessToken%22%7D%2C%7B%22id%22%3A%2240d7abcf2a47f382acdd16645f2339ee46da14ba01%22%2C%22exportedName%22%3A%22retrieveCorrelId%22%7D%2C%7B%22id%22%3A%2240f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb%22%2C%22exportedName%22%3A%22retrieveCustomerId%22%7D%2C%7B%22id%22%3A%2240fb93412cb8910c389b9bef1a01cf17e6bb15379b%22%2C%22exportedName%22%3A%22storeAuthorizationCode%22%7D%2C%7B%22id%22%3A%2260325ece34f85a6ed2aff537378b9ed92812b4c97b%22%2C%22exportedName%22%3A%22getAuthorizationCodeCallbackHandler%22%7D%2C%7B%22id%22%3A%2278feca9946baeb980c93461938e15da67fb1f1530b%22%2C%22exportedName%22%3A%22makeApiCall%22%7D%2C%7B%22id%22%3A%227f276eddd716d85ac6647acaf92f150beea2a1298d%22%2C%22exportedName%22%3A%22getMarketData%22%7D%2C%7B%22id%22%3A%227f8d47bfa3f1bd78bb4909dc7270562779ecb727b3%22%2C%22exportedName%22%3A%22getAccounts%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/has-flag","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fget-saved-symbols%2Froute&page=%2Fapi%2Fget-saved-symbols%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-saved-symbols%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();