"use client";
import { createContext, useContext, useEffect, useState } from "react";
import { io } from "socket.io-client";
import {
  retrieveAccessToken,
  retrieveCustomerId,
  retrieveCorrelId,
  storeCustomerId,
  storeCorrelId
} from "@/actions/schwabAccess";
import { useExcelData } from "@/app/Strategies/WB/configuration/page";

const MarketDataContext = createContext();

export function MarketDataProvider({ children }) {
  const [marketData, setMarketData] = useState([]);
  const [filteredData, setFilteredData] = useState({});
  const [accountData, setAccountData] = useState([]);
  const [accountFilteredData, setAccountFilteredData] = useState({});

  // Accesso al sistema di cache per i dati di mercato
  const { updateMarketData, getMarketData, marketDataCache } = useExcelData() || {};

  // Inizializza i dati dal localStorage all'avvio
  useEffect(() => {
    if (marketDataCache && Object.keys(marketDataCache).length > 0) {
      console.log("🔄 Initializing market data from localStorage:", Object.keys(marketDataCache).length, "symbols");
      console.log("📊 Cached data:", marketDataCache);

      // Aggiorna filteredData con i dati salvati
      setFilteredData(prev => {
        const updatedData = { ...prev };

        Object.keys(marketDataCache).forEach(ticker => {
          const savedData = marketDataCache[ticker];
          if (updatedData[ticker]) {
            // Aggiungi i dati salvati a quelli esistenti
            updatedData[ticker] = {
              ...updatedData[ticker],
              ...(savedData.dividend !== undefined && { dividend: savedData.dividend }),
              ...(savedData.ex_div_date !== undefined && { ex_div_date: savedData.ex_div_date })
            };
          } else {
            // Crea un nuovo entry con i dati salvati
            updatedData[ticker] = {
              ...(savedData.dividend !== undefined && { dividend: savedData.dividend }),
              ...(savedData.ex_div_date !== undefined && { ex_div_date: savedData.ex_div_date })
            };
          }
        });

        return updatedData;
      });
    }
  }, [marketDataCache]);

  useEffect(() => {

    const socket = io("https://localhost:3001", {
      transports: ["websocket"],
    });

    socket.on("connect", () => {
      console.log("Connesso al server Socket.io");
      handleNewToken();
    });

    socket.on('authenticated', (response) => {
      if (response.success) {
        console.log('Socket authenticated successfully');
      } else {
        console.error('Socket authentication failed:', response.error);
      }
    });

    async function handleNewToken() {
      try {
        // Assicurati che il customerId e correlId siano salvati prima di usarli
        await storeCustomerId();
        await storeCorrelId();
        const accessToken = await retrieveAccessToken();
        const customerId = await retrieveCustomerId();
        const correlId = await retrieveCorrelId();

        if (!customerId || !correlId) {
          console.error("CustomerId o correlId non disponibili, devi prima richiederli!");
          return;
        }

        // Send authentication data to WebSocket
        socket.emit('authenticate', {
          customerId: customerId,
          correlId: correlId,
          accessToken: accessToken
        });

        const res = await fetch("https://localhost:3001/init-schwab", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            token: accessToken,
            clientCorrelId: correlId,
            clientCustomerId: customerId
          }),
          credentials: "include",
        });
        await res.json();
      } catch (err) {
        console.error("Errore nell'aggiornare il token:", err);
      }
    }

    socket.on("marketData", (data) => {
      let parsedData;
      try {
        parsedData = JSON.parse(data);
      } catch (error) {
        console.error("Errore nel parsing dei dati:", error);
        return;
      }

      setMarketData((prev) => [...prev, parsedData]);

      if (parsedData.data && Array.isArray(parsedData.data)) {
        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati
        

        parsedData.data.forEach((item) => {
          if (item.content && Array.isArray(item.content)) {
            item.content.forEach((stock) => {
              const ticker = stock.key;
              const value1 = stock["1"];
              const value2 = stock["2"];
              const value3 = stock["3"];
              const value5 = stock["18"];
              const value6 = stock["8"];
              const value7 = stock["22"];
              const value8 = stock["26"];
              if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {
                // Crea un nuovo oggetto per questo ticker con i valori principali
                const tickerData = {
                  bid_prc: value1,
                  ask_prc: value2,
                  last_prc: value3,
                  timestamp: item.timestamp,
                };
                

                // Aggiungi change e volume solo se sono definiti
                if (value5 !== undefined) {
                  tickerData.change = value5;
                }

                if (value6 !== undefined) {
                  tickerData.volume = value6;
                }

                // Aggiungi dividend solo se è definito
                if (value7 !== undefined) {
                  tickerData.dividend = value7;
                }
                if (value8 !== undefined) {
                  tickerData.ex_div_date = value8;
                }

                // Salva dividend e ex-date nel localStorage se sono definiti
                if ((value7 !== undefined || value8 !== undefined) && updateMarketData) {
                  const marketDataToSave = {};
                  if (value7 !== undefined) marketDataToSave.dividend = value7;
                  if (value8 !== undefined) marketDataToSave.ex_div_date = value8;

                  updateMarketData(ticker, marketDataToSave);
                  console.log(`💾 Saved market data for ${ticker}:`, marketDataToSave);
                }

                // Salva l'oggetto completo
                newFilteredData[ticker] = tickerData;
              }
            });
          }
        });


        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati
        setFilteredData((prev) => {
          const updatedData = { ...prev };

          // Per ogni ticker nei nuovi dati
          Object.keys(newFilteredData).forEach(ticker => {
            // Recupera i dati salvati dal localStorage se disponibili
            const savedMarketData = getMarketData ? getMarketData(ticker) : null;

            // Se il ticker esiste già nello stato precedente
            if (updatedData[ticker]) {
              // Crea un nuovo oggetto per questo ticker
              updatedData[ticker] = {
                ...updatedData[ticker], // Mantieni tutti i valori precedenti
                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori
              };

              // Se dividend è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti
              if (newFilteredData[ticker].dividend === undefined) {
                if (savedMarketData?.dividend !== undefined) {
                  updatedData[ticker].dividend = savedMarketData.dividend;
                } else if (updatedData[ticker].dividend !== undefined) {
                  updatedData[ticker].dividend = prev[ticker].dividend;
                }
              }

              // Se ex_div_date è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti
              if (newFilteredData[ticker].ex_div_date === undefined) {
                if (savedMarketData?.ex_div_date !== undefined) {
                  updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;
                } else if (updatedData[ticker].ex_div_date !== undefined) {
                  updatedData[ticker].ex_div_date = prev[ticker].ex_div_date;
                }
              }
            } else {
              // Se è un nuovo ticker, aggiungi i nuovi dati e integra con quelli salvati
              updatedData[ticker] = { ...newFilteredData[ticker] };

              // Aggiungi i dati salvati se non sono presenti nei nuovi dati
              if (savedMarketData) {
                if (updatedData[ticker].dividend === undefined && savedMarketData.dividend !== undefined) {
                  updatedData[ticker].dividend = savedMarketData.dividend;
                }
                if (updatedData[ticker].ex_div_date === undefined && savedMarketData.ex_div_date !== undefined) {
                  updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;
                }
              }
            }
          });

          return updatedData;
        });
      }
    });




    socket.on("disconnect", () => {
      console.log("Disconnesso dal server Socket.io");
    });

    return () => {
      socket.disconnect();
      console.log("Socket disconnesso (cleanup del MarketDataProvider)");
    };
  }, []);

  return (
    <MarketDataContext.Provider value={{ marketData, filteredData }}>
      {children}
    </MarketDataContext.Provider>
  );
}

export function useMarketData() {
  return useContext(MarketDataContext);
}
