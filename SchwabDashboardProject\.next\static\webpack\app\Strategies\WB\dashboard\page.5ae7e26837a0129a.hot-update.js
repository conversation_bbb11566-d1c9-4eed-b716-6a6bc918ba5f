"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/dashboard/page",{

/***/ "(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js":
/*!***************************************************!*\
  !*** ./app/testingWebsocket/MarketDataContext.js ***!
  \***************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataProvider: () => (/* binding */ MarketDataProvider),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Strategies/WB/configuration/page */ \"(app-pages-browser)/./app/Strategies/WB/configuration/page.js\");\n/* __next_internal_client_entry_do_not_use__ MarketDataProvider,useMarketData auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst MarketDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction MarketDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [accountData, setAccountData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accountFilteredData, setAccountFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Accesso al sistema di cache per i dati di mercato\n    const { updateMarketData, getMarketData, marketDataCache } = (0,_app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData)() || {};\n    // Inizializza i dati dal localStorage all'avvio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            if (marketDataCache && Object.keys(marketDataCache).length > 0) {\n                console.log(\"Initializing market data from localStorage:\", marketDataCache);\n                // Aggiorna filteredData con i dati salvati\n                setFilteredData({\n                    \"MarketDataProvider.useEffect\": (prev)=>{\n                        const updatedData = {\n                            ...prev\n                        };\n                        Object.keys(marketDataCache).forEach({\n                            \"MarketDataProvider.useEffect\": (ticker)=>{\n                                const savedData = marketDataCache[ticker];\n                                if (updatedData[ticker]) {\n                                    // Aggiungi i dati salvati a quelli esistenti\n                                    updatedData[ticker] = {\n                                        ...updatedData[ticker],\n                                        ...savedData.dividend !== undefined && {\n                                            dividend: savedData.dividend\n                                        },\n                                        ...savedData.ex_div_date !== undefined && {\n                                            ex_div_date: savedData.ex_div_date\n                                        }\n                                    };\n                                } else {\n                                    // Crea un nuovo entry con i dati salvati\n                                    updatedData[ticker] = {\n                                        ...savedData.dividend !== undefined && {\n                                            dividend: savedData.dividend\n                                        },\n                                        ...savedData.ex_div_date !== undefined && {\n                                            ex_div_date: savedData.ex_div_date\n                                        }\n                                    };\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        return updatedData;\n                    }\n                }[\"MarketDataProvider.useEffect\"]);\n            }\n        }\n    }[\"MarketDataProvider.useEffect\"], [\n        marketDataCache\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"https://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ]\n            });\n            socket.on(\"connect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Connesso al server Socket.io\");\n                    handleNewToken();\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on('authenticated', {\n                \"MarketDataProvider.useEffect\": (response)=>{\n                    if (response.success) {\n                        console.log('Socket authenticated successfully');\n                    } else {\n                        console.error('Socket authentication failed:', response.error);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            async function handleNewToken() {\n                try {\n                    // Assicurati che il customerId e correlId siano salvati prima di usarli\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCustomerId)();\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCorrelId)();\n                    const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                    const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                    const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                    if (!customerId || !correlId) {\n                        console.error(\"CustomerId o correlId non disponibili, devi prima richiederli!\");\n                        return;\n                    }\n                    // Send authentication data to WebSocket\n                    socket.emit('authenticate', {\n                        customerId: customerId,\n                        correlId: correlId,\n                        accessToken: accessToken\n                    });\n                    const res = await fetch(\"https://localhost:3001/init-schwab\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            token: accessToken,\n                            clientCorrelId: correlId,\n                            clientCustomerId: customerId\n                        }),\n                        credentials: \"include\"\n                    });\n                    await res.json();\n                } catch (err) {\n                    console.error(\"Errore nell'aggiornare il token:\", err);\n                }\n            }\n            socket.on(\"marketData\", {\n                \"MarketDataProvider.useEffect\": (data)=>{\n                    let parsedData;\n                    try {\n                        parsedData = JSON.parse(data);\n                    } catch (error) {\n                        console.error(\"Errore nel parsing dei dati:\", error);\n                        return;\n                    }\n                    setMarketData({\n                        \"MarketDataProvider.useEffect\": (prev)=>[\n                                ...prev,\n                                parsedData\n                            ]\n                    }[\"MarketDataProvider.useEffect\"]);\n                    if (parsedData.data && Array.isArray(parsedData.data)) {\n                        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati\n                        parsedData.data.forEach({\n                            \"MarketDataProvider.useEffect\": (item)=>{\n                                if (item.content && Array.isArray(item.content)) {\n                                    item.content.forEach({\n                                        \"MarketDataProvider.useEffect\": (stock)=>{\n                                            const ticker = stock.key;\n                                            const value1 = stock[\"1\"];\n                                            const value2 = stock[\"2\"];\n                                            const value3 = stock[\"3\"];\n                                            const value5 = stock[\"18\"];\n                                            const value6 = stock[\"8\"];\n                                            const value7 = stock[\"22\"];\n                                            const value8 = stock[\"26\"];\n                                            if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {\n                                                // Crea un nuovo oggetto per questo ticker con i valori principali\n                                                const tickerData = {\n                                                    bid_prc: value1,\n                                                    ask_prc: value2,\n                                                    last_prc: value3,\n                                                    timestamp: item.timestamp\n                                                };\n                                                // Aggiungi change e volume solo se sono definiti\n                                                if (value5 !== undefined) {\n                                                    tickerData.change = value5;\n                                                }\n                                                if (value6 !== undefined) {\n                                                    tickerData.volume = value6;\n                                                }\n                                                // Aggiungi dividend solo se è definito\n                                                if (value7 !== undefined) {\n                                                    tickerData.dividend = value7;\n                                                }\n                                                if (value8 !== undefined) {\n                                                    tickerData.ex_div_date = value8;\n                                                }\n                                                // Salva dividend e ex-date nel localStorage se sono definiti\n                                                if ((value7 !== undefined || value8 !== undefined) && updateMarketData) {\n                                                    const marketDataToSave = {};\n                                                    if (value7 !== undefined) marketDataToSave.dividend = value7;\n                                                    if (value8 !== undefined) marketDataToSave.ex_div_date = value8;\n                                                    updateMarketData(ticker, marketDataToSave);\n                                                    console.log(\"Saved market data for \".concat(ticker, \":\"), marketDataToSave);\n                                                }\n                                                // Salva l'oggetto completo\n                                                newFilteredData[ticker] = tickerData;\n                                            }\n                                        }\n                                    }[\"MarketDataProvider.useEffect\"]);\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati\n                        setFilteredData({\n                            \"MarketDataProvider.useEffect\": (prev)=>{\n                                const updatedData = {\n                                    ...prev\n                                };\n                                // Per ogni ticker nei nuovi dati\n                                Object.keys(newFilteredData).forEach({\n                                    \"MarketDataProvider.useEffect\": (ticker)=>{\n                                        // Recupera i dati salvati dal localStorage se disponibili\n                                        const savedMarketData = getMarketData ? getMarketData(ticker) : null;\n                                        // Se il ticker esiste già nello stato precedente\n                                        if (updatedData[ticker]) {\n                                            // Crea un nuovo oggetto per questo ticker\n                                            updatedData[ticker] = {\n                                                ...updatedData[ticker],\n                                                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori\n                                            };\n                                            // Se dividend è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].dividend === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.dividend) !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                } else if (updatedData[ticker].dividend !== undefined) {\n                                                    updatedData[ticker].dividend = prev[ticker].dividend;\n                                                }\n                                            }\n                                            // Se ex_div_date è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].ex_div_date === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.ex_div_date) !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                } else if (updatedData[ticker].ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = prev[ticker].ex_div_date;\n                                                }\n                                            }\n                                        } else {\n                                            // Se è un nuovo ticker, aggiungi i nuovi dati e integra con quelli salvati\n                                            updatedData[ticker] = {\n                                                ...newFilteredData[ticker]\n                                            };\n                                            // Aggiungi i dati salvati se non sono presenti nei nuovi dati\n                                            if (savedMarketData) {\n                                                if (updatedData[ticker].dividend === undefined && savedMarketData.dividend !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                }\n                                                if (updatedData[ticker].ex_div_date === undefined && savedMarketData.ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                }\n                                            }\n                                        }\n                                    }\n                                }[\"MarketDataProvider.useEffect\"]);\n                                return updatedData;\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Disconnesso dal server Socket.io\");\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            return ({\n                \"MarketDataProvider.useEffect\": ()=>{\n                    socket.disconnect();\n                    console.log(\"Socket disconnesso (cleanup del MarketDataProvider)\");\n                }\n            })[\"MarketDataProvider.useEffect\"];\n        }\n    }[\"MarketDataProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MarketDataContext.Provider, {\n        value: {\n            marketData,\n            filteredData\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\testingWebsocket\\\\MarketDataContext.js\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketDataProvider, \"XB21fpjhncuXgJrNPUSPM0AjI2M=\", false, function() {\n    return [\n        _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData\n    ];\n});\n_c = MarketDataProvider;\nfunction useMarketData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MarketDataContext);\n}\n_s1(useMarketData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"MarketDataProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js\n"));

/***/ })

});