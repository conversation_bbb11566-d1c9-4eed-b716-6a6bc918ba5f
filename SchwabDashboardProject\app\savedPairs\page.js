"use client";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export default function SavedPairsPage() {
  const { data: session, status } = useSession();
  const [pairs, setPairs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState("all");

  useEffect(() => {
    const fetchPairs = async () => {
      if (status !== "authenticated") return;

      try {
        setLoading(true);
        const url = filter === "all"
          ? "/api/mongodb/pairs"
          : `/api/mongodb/pairs?status=${filter}`;

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch pairs");
        }

        const data = await response.json();
        // Ensure data.pairs is always an array, even if it's null or undefined
        setPairs(Array.isArray(data.pairs) ? data.pairs : []);
      } catch (err) {
        console.error("Error fetching pairs:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPairs();
  }, [status, filter]);

  if (status === "loading") {
    return <div className="p-4 dark:text-gray-200">Loading session...</div>;
  }

  if (status === "unauthenticated") {
    return <div className="p-4 dark:text-gray-200">Please sign in to view your saved pairs.</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950">
      {/* Header section - Reduced height */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-3 px-4 shadow-md dark:shadow-indigo-950 mb-3">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold">Saved Trading Pairs</h1>
          <p className="text-blue-100 text-sm">View and manage your saved trading pairs</p>
        </div>
      </div>

      <div className="container mx-auto px-4">

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mb-4">
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-2 px-4">
          <h2 className="text-lg font-semibold text-white">Filter Options</h2>
        </div>
        <div className="p-6">
          <div className="flex items-center">
            <label htmlFor="filter" className="mr-3 font-medium text-gray-700 dark:text-gray-200">Filter by status:</label>
            <select
              id="filter"
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border rounded-md p-2 bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
            >
              <option value="all">All Pairs</option>
              <option value="WB_LoadedPairs">Loaded Pairs</option>
              <option value="WB_OpenPositions">Open Positions</option>
              <option value="WB_ClosedPositions">Closed Pairs</option>
            </select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-2 px-4">
            <h2 className="text-lg font-semibold text-white">Loading Data</h2>
          </div>
          <div className="p-12 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading trading pairs...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
          <div className="bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-2 px-4">
            <h2 className="text-lg font-semibold text-white">Error</h2>
          </div>
          <div className="p-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-red-500 dark:text-red-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-medium text-red-600 dark:text-red-400">Error Loading Data</p>
            <p className="text-gray-600 dark:text-gray-300 mt-2">{error}</p>
          </div>
        </div>
      ) : !pairs || pairs.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
          <div className="bg-gradient-to-r from-amber-500 to-orange-600 dark:from-amber-600 dark:to-orange-700 py-2 px-4">
            <h2 className="text-lg font-semibold text-white">No Pairs Found</h2>
          </div>
          <div className="p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-amber-400 dark:text-amber-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-2">No Trading Pairs Found</p>
            <p className="text-gray-600 dark:text-gray-400">There are no saved trading pairs in the database.</p>
            <p className="text-gray-600 dark:text-gray-400 mt-4">You can create pairs in the WB Dashboard and save them to the database.</p>
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
          <div className="bg-gradient-to-r from-green-500 to-teal-600 dark:from-green-600 dark:to-teal-700 py-2 px-4">
            <h2 className="text-lg font-semibold text-white">Trading Pairs</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {pairs.map((pair, index) => (
                <div key={pair._id || pair.id || `pair-${index}`} className="bg-gray-50 dark:bg-gray-800/80 rounded-lg shadow-md dark:shadow-gray-900 overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
                    <span className="font-bold text-gray-800 dark:text-gray-100">
                      {pair.shortComponent?.ticker} / {pair.longComponent?.ticker}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      pair.status === "WB_LoadedPairs"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200"
                        : pair.status === "WB_ClosedPositions"
                          ? "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200"
                          : "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200"
                    }`}>
                      {pair.status === "WB_LoadedPairs" ? "Loaded" : (pair.status === "WB_ClosedPositions" || pair.status === "closed"
                          ? "Closed"
                          : "Open")}
                    </span>
                  </div>

                  <div className="p-4">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                        <h3 className="font-semibold text-red-700 dark:text-red-400 mb-2 border-b border-red-200 dark:border-red-800 pb-1">Short Component</h3>
                        <div className="space-y-1 text-sm">
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Ticker:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.shortComponent?.ticker || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Cost:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.shortComponent?.formattedCost || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Shares:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.shortComponent?.formattedAmt || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Last:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.shortComponent?.formattedLast || 'N/A'}</span>
                          </p>
                        </div>
                      </div>

                      <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                        <h3 className="font-semibold text-green-700 dark:text-green-400 mb-2 border-b border-green-200 dark:border-green-800 pb-1">Long Component</h3>
                        <div className="space-y-1 text-sm">
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Ticker:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.longComponent?.ticker || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Cost:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.longComponent?.formattedCost || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Shares:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.longComponent?.formattedAmt || 'N/A'}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Last:</span>
                            <span className="font-medium text-gray-800 dark:text-gray-200">{pair.longComponent?.formattedLast || 'N/A'}</span>
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                      <p className={`font-bold ${
                        parseFloat(pair.combinedPNL) > 0
                          ? "text-green-600 dark:text-green-400"
                          : parseFloat(pair.combinedPNL) < 0
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-700 dark:text-gray-300"
                      }`}>
                        Combined P/L: {pair.combinedPNL || 'N/A'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Created: {pair.createdAt ? new Date(pair.createdAt).toLocaleString() : 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
