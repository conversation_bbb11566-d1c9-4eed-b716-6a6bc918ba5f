const MarketDataAPIbaseURL = "https://api.schwab.com/marketdata/v1";

//QUOTES

export async function getQuotes(accessToken) {
  const endpoint = "/quotes";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching quotes by list of symbols`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getQuoteBySymbol(symbol_id, accessToken) {
  const endpoint = `/${symbol_id}/quotes`;
  console.log(
    `GET ${MarketDataAPIbaseURL + endpoint} - Fetching quote by single symbol`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//OPTION CHAINS

export async function getOptionChains(accessToken) {
  const endpoint = "/chains";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching option chain for an optionable symbol`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getOptionExpirationChain(accessToken) {
  const endpoint = "/expirationchain";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching option expiration chain for an optionable symbol`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//PRICE HISTORY

export async function getPriceHistory(accessToken) {
  const endpoint = "/pricehistory";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching price history for a single symbol and date ranges`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//MOVERS

export async function getMovers(symbol_id, accessToken) {
  const endpoint = `/movers/${symbol_id}`;
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching movers for a specific index`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//MARKET HOURS

export async function getMarketHours(accessToken) {
  const endpoint = "/markets";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching market hours for different markets`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getMarketHoursByMarketId(market_id, accessToken) {
  const endpoint = `/markets/${market_id}`;
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching market hours for a single market`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

//INSTRUMENTS

export async function getInstruments(accessToken) {
  const endpoint = "/instruments";
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching instruments by symbols and projections`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}

export async function getInstrumentByCusip(cusip_id, accessToken) {
  const endpoint = `/instruments/${cusip_id}`;
  console.log(
    `GET ${
      MarketDataAPIbaseURL + endpoint
    } - Fetching instrument by specific CUSIP`
  );
  const response = await axios.get(MarketDataAPIbaseURL + endpoint, {
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });
  return response.data;
}
