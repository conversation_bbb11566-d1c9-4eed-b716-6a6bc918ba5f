import React, { useState } from 'react';
import { useEndpointAppContext } from '@/components/EndpointAppContext';
import { useEffect } from 'react';
import { accountObserver } from '@/utils/Observer';
import { formatNumber } from '@/utils/formatNumber';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import OrderEntryDialog from '@/components/OrderEntryDialog';
import { useMarketData } from '../testingWebsocket/MarketDataContext';

const PositionsCard = () => {
  const { accountData } = useEndpointAppContext();
  const [positions, setPositions] = useState([]);

  const updatePositions = (data) => {
    // Attach account number to each position
    const extractedPositions = data?.flatMap(acc => {
      const acctNum = acc.securitiesAccount?.accountNumber;
      return (acc.securitiesAccount?.positions || []).map(pos => ({ ...pos, accountNumber: acctNum }));
    });
    setPositions(extractedPositions);
  };


  useEffect(() => {
    if (accountData) {
      updatePositions(accountData);
    }
  }, [accountData]);


  useEffect(() => {
    accountObserver.subscribe(updatePositions);
    return () => {
      accountObserver.unsubscribe(updatePositions);
    };
  }, []);


  const { filteredData: marketData } = useMarketData();
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [orderPrefill, setOrderPrefill] = useState(null);

  if (!positions || positions.length === 0) {
    return (

      <div className="flex items-center justify-center h-full w-full">
        <div className="text-center text-gray-500 dark:text-gray-400 p-4">No positions data</div>


      </div>
    );
  }

  // Helper to open order dialog with prefilled values
  const handleClosePosition = (position) => {
    const symbol = position.instrument?.symbol ?? '';
    const longQty = position.longQuantity || 0;
    const shortQty = position.shortQuantity || 0;
    let side = '';
    let quantity = 0;
    let price = 0;
    // Determine side and qty
    if (longQty > 0) {
      side = 'sell';
      quantity = longQty;
      // Use ask price for sell
      price = marketData && marketData[symbol]?.ask_prc !== undefined ? marketData[symbol].ask_prc : 0;
    } else if (shortQty > 0) {
      side = 'buy';
      quantity = shortQty;
      // Use bid price for buy
      price = marketData && marketData[symbol]?.bid_prc !== undefined ? marketData[symbol].bid_prc : 0;
    }
    setOrderPrefill({
      symbol,
      side,
      quantity,
      orderType: 'limit',
      price,
      account: position.accountNumber || '',
    });
    setOrderDialogOpen(true);
  };

  return (
    <div className="overflow-x-auto">
      <OrderEntryDialog open={orderDialogOpen} setOpen={setOrderDialogOpen} prefill={orderPrefill}>
        {/* The dialog is controlled programmatically, so no trigger button here */}
      </OrderEntryDialog>
      <TooltipProvider delayDuration={20}>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-green-50 dark:bg-green-900">
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-24">Action</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-24">Account #</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-20">Asset Type</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-24">CUSIP</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-20">Symbol</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-20">Short Qty</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-20">Long Qty</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-24">Avg Price</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-28">Market Value</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-28">Current Day P/L</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-28">Short Open P/L</th>
              <th className="border-b-2 border-green-200 dark:border-green-700 py-2 px-1 text-left text-xs font-medium text-green-800 dark:text-green-100 w-28">Long Open P/L</th>
            </tr>
          </thead>
          <tbody>
            {positions.map((position, index) => {
              const fields = [
                position.accountNumber ?? '--',
                position.instrument?.assetType ?? '--',
                position.instrument?.cusip ?? '--',
                position.instrument?.symbol ?? '--',
                formatNumber(position.shortQuantity, 0),
                formatNumber(position.longQuantity, 0),
                formatNumber(position.averagePrice, 2, true),
                formatNumber(position.marketValue, 2, true),
                formatNumber(position.currentDayProfitLoss, 2, true),
                formatNumber(position.shortOpenProfitLoss, 2, true),
                formatNumber(position.longOpenProfitLoss, 2, true),
              ];
              return (
                <tr key={index} className="hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors">
                  <td className="border-b border-green-100 dark:border-green-800 py-2 px-1 text-xs w-24">
                    <button
                      className="px-2 py-1 rounded bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60"
                      onClick={() => handleClosePosition(position)}
                      disabled={!(position.longQuantity > 0 || position.shortQuantity > 0)}
                    >
                      Close Position
                    </button>
                  </td>
                  {fields.map((val, i) => (
                    <td key={i} className="border-b border-green-100 dark:border-green-800 py-2 px-1 text-xs dark:text-gray-200 w-20 max-w-[8rem] truncate">
                      <TooltipProvider delayDuration={20}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate cursor-help">{val ?? '--'}</span>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <span className="break-all">{val ?? '--'}</span>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </TooltipProvider>
    </div>
  );
};


export default PositionsCard;
