/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mongodb/excel/route";
exports.ids = ["app/api/mongodb/excel/route"];
exports.modules = {

/***/ "(rsc)/./app/api/mongodb/excel/route.js":
/*!****************************************!*\
  !*** ./app/api/mongodb/excel/route.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./lib/auth-options.js\");\n/* harmony import */ var _lib_prisma_dal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma-dal */ \"(rsc)/./lib/prisma-dal.js\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./lib/validation.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"40215eb39791c0d37ac34866f712a7cca55e121e6a\":\"POST\",\"40d9d3a57f8247189f4613af6b708c1cc339e3123b\":\"GET\"} */ \n\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Get the current user session\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_4__.authOptions);\n        if (!session || !session.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the database\n        const user = await (0,_lib_prisma_dal__WEBPACK_IMPORTED_MODULE_5__.getUserByEmail)(session.user.email);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Use user.id which is already set in getUserByEmail\n        const userId = user.id;\n        console.log(\"User ID for saving Excel data:\", userId);\n        // Parse the request body\n        const rawData = await request.json();\n        // Validate the Excel data with a lightweight schema\n        const validation = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.validateInput)(_lib_validation__WEBPACK_IMPORTED_MODULE_6__.excelDataSchema, rawData);\n        if (!validation.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Invalid Excel data format\",\n                details: validation.errors\n            }, {\n                status: 400\n            });\n        }\n        const excelData = validation.data;\n        // Sanitize the data (remove any potentially harmful content)\n        const sanitizedData = {\n            shortOpenTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.shortOpenTableData),\n            shortLoadedTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.shortLoadedTableData),\n            longOpenTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.longOpenTableData),\n            longLoadedTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.longLoadedTableData),\n            shortClosedTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.shortClosedTableData),\n            longClosedTableData: (0,_lib_validation__WEBPACK_IMPORTED_MODULE_6__.sanitizeArrayData)(excelData.longClosedTableData)\n        };\n        // Save the Excel data to MongoDB using Prisma\n        const result = await (0,_lib_prisma_dal__WEBPACK_IMPORTED_MODULE_5__.saveExcelData)(sanitizedData, userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            success: true,\n            message: \"Excel data saved successfully. Previous data was overwritten.\",\n            result\n        });\n    } catch (error) {\n        console.error(\"Error saving Excel data:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Failed to save Excel data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Get the current user session\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_4__.authOptions);\n        if (!session || !session.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the database\n        const user = await (0,_lib_prisma_dal__WEBPACK_IMPORTED_MODULE_5__.getUserByEmail)(session.user.email);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Use user.id which is already set in getUserByEmail\n        const userId = user.id;\n        console.log(\"User ID for getting Excel data:\", userId);\n        // Get the Excel data from MongoDB using Prisma\n        const excelData = await (0,_lib_prisma_dal__WEBPACK_IMPORTED_MODULE_5__.getExcelData)(userId);\n        if (!excelData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"No Excel data found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            success: true,\n            excelData: {\n                shortOpenTableData: excelData.shortOpenTableData || [],\n                shortLoadedTableData: excelData.shortLoadedTableData || [],\n                longOpenTableData: excelData.longOpenTableData || [],\n                longLoadedTableData: excelData.longLoadedTableData || [],\n                shortClosedTableData: excelData.shortClosedTableData || [],\n                longClosedTableData: excelData.longClosedTableData || []\n            }\n        });\n    } catch (error) {\n        console.error(\"Error getting Excel data:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Failed to get Excel data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_7__.ensureServerEntryExports)([\n    POST,\n    GET\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(POST, \"40215eb39791c0d37ac34866f712a7cca55e121e6a\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(GET, \"40d9d3a57f8247189f4613af6b708c1cc339e3123b\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mongodb/excel/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth-options.js":
/*!*****************************!*\
  !*** ./lib/auth-options.js ***!
  \*****************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _rateLimit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rateLimit.js */ \"(rsc)/./lib/rateLimit.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma-dal.js */ \"(rsc)/./lib/prisma-dal.js\");\n\n\n\n\nconst CredentialsProvider = next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"] || next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__;\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET,\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    providers: [\n        CredentialsProvider({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\",\n                    required: true\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    required: true\n                }\n            },\n            async authorize (credentials, req) {\n                // Rate limit by IP address (or fallback to email if no IP)\n                let ip = null;\n                if (req && req.headers) {\n                    ip = req.headers[\"x-forwarded-for\"]?.split(\",\")[0]?.trim() || req.headers[\"x-real-ip\"] || req.socket?.remoteAddress;\n                }\n                // fallback to email if no IP (not ideal, but better than nothing)\n                const rateLimitKey = ip || (credentials?.email ? `email:${credentials.email}` : \"unknown\");\n                if ((0,_rateLimit_js__WEBPACK_IMPORTED_MODULE_0__.rateLimit)(rateLimitKey, 5, 60 * 1000)) {\n                    throw new Error(\"Too many login attempts. Please try again in a minute.\");\n                }\n                try {\n                    // Check if credentials are provided\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"Email and password are required\");\n                    }\n                    // Find the user using Prisma\n                    const user = await (0,_prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__.verifyUserCredentials)(credentials.email);\n                    if (!user) {\n                        console.log(\"User not found\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"User found:\", user.email);\n                    // Verify password\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        console.log(\"Invalid password\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"Password verified successfully\");\n                    // Return the user object\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name || user.email.split('@')[0],\n                        role: user.role || 'user'\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw error;\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/generalLogin\",\n        signOut: \"/generalLogin\",\n        error: \"/generalLogin\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user data to the token when signing in\n            if (user) {\n                token.id = user.id;\n                // Explicitly check for null/undefined and set to 'user' if so\n                token.role = user.role === undefined || user.role === null ? 'user' : user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Reject session if token is missing or expired\n            if (!token || !token.id || token.exp && Date.now() / 1000 > token.exp) {\n                return null;\n            }\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role === undefined || token.role === null ? 'user' : token.role;\n            }\n            return session;\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-options.js\n");

/***/ }),

/***/ "(rsc)/./lib/prisma-dal.js":
/*!***************************!*\
  !*** ./lib/prisma-dal.js ***!
  \***************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExcelData: () => (/* binding */ getExcelData),\n/* harmony export */   getTradingPairs: () => (/* binding */ getTradingPairs),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   saveExcelData: () => (/* binding */ saveExcelData),\n/* harmony export */   saveTradingPairs: () => (/* binding */ saveTradingPairs),\n/* harmony export */   verifyUserCredentials: () => (/* binding */ verifyUserCredentials)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\r\n * Prisma Data Access Layer (DAL)\r\n *\r\n * This file provides a layer of abstraction for database operations using Prisma.\r\n * It replaces the direct MongoDB operations in lib/mongodb.js.\r\n */ \n// Create a new PrismaClient instance\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n/**\r\n * User-related operations\r\n */ // Get a user by email\nasync function getUserByEmail(email) {\n    try {\n        console.log('Getting user by email:', email);\n        // Log available models\n        const availableModels = Object.keys(prisma).filter((key)=>!key.startsWith('_') && !key.startsWith('$') && typeof prisma[key] === 'object');\n        console.log('Available models:', availableModels);\n        // Determine which model to use for users\n        let userModel;\n        if (availableModels.includes('users')) {\n            userModel = prisma.users;\n        } else {\n            console.error('No user model found in Prisma client');\n            return null;\n        }\n        // Use the determined model\n        const user = await userModel.findUnique({\n            where: {\n                email\n            }\n        });\n        console.log('User found:', user ? 'Yes' : 'No');\n        if (user) {\n            // With Prisma, the id is already a string\n            // Just make sure we have a consistent property name\n            user.id = user.id || user._id;\n            // Log the user object for debugging\n            console.log('User object:', {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            });\n        }\n        return user;\n    } catch (error) {\n        console.error('Error getting user by email:', error);\n        return null;\n    }\n}\n// Verify user credentials\nasync function verifyUserCredentials(email) {\n    try {\n        const user = await getUserByEmail(email);\n        return user;\n    } catch (error) {\n        console.error('Error verifying user credentials:', error);\n        return null;\n    }\n}\n/**\r\n * Trading Pair operations\r\n */ // Save trading pairs\nasync function saveTradingPairs(pairs, userId) {\n    try {\n        console.log('saveTradingPairs called with pairs:', pairs.length);\n        console.log('saveTradingPairs called with userId:', userId);\n        // Delete all existing pairs for this user\n        await prisma.tradingPair.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Log a sample pair for debugging\n        if (pairs.length > 0) {\n            console.log('Sample pair to save:', {\n                pairKey: pairs[0].pairKey,\n                status: pairs[0].status,\n                combinedPNL: pairs[0].combinedPNL\n            });\n        }\n        // Create all new pairs - we need to handle each pair individually\n        // since createMany doesn't work well with JSON fields in MongoDB\n        let insertedCount = 0;\n        for (const pair of pairs){\n            try {\n                // For pairKey, ensure it's a string or null\n                const pairKeyValue = pair.pairKey ? String(pair.pairKey) : null;\n                // Transform the shortComponent to match the expected schema\n                const shortComponent = {\n                    dividendUserValue: parseInt(pair.shortComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.shortComponent?.dollarCost || 0),\n                    expectedQuantity: pair.shortComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.shortComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.shortComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.shortComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.shortComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.shortComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.shortComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.shortComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.shortComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.shortComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.shortComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.shortComponent?.formattedVolume || \"0\",\n                    id: pair.shortComponent?.id || \"0\",\n                    pnl: parseInt(pair.shortComponent?.pnl || 0),\n                    sectorValue: pair.shortComponent?.sectorValue || \"\",\n                    spreadUserValue: pair.shortComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.shortComponent?.spreadValue || 0,\n                    statusValue: pair.shortComponent?.statusValue || \"\",\n                    ticker: pair.shortComponent?.ticker || \"\"\n                };\n                // Transform the longComponent to match the expected schema\n                const longComponent = {\n                    dividendUserValue: parseInt(pair.longComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.longComponent?.dollarCost || 0),\n                    // expectedQuantity is a Json type in the schema\n                    expectedQuantity: pair.longComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.longComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.longComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.longComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.longComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.longComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.longComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.longComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.longComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.longComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.longComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.longComponent?.formattedVolume || \"0\",\n                    id: pair.longComponent?.id || \"0\",\n                    pnl: parseInt(pair.longComponent?.pnl || 0),\n                    sectorValue: pair.longComponent?.sectorValue || \"\",\n                    // spreadUserValue is a Json type in the schema\n                    spreadUserValue: pair.longComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.longComponent?.spreadValue || 0,\n                    statusValue: pair.longComponent?.statusValue || \"\",\n                    ticker: pair.longComponent?.ticker || \"\"\n                };\n                await prisma.tradingPair.create({\n                    data: {\n                        pairKey: pairKeyValue,\n                        status: pair.status || \"\",\n                        shortComponent: shortComponent,\n                        longComponent: longComponent,\n                        combinedPNL: pair.combinedPNL || \"0\",\n                        userId,\n                        createdAt: now,\n                        updatedAt: now\n                    }\n                });\n                insertedCount++;\n            } catch (error) {\n                console.error(`Error inserting pair:`, error);\n            // Continue with the next pair\n            }\n        }\n        const createdPairs = {\n            count: insertedCount\n        };\n        return {\n            success: true,\n            insertedCount: createdPairs.count,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving trading pairs:', error);\n        throw error;\n    }\n}\n// Get trading pairs\nasync function getTradingPairs(userId, status = null) {\n    try {\n        const whereClause = {\n            userId\n        };\n        if (status) {\n            whereClause.status = status;\n        }\n        const pairs = await prisma.tradingPair.findMany({\n            where: whereClause,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return pairs;\n    } catch (error) {\n        console.error('Error getting trading pairs:', error);\n        return [];\n    }\n}\n/**\r\n * Excel Data operations\r\n */ // Save Excel data\nasync function saveExcelData(excelData, userId) {\n    try {\n        // Delete all existing Excel data for this user\n        await prisma.excelData.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Create new Excel data\n        const createdExcelData = await prisma.excelData.create({\n            data: {\n                shortOpenTableData: excelData.shortOpenTableData || [],\n                shortLoadedTableData: excelData.shortLoadedTableData || [],\n                longOpenTableData: excelData.longOpenTableData || [],\n                longLoadedTableData: excelData.longLoadedTableData || [],\n                shortClosedTableData: excelData.shortClosedTableData || [],\n                longClosedTableData: excelData.longClosedTableData || [],\n                userId,\n                createdAt: now,\n                updatedAt: now\n            }\n        });\n        return {\n            success: true,\n            insertedId: createdExcelData.id,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving Excel data:', error);\n        throw error;\n    }\n}\n// Get Excel data\nasync function getExcelData(userId) {\n    try {\n        const excelData = await prisma.excelData.findFirst({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return excelData;\n    } catch (error) {\n        console.error('Error getting Excel data:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma-dal.js\n");

/***/ }),

/***/ "(rsc)/./lib/rateLimit.js":
/*!**************************!*\
  !*** ./lib/rateLimit.js ***!
  \**************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit)\n/* harmony export */ });\n// lib/rateLimit.js\n// Simple in-memory rate limiter for development/testing (not for production scale)\nconst store = new Map();\nfunction rateLimit(key, limit = 5, windowMs = 60 * 1000) {\n    const now = Date.now();\n    let entry = store.get(key);\n    if (!entry || now - entry.last > windowMs) {\n        entry = {\n            count: 1,\n            last: now\n        };\n    } else {\n        entry.count += 1;\n    }\n    store.set(key, entry);\n    return entry.count > limit;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcmF0ZUxpbWl0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtQkFBbUI7QUFDbkIsbUZBQW1GO0FBQ25GLE1BQU1BLFFBQVEsSUFBSUM7QUFFWCxTQUFTQyxVQUFVQyxHQUFHLEVBQUVDLFFBQVEsQ0FBQyxFQUFFQyxXQUFXLEtBQUssSUFBSTtJQUM1RCxNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO0lBQ3BCLElBQUlFLFFBQVFSLE1BQU1TLEdBQUcsQ0FBQ047SUFDdEIsSUFBSSxDQUFDSyxTQUFTRixNQUFNRSxNQUFNRSxJQUFJLEdBQUdMLFVBQVU7UUFDekNHLFFBQVE7WUFBRUcsT0FBTztZQUFHRCxNQUFNSjtRQUFJO0lBQ2hDLE9BQU87UUFDTEUsTUFBTUcsS0FBSyxJQUFJO0lBQ2pCO0lBQ0FYLE1BQU1ZLEdBQUcsQ0FBQ1QsS0FBS0s7SUFDZixPQUFPQSxNQUFNRyxLQUFLLEdBQUdQO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVsbGVuXFxPbmVEcml2ZVxcRGVza3RvcFxcRGFzaGJvYXJkXFxTY2h3YWJEYXNoYm9hcmRQcm9qZWN0XFxsaWJcXHJhdGVMaW1pdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBsaWIvcmF0ZUxpbWl0LmpzXHJcbi8vIFNpbXBsZSBpbi1tZW1vcnkgcmF0ZSBsaW1pdGVyIGZvciBkZXZlbG9wbWVudC90ZXN0aW5nIChub3QgZm9yIHByb2R1Y3Rpb24gc2NhbGUpXHJcbmNvbnN0IHN0b3JlID0gbmV3IE1hcCgpO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHJhdGVMaW1pdChrZXksIGxpbWl0ID0gNSwgd2luZG93TXMgPSA2MCAqIDEwMDApIHtcclxuICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gIGxldCBlbnRyeSA9IHN0b3JlLmdldChrZXkpO1xyXG4gIGlmICghZW50cnkgfHwgbm93IC0gZW50cnkubGFzdCA+IHdpbmRvd01zKSB7XHJcbiAgICBlbnRyeSA9IHsgY291bnQ6IDEsIGxhc3Q6IG5vdyB9O1xyXG4gIH0gZWxzZSB7XHJcbiAgICBlbnRyeS5jb3VudCArPSAxO1xyXG4gIH1cclxuICBzdG9yZS5zZXQoa2V5LCBlbnRyeSk7XHJcbiAgcmV0dXJuIGVudHJ5LmNvdW50ID4gbGltaXQ7XHJcbn1cclxuIl0sIm5hbWVzIjpbInN0b3JlIiwiTWFwIiwicmF0ZUxpbWl0Iiwia2V5IiwibGltaXQiLCJ3aW5kb3dNcyIsIm5vdyIsIkRhdGUiLCJlbnRyeSIsImdldCIsImxhc3QiLCJjb3VudCIsInNldCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/rateLimit.js\n");

/***/ }),

/***/ "(rsc)/./lib/validation.js":
/*!***************************!*\
  !*** ./lib/validation.js ***!
  \***************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   excelDataSchema: () => (/* binding */ excelDataSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   sanitizeArrayData: () => (/* binding */ sanitizeArrayData),\n/* harmony export */   sanitizeObject: () => (/* binding */ sanitizeObject),\n/* harmony export */   tradingPairsSchema: () => (/* binding */ tradingPairsSchema),\n/* harmony export */   validateInput: () => (/* binding */ validateInput)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n\n/**\r\n * Basic validation schema for login credentials\r\n */ const loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email({\n        message: \"Invalid email address\"\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, {\n        message: \"Password must be at least 6 characters\"\n    })\n});\n/**\r\n * Basic validation schema for Excel data\r\n */ const excelDataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    shortOpenTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([]),\n    shortLoadedTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([]),\n    longOpenTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([]),\n    longLoadedTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([]),\n    shortClosedTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([]),\n    longClosedTableData: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional().default([])\n});\n/**\r\n * Basic validation schema for trading pairs\r\n */ const tradingPairsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pairs: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        status: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional().default(\"\"),\n        shortComponent: zod__WEBPACK_IMPORTED_MODULE_0__.z.any(),\n        longComponent: zod__WEBPACK_IMPORTED_MODULE_0__.z.any(),\n        pairKey: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional(),\n        combinedPNL: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional().default(\"0\")\n    }))\n});\n/**\r\n * Generic input validation function\r\n * @param {object} schema - Zod schema to validate against\r\n * @param {any} data - Data to validate\r\n * @returns {object} - Validation result\r\n */ function validateInput(schema, data) {\n    try {\n        const result = schema.safeParse(data);\n        if (result.success) {\n            return {\n                success: true,\n                data: result.data\n            };\n        } else {\n            console.log(\"Validation error:\", result.error.errors);\n            return {\n                success: false,\n                errors: result.error.errors.map((err)=>({\n                        path: err.path.join('.'),\n                        message: err.message\n                    }))\n            };\n        }\n    } catch (error) {\n        console.error(\"Unexpected validation error:\", error);\n        return {\n            success: false,\n            errors: [\n                {\n                    path: \"unknown\",\n                    message: \"Unexpected validation error\"\n                }\n            ]\n        };\n    }\n}\n/**\r\n * Sanitizes an array of data by removing potentially harmful properties\r\n * @param {Array} array - Array to sanitize\r\n * @returns {Array} - Sanitized array\r\n */ function sanitizeArrayData(array) {\n    if (!Array.isArray(array)) return [];\n    return array.map((item)=>{\n        if (typeof item !== 'object' || item === null) return item;\n        // Create a new object with only safe properties\n        const safeItem = {};\n        for (const [key, value] of Object.entries(item)){\n            // Skip functions or potentially dangerous properties\n            if (typeof value !== 'function' && key !== '__proto__' && key !== 'constructor') {\n                safeItem[key] = value;\n            }\n        }\n        return safeItem;\n    });\n}\n/**\r\n * Sanitizes an object by removing potentially harmful properties\r\n * @param {object} obj - Object to sanitize\r\n * @returns {object} - Sanitized object\r\n */ function sanitizeObject(obj) {\n    if (typeof obj !== 'object' || obj === null) return {};\n    const safeObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // Skip functions or potentially dangerous properties\n        if (typeof value !== 'function' && key !== '__proto__' && key !== 'constructor') {\n            // Handle nested objects\n            if (typeof value === 'object' && value !== null) {\n                if (Array.isArray(value)) {\n                    safeObj[key] = sanitizeArrayData(value);\n                } else {\n                    safeObj[key] = sanitizeObject(value);\n                }\n            } else {\n                safeObj[key] = value;\n            }\n        }\n    }\n    return safeObj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/validation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmongodb%2Fexcel%2Froute&page=%2Fapi%2Fmongodb%2Fexcel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmongodb%2Fexcel%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmongodb%2Fexcel%2Froute&page=%2Fapi%2Fmongodb%2Fexcel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmongodb%2Fexcel%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_mongodb_excel_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mongodb/excel/route.js */ \"(rsc)/./app/api/mongodb/excel/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mongodb/excel/route\",\n        pathname: \"/api/mongodb/excel\",\n        filename: \"route\",\n        bundlePath: \"app/api/mongodb/excel/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\api\\\\mongodb\\\\excel\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_mongodb_excel_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmongodb%2Fexcel%2Froute&page=%2Fapi%2Fmongodb%2Fexcel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmongodb%2Fexcel%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cmongodb%5C%5Cexcel%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%2240215eb39791c0d37ac34866f712a7cca55e121e6a%22%2C%22exportedName%22%3A%22POST%22%7D%2C%7B%22id%22%3A%2240d9d3a57f8247189f4613af6b708c1cc339e3123b%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cmongodb%5C%5Cexcel%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%2240215eb39791c0d37ac34866f712a7cca55e121e6a%22%2C%22exportedName%22%3A%22POST%22%7D%2C%7B%22id%22%3A%2240d9d3a57f8247189f4613af6b708c1cc339e3123b%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"40215eb39791c0d37ac34866f712a7cca55e121e6a\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_mongodb_excel_route_js__WEBPACK_IMPORTED_MODULE_0__.POST),\n/* harmony export */   \"40d9d3a57f8247189f4613af6b708c1cc339e3123b\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_mongodb_excel_route_js__WEBPACK_IMPORTED_MODULE_0__.GET)\n/* harmony export */ });\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_mongodb_excel_route_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/api/mongodb/excel/route.js */ \"(rsc)/./app/api/mongodb/excel/route.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRWxsZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEYXNoYm9hcmQlNUMlNUNTY2h3YWJEYXNoYm9hcmRQcm9qZWN0JTVDJTVDYXBwJTVDJTVDYXBpJTVDJTVDbW9uZ29kYiU1QyU1Q2V4Y2VsJTVDJTVDcm91dGUuanMlMjIlMkMlNUIlN0IlMjJpZCUyMiUzQSUyMjQwMjE1ZWIzOTc5MWMwZDM3YWMzNDg2NmY3MTJhN2NjYTU1ZTEyMWU2YSUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMlBPU1QlMjIlN0QlMkMlN0IlMjJpZCUyMiUzQSUyMjQwZDlkM2E1N2Y4MjQ3MTg5ZjQ2MTNhZjZiNzA4YzFjYzMzOWUzMTIzYiUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMkdFVCUyMiU3RCU1RCU1RCU1RCZfX2NsaWVudF9pbXBvcnRlZF9fPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNpTDtBQUNEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBQT1NUIGFzIFwiNDAyMTVlYjM5NzkxYzBkMzdhYzM0ODY2ZjcxMmE3Y2NhNTVlMTIxZTZhXCIgfSBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcRWxsZW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEYXNoYm9hcmRcXFxcU2Nod2FiRGFzaGJvYXJkUHJvamVjdFxcXFxhcHBcXFxcYXBpXFxcXG1vbmdvZGJcXFxcZXhjZWxcXFxccm91dGUuanNcIlxuZXhwb3J0IHsgR0VUIGFzIFwiNDBkOWQzYTU3ZjgyNDcxODlmNDYxM2FmNmI3MDhjMWNjMzM5ZTMxMjNiXCIgfSBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcRWxsZW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEYXNoYm9hcmRcXFxcU2Nod2FiRGFzaGJvYXJkUHJvamVjdFxcXFxhcHBcXFxcYXBpXFxcXG1vbmdvZGJcXFxcZXhjZWxcXFxccm91dGUuanNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cmongodb%5C%5Cexcel%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%2240215eb39791c0d37ac34866f712a7cca55e121e6a%22%2C%22exportedName%22%3A%22POST%22%7D%2C%7B%22id%22%3A%2240d9d3a57f8247189f4613af6b708c1cc339e3123b%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmongodb%2Fexcel%2Froute&page=%2Fapi%2Fmongodb%2Fexcel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmongodb%2Fexcel%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();