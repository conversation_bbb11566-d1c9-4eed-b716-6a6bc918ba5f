"use client";
import { createContext, useContext, useState, useEffect } from "react";
import { useComponentArray } from "./PairComponent";
import { useExcelData } from "../Strategies/WB/configuration/page";

const PairArrayContext = createContext(null);

export function PairArrayProvider({ children }) {
  const { componentArray } = useComponentArray();
  const { shortOpenTableData, shortLoadedTableData, longOpenTableData, longLoadedTableData, shortClosedTableData, longClosedTableData } = useExcelData();
  const [pairArray, setPairArray] = useState([]);
  const [pairStatus, setPairStatus] = useState({});

  // Generate pairs from component array
  useEffect(() => {
    if (
      !componentArray ||
      !Array.isArray(componentArray.longComponents) ||
      !Array.isArray(componentArray.shortComponents)
    )
      return;

    const { longComponents, shortComponents } = componentArray;
    const minLength = Math.min(longComponents.length, shortComponents.length);
    let pairs = [];
    for (let i = 0; i < minLength; i++) {
      const longComponent = longComponents[i];
      const shortComponent = shortComponents[i];
      const status = longComponent.statusValue;
      const pnlLong = parseFloat(longComponent.formattedPnl || 0);
      const pnlShort = parseFloat(shortComponent.formattedPnl || 0);
      const combinedPNL = (pnlLong + pnlShort).toFixed(2);
      const key = `${shortComponent.ticker}-${longComponent.ticker}-${i}`;
      pairs.push({
        id: i + 1,
        key,
        longComponent,
        shortComponent,
        combinedPNL,
        status,
      });
    }
    setPairArray(pairs);
  }, [componentArray]);

  // Variables for manual saving
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');

  // Function to manually save pairs and Excel data to database
  const savePairsToDatabase = async () => {
    // Skip if already saving
    if (isSaving) {
      setSaveStatus('Already saving data');
      return;
    }

    setIsSaving(true);
    setSaveStatus('Saving data to database...');

    try {
      // Get the current state of pairArray to ensure we're using the most up-to-date version
      console.log('Current pairArray state:', pairArray);
      console.log('pairArray keys:', pairArray.map(pair => pair.key));

      // Save pairs to database
      if (pairArray.length > 0) {
        console.log('Saving pairs to database:', pairArray.length);

        // Create a deep copy of the pairArray to avoid any reference issues
        const pairsToSave = JSON.parse(JSON.stringify(pairArray));

        // Transform the pairs to match the expected schema
        const transformedPairs = pairsToSave.map(pair => ({
          pairKey: pair.key || null,  // Map key to pairKey
          status: pair.status || "",
          shortComponent: pair.shortComponent || {},
          longComponent: pair.longComponent || {},
          combinedPNL: pair.combinedPNL || "0"
        }));

        console.log('Transformed pairs to save:', transformedPairs.length);
        console.log('Sample pair:', transformedPairs[0]);

        const pairsResponse = await fetch('/api/mongodb/pairs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ pairs: transformedPairs }),
        });

        const pairsData = await pairsResponse.json();

        if (!pairsResponse.ok) {
          console.error('Failed to save pairs:', pairsData);
          setSaveStatus(`Error saving pairs: ${pairsData.error || 'Unknown error'}`);
          setIsSaving(false);
          return;
        }

        console.log('Pairs saved successfully:', pairsData);
      } else {
        console.log('No pairs to save, clearing database');
        // Even if there are no pairs, we should still make the API call to clear the database
        const pairsResponse = await fetch('/api/mongodb/pairs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ pairs: [] }),
        });

        const pairsData = await pairsResponse.json();

        if (!pairsResponse.ok) {
          console.error('Failed to clear pairs:', pairsData);
          setSaveStatus(`Error clearing pairs: ${pairsData.error || 'Unknown error'}`);
          setIsSaving(false);
          return;
        }

        console.log('Database cleared successfully:', pairsData);
      }

      // Save Excel data to database
      const excelData = {
        shortOpenTableData,
        shortLoadedTableData,
        longOpenTableData,
        longLoadedTableData,
        shortClosedTableData,
        longClosedTableData,
      };

      console.log('Saving Excel data to database');
      const excelResponse = await fetch('/api/mongodb/excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(excelData),
      });

      const excelResponseData = await excelResponse.json();

      if (!excelResponse.ok) {
        console.error('Failed to save Excel data:', excelResponseData);
        setSaveStatus(`Error saving Excel data: ${excelResponseData.error || 'Unknown error'}`);
      } else {
        console.log('Excel data saved successfully:', excelResponseData);
        setSaveStatus('All data saved successfully to database');
      }
    } catch (error) {
      console.error('Error saving data to database:', error);
      setSaveStatus(`Error: ${error.message || 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <PairArrayContext.Provider value={{
      pairArray,
      setPairArray,
      pairStatus,
      setPairStatus,
      savePairsToDatabase,
      isSaving,
      saveStatus
    }}>
      {children}
    </PairArrayContext.Provider>
  );
}

export function usePairArray() {
  return useContext(PairArrayContext);
}
