"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0297b690c510\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWxsZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxEYXNoYm9hcmRcXFNjaHdhYkRhc2hib2FyZFByb2plY3RcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwMjk3YjY5MGM1MTBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js":
/*!***************************************************!*\
  !*** ./app/testingWebsocket/MarketDataContext.js ***!
  \***************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataProvider: () => (/* binding */ MarketDataProvider),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ MarketDataProvider,useMarketData auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst MarketDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction MarketDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [accountData, setAccountData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accountFilteredData, setAccountFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"https://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ]\n            });\n            socket.on(\"connect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Connesso al server Socket.io\");\n                    handleNewToken();\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on('authenticated', {\n                \"MarketDataProvider.useEffect\": (response)=>{\n                    if (response.success) {\n                        console.log('Socket authenticated successfully');\n                    } else {\n                        console.error('Socket authentication failed:', response.error);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            async function handleNewToken() {\n                try {\n                    // Assicurati che il customerId e correlId siano salvati prima di usarli\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCustomerId)();\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCorrelId)();\n                    const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                    const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                    const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                    if (!customerId || !correlId) {\n                        console.error(\"CustomerId o correlId non disponibili, devi prima richiederli!\");\n                        return;\n                    }\n                    // Send authentication data to WebSocket\n                    socket.emit('authenticate', {\n                        customerId: customerId,\n                        correlId: correlId,\n                        accessToken: accessToken\n                    });\n                    // 🔄 Usa il nuovo endpoint per forzare il refresh della connessione\n                    console.log(\"🔄 Forcing Schwab connection refresh to get fresh data...\");\n                    const res = await fetch(\"https://localhost:3001/refresh-schwab\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            token: accessToken,\n                            clientCorrelId: correlId,\n                            clientCustomerId: customerId\n                        }),\n                        credentials: \"include\"\n                    });\n                    const result = await res.json();\n                    console.log(\"🚀 Schwab refresh response:\", result.message);\n                } catch (err) {\n                    console.error(\"Errore nell'aggiornare il token:\", err);\n                }\n            }\n            socket.on(\"marketData\", {\n                \"MarketDataProvider.useEffect\": (data)=>{\n                    let parsedData;\n                    try {\n                        parsedData = JSON.parse(data);\n                    } catch (error) {\n                        console.error(\"Errore nel parsing dei dati:\", error);\n                        return;\n                    }\n                    setMarketData({\n                        \"MarketDataProvider.useEffect\": (prev)=>[\n                                ...prev,\n                                parsedData\n                            ]\n                    }[\"MarketDataProvider.useEffect\"]);\n                    if (parsedData.data && Array.isArray(parsedData.data)) {\n                        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati\n                        parsedData.data.forEach({\n                            \"MarketDataProvider.useEffect\": (item)=>{\n                                if (item.content && Array.isArray(item.content)) {\n                                    item.content.forEach({\n                                        \"MarketDataProvider.useEffect\": (stock)=>{\n                                            const ticker = stock.key;\n                                            const value1 = stock[\"1\"];\n                                            const value2 = stock[\"2\"];\n                                            const value3 = stock[\"3\"];\n                                            const value5 = stock[\"18\"];\n                                            const value6 = stock[\"8\"];\n                                            const value7 = stock[\"22\"];\n                                            const value8 = stock[\"26\"];\n                                            if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {\n                                                // Crea un nuovo oggetto per questo ticker con i valori principali\n                                                const tickerData = {\n                                                    bid_prc: value1,\n                                                    ask_prc: value2,\n                                                    last_prc: value3,\n                                                    timestamp: item.timestamp\n                                                };\n                                                // Aggiungi change e volume solo se sono definiti\n                                                if (value5 !== undefined) {\n                                                    tickerData.change = value5;\n                                                }\n                                                if (value6 !== undefined) {\n                                                    tickerData.volume = value6;\n                                                }\n                                                // Aggiungi dividend solo se è definito\n                                                if (value7 !== undefined) {\n                                                    tickerData.dividend = value7;\n                                                }\n                                                if (value8 !== undefined) {\n                                                    tickerData.ex_div_date = value8;\n                                                }\n                                                // Salva l'oggetto completo\n                                                newFilteredData[ticker] = tickerData;\n                                            }\n                                        }\n                                    }[\"MarketDataProvider.useEffect\"]);\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati\n                        setFilteredData({\n                            \"MarketDataProvider.useEffect\": (prev)=>{\n                                const updatedData = {\n                                    ...prev\n                                };\n                                // Per ogni ticker nei nuovi dati\n                                Object.keys(newFilteredData).forEach({\n                                    \"MarketDataProvider.useEffect\": (ticker)=>{\n                                        // Se il ticker esiste già nello stato precedente\n                                        if (updatedData[ticker]) {\n                                            // Crea un nuovo oggetto per questo ticker\n                                            updatedData[ticker] = {\n                                                ...updatedData[ticker],\n                                                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori\n                                            };\n                                            // Se dividend è undefined nei nuovi dati ma esiste nei dati precedenti, mantieni il valore precedente\n                                            if (newFilteredData[ticker].dividend === undefined && updatedData[ticker].dividend !== undefined) {\n                                                updatedData[ticker].dividend = prev[ticker].dividend;\n                                            }\n                                        } else {\n                                            // Se è un nuovo ticker, aggiungi semplicemente i nuovi dati\n                                            updatedData[ticker] = newFilteredData[ticker];\n                                        }\n                                    }\n                                }[\"MarketDataProvider.useEffect\"]);\n                                return updatedData;\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Disconnesso dal server Socket.io\");\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            return ({\n                \"MarketDataProvider.useEffect\": ()=>{\n                    socket.disconnect();\n                    console.log(\"Socket disconnesso (cleanup del MarketDataProvider)\");\n                }\n            })[\"MarketDataProvider.useEffect\"];\n        }\n    }[\"MarketDataProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MarketDataContext.Provider, {\n        value: {\n            marketData,\n            filteredData\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\testingWebsocket\\\\MarketDataContext.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketDataProvider, \"SIjasnG+6SRu8lpNP74JVkp7VBE=\");\n_c = MarketDataProvider;\nfunction useMarketData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MarketDataContext);\n}\n_s1(useMarketData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"MarketDataProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js\n"));

/***/ })

});