"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ProtectedRoute */ \"(app-pages-browser)/./components/ProtectedRoute.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    var _session_user, _session_user_email, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isSchwabCallback, setIsSchwabCallback] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isLoggedInToSchwab, setIsLoggedInToSchwab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            console.log(\"HomePage session:\", session, \"status:\", status);\n        }\n    }[\"HomePage.useEffect\"], [\n        session,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if we're on the callback URL\n            const url = new URL(window.location.href);\n            const code = url.searchParams.get(\"code\");\n            if (code) {\n                setIsSchwabCallback(true);\n            }\n            // Always check Schwab login status on page load\n            checkSchwabLogin();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const checkSchwabLogin = async ()=>{\n        try {\n            // Chiedi lo stato Schwab al backend (cookie httpOnly)\n            const res = await fetch(\"/api/schwab-status\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            const data = await res.json();\n            setIsLoggedInToSchwab(data.loggedIn);\n            if (data.loggedIn) {\n                localStorage.setItem('schwabLoggedIn', 'true');\n            } else {\n                localStorage.removeItem('schwabLoggedIn');\n            }\n        } catch (error) {\n            console.error(\"Error checking Schwab login status:\", error);\n        }\n    };\n    const handleLogInToSchwab = async ()=>{\n        try {\n            console.log(\"Getting Auth URL...\");\n            const url = new URL(window.location.href);\n            let code = url.searchParams.get(\"code\");\n            if (!code) {\n                // Set a flag to indicate we're attempting to log in\n                localStorage.setItem('schwabLoginAttempt', 'true');\n                window.location.href = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeURL)();\n                return;\n            }\n            // If we have a code, we're logged in\n            localStorage.setItem('schwabLoggedIn', 'true');\n            setIsLoggedInToSchwab(true);\n        } catch (error) {\n            console.error(\"Error getting OAuth Token:\", error);\n        }\n    };\n    const handleLogOutFromSchwab = ()=>{\n        try {\n            // Clear Schwab-related cookies\n            document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            // Clear localStorage flags\n            localStorage.removeItem('schwabLoggedIn');\n            localStorage.removeItem('schwabLoginAttempt');\n            // Update state\n            setIsLoggedInToSchwab(false);\n            // Refresh the page to ensure all state is reset\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error logging out from Schwab:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        localStorage.clear();\n        sessionStorage.clear();\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/generalLogin\"\n        });\n    // window.location.href = \"/generalLogin\";\n    };\n    // Professional navigation cards with SVG icons\n    const navigationCards = [\n        {\n            title: \"Account Summary\",\n            description: \"Comprehensive portfolio overview and account balances\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this),\n            href: \"/accountsSummary\",\n            bgColor: \"bg-gradient-to-br from-slate-700 to-slate-800\",\n            shadowColor: \"shadow-slate-500/20\",\n            hoverEffect: \"hover:shadow-slate-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Dashboard\",\n            description: \"Real-time trading pair monitoring and management\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/dashboard\",\n            bgColor: \"bg-gradient-to-br from-blue-700 to-blue-800\",\n            shadowColor: \"shadow-blue-500/20\",\n            hoverEffect: \"hover:shadow-blue-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Configuration\",\n            description: \"Advanced strategy setup and parameter optimization\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/configuration\",\n            bgColor: \"bg-gradient-to-br from-indigo-700 to-indigo-800\",\n            shadowColor: \"shadow-indigo-500/20\",\n            hoverEffect: \"hover:shadow-indigo-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Saved Pairs\",\n            description: \"Curated collection of trading pair configurations\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            href: \"/savedPairs\",\n            bgColor: \"bg-gradient-to-br from-emerald-700 to-emerald-800\",\n            shadowColor: \"shadow-emerald-500/20\",\n            hoverEffect: \"hover:shadow-emerald-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"File Handler\",\n            description: \"Data import and file management utilities\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            href: \"/fileHandler\",\n            bgColor: \"bg-gradient-to-br from-gray-700 to-gray-800\",\n            shadowColor: \"shadow-gray-500/20\",\n            hoverEffect: \"hover:shadow-gray-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Account Activity\",\n            description: \"Transaction history and detailed activity logs\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            href: \"/testingAccountActivity\",\n            bgColor: \"bg-gradient-to-br from-purple-700 to-purple-800\",\n            shadowColor: \"shadow-purple-500/20\",\n            hoverEffect: \"hover:shadow-purple-500/40 hover:-translate-y-1\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\\\")\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold bg-white/10 text-white backdrop-blur-sm border border-white/20\",\n                                            children: \"Professional Trading Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight\",\n                                        children: isSchwabCallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-emerald-400\",\n                                                    children: \"Connection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Established\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Investment Management \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 43\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                    children: \"Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto mb-12 leading-relaxed font-light\",\n                                        children: isSchwabCallback ? \"Your Schwab connection is active. Access real-time market data and manage your investment strategies with institutional-grade tools.\" : \"Advanced analytics, real-time data integration, and comprehensive portfolio management tools designed for professional traders and investors.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105\",\n                                        onClick: ()=>handleLogInToSchwab(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-5 w-5 mr-3\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Connect to Schwab\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-5 w-5 ml-3\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/4 left-8 w-32 h-32 border border-white/10 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1/4 right-8 w-24 h-24 border border-blue-400/20 rounded-lg rotate-45\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-1/4 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-slate-800 to-blue-800 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4 sm:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-14 h-14 bg-white/10 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-7 w-7 text-white\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: [\n                                                                \"Welcome, \",\n                                                                ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.split('@')[0])\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2.5 h-2.5 rounded-full mr-2 \".concat(isLoggedInToSchwab ? 'bg-emerald-400' : 'bg-red-400')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-300 text-sm\",\n                                                                    children: isLoggedInToSchwab ? \"Schwab Connected • Real-time data active\" : \"Schwab Disconnected • Limited access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogInToSchwab(),\n                                                    className: \"px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg\",\n                                                    children: \"Connect Schwab\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogOutFromSchwab,\n                                                    className: \"px-4 py-2 text-slate-300 hover:text-white transition-colors text-sm border border-white/20 rounded-lg hover:bg-white/10\",\n                                                    children: \"Disconnect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogout(),\n                                                    className: \"px-4 py-2 text-slate-300 hover:text-white transition-colors text-sm border border-white/20 rounded-lg hover:bg-white/10\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-slate-50 dark:bg-gray-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5 text-blue-600 dark:text-blue-400\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: \"Dashboard Services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-emerald-600 dark:text-emerald-400 font-medium\",\n                                                            children: \"Online\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5 text-purple-600 dark:text-purple-400\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: \"Data Processing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-emerald-600 dark:text-emerald-400 font-medium\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5 text-emerald-600 dark:text-emerald-400\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: \"Market Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-medium \".concat(isLoggedInToSchwab ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'),\n                                                            children: isLoggedInToSchwab ? 'Live' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 248,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Platform Modules\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                    children: \"Comprehensive suite of professional trading and investment management tools designed for institutional-grade performance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: navigationCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                    href: card.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative overflow-hidden rounded-xl \".concat(card.bgColor, \" \").concat(card.shadowColor, \" shadow-xl \").concat(card.hoverEffect, \" transition-all duration-300 cursor-pointer border border-white/10\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20\",\n                                                                children: card.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm group-hover:bg-white/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-3 group-hover:text-blue-200 transition-colors duration-300\",\n                                                        children: card.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/80 text-sm leading-relaxed mb-6\",\n                                                        children: card.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm font-medium text-white/90\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: \"Launch Module\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-px bg-white/40 group-hover:w-6 transition-all duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/0 via-white/3 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-slate-900 via-gray-900 to-slate-900 text-white py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-6\",\n                                    children: \"Ready to Begin?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-300 max-w-3xl mx-auto mb-10 text-lg leading-relaxed\",\n                                    children: \"Access institutional-grade trading tools, real-time market data, and advanced portfolio analytics designed for professional investors.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/dashboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                                children: \"Access Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/configuration\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-8 py-3 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20\",\n                                                children: \"Strategy Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vggbpo6EXw96Kx48ex04Bhn8Jac=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});