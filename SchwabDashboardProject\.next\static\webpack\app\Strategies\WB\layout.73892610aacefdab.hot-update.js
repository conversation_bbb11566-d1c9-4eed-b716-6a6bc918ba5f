"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/layout",{

/***/ "(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js":
/*!***************************************************!*\
  !*** ./app/testingWebsocket/MarketDataContext.js ***!
  \***************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataProvider: () => (/* binding */ MarketDataProvider),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Strategies/WB/configuration/page */ \"(app-pages-browser)/./app/Strategies/WB/configuration/page.js\");\n/* __next_internal_client_entry_do_not_use__ MarketDataProvider,useMarketData auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst MarketDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction MarketDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [accountData, setAccountData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accountFilteredData, setAccountFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Accesso al sistema di cache per i dati di mercato\n    const { updateMarketData, getMarketData, marketDataCache } = (0,_app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData)() || {};\n    // Inizializza i dati dal localStorage all'avvio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            if (marketDataCache && Object.keys(marketDataCache).length > 0) {\n                console.log(\"🔄 Initializing market data from localStorage:\", Object.keys(marketDataCache).length, \"symbols\");\n                console.log(\"📊 Cached data:\", marketDataCache);\n                // Aggiorna filteredData con i dati salvati\n                setFilteredData({\n                    \"MarketDataProvider.useEffect\": (prev)=>{\n                        const updatedData = {\n                            ...prev\n                        };\n                        Object.keys(marketDataCache).forEach({\n                            \"MarketDataProvider.useEffect\": (ticker)=>{\n                                const savedData = marketDataCache[ticker];\n                                if (updatedData[ticker]) {\n                                    // Aggiungi i dati salvati a quelli esistenti\n                                    updatedData[ticker] = {\n                                        ...updatedData[ticker],\n                                        ...savedData.dividend !== undefined && {\n                                            dividend: savedData.dividend\n                                        },\n                                        ...savedData.ex_div_date !== undefined && {\n                                            ex_div_date: savedData.ex_div_date\n                                        }\n                                    };\n                                } else {\n                                    // Crea un nuovo entry con i dati salvati\n                                    updatedData[ticker] = {\n                                        ...savedData.dividend !== undefined && {\n                                            dividend: savedData.dividend\n                                        },\n                                        ...savedData.ex_div_date !== undefined && {\n                                            ex_div_date: savedData.ex_div_date\n                                        }\n                                    };\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        return updatedData;\n                    }\n                }[\"MarketDataProvider.useEffect\"]);\n            }\n        }\n    }[\"MarketDataProvider.useEffect\"], [\n        marketDataCache\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"https://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ]\n            });\n            socket.on(\"connect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Connesso al server Socket.io\");\n                    handleNewToken();\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on('authenticated', {\n                \"MarketDataProvider.useEffect\": (response)=>{\n                    if (response.success) {\n                        console.log('Socket authenticated successfully');\n                    } else {\n                        console.error('Socket authentication failed:', response.error);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            async function handleNewToken() {\n                try {\n                    // Assicurati che il customerId e correlId siano salvati prima di usarli\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCustomerId)();\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCorrelId)();\n                    const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                    const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                    const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                    if (!customerId || !correlId) {\n                        console.error(\"CustomerId o correlId non disponibili, devi prima richiederli!\");\n                        return;\n                    }\n                    // Send authentication data to WebSocket\n                    socket.emit('authenticate', {\n                        customerId: customerId,\n                        correlId: correlId,\n                        accessToken: accessToken\n                    });\n                    const res = await fetch(\"https://localhost:3001/init-schwab\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            token: accessToken,\n                            clientCorrelId: correlId,\n                            clientCustomerId: customerId\n                        }),\n                        credentials: \"include\"\n                    });\n                    await res.json();\n                } catch (err) {\n                    console.error(\"Errore nell'aggiornare il token:\", err);\n                }\n            }\n            socket.on(\"marketData\", {\n                \"MarketDataProvider.useEffect\": (data)=>{\n                    let parsedData;\n                    try {\n                        parsedData = JSON.parse(data);\n                    } catch (error) {\n                        console.error(\"Errore nel parsing dei dati:\", error);\n                        return;\n                    }\n                    setMarketData({\n                        \"MarketDataProvider.useEffect\": (prev)=>[\n                                ...prev,\n                                parsedData\n                            ]\n                    }[\"MarketDataProvider.useEffect\"]);\n                    if (parsedData.data && Array.isArray(parsedData.data)) {\n                        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati\n                        parsedData.data.forEach({\n                            \"MarketDataProvider.useEffect\": (item)=>{\n                                if (item.content && Array.isArray(item.content)) {\n                                    item.content.forEach({\n                                        \"MarketDataProvider.useEffect\": (stock)=>{\n                                            const ticker = stock.key;\n                                            const value1 = stock[\"1\"];\n                                            const value2 = stock[\"2\"];\n                                            const value3 = stock[\"3\"];\n                                            const value5 = stock[\"18\"];\n                                            const value6 = stock[\"8\"];\n                                            const value7 = stock[\"22\"];\n                                            const value8 = stock[\"26\"];\n                                            if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {\n                                                // Crea un nuovo oggetto per questo ticker con i valori principali\n                                                const tickerData = {\n                                                    bid_prc: value1,\n                                                    ask_prc: value2,\n                                                    last_prc: value3,\n                                                    timestamp: item.timestamp\n                                                };\n                                                // Aggiungi change e volume solo se sono definiti\n                                                if (value5 !== undefined) {\n                                                    tickerData.change = value5;\n                                                }\n                                                if (value6 !== undefined) {\n                                                    tickerData.volume = value6;\n                                                }\n                                                // Aggiungi dividend solo se è definito\n                                                if (value7 !== undefined) {\n                                                    tickerData.dividend = value7;\n                                                }\n                                                if (value8 !== undefined) {\n                                                    tickerData.ex_div_date = value8;\n                                                }\n                                                // Salva dividend e ex-date nel localStorage se sono definiti\n                                                if ((value7 !== undefined || value8 !== undefined) && updateMarketData) {\n                                                    const marketDataToSave = {};\n                                                    if (value7 !== undefined) marketDataToSave.dividend = value7;\n                                                    if (value8 !== undefined) marketDataToSave.ex_div_date = value8;\n                                                    updateMarketData(ticker, marketDataToSave);\n                                                    console.log(\"\\uD83D\\uDCBE Saved market data for \".concat(ticker, \":\"), marketDataToSave);\n                                                }\n                                                // Salva l'oggetto completo\n                                                newFilteredData[ticker] = tickerData;\n                                            }\n                                        }\n                                    }[\"MarketDataProvider.useEffect\"]);\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati\n                        setFilteredData({\n                            \"MarketDataProvider.useEffect\": (prev)=>{\n                                const updatedData = {\n                                    ...prev\n                                };\n                                // Per ogni ticker nei nuovi dati\n                                Object.keys(newFilteredData).forEach({\n                                    \"MarketDataProvider.useEffect\": (ticker)=>{\n                                        // Recupera i dati salvati dal localStorage se disponibili\n                                        const savedMarketData = getMarketData ? getMarketData(ticker) : null;\n                                        // Se il ticker esiste già nello stato precedente\n                                        if (updatedData[ticker]) {\n                                            // Crea un nuovo oggetto per questo ticker\n                                            updatedData[ticker] = {\n                                                ...updatedData[ticker],\n                                                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori\n                                            };\n                                            // Se dividend è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].dividend === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.dividend) !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                } else if (updatedData[ticker].dividend !== undefined) {\n                                                    updatedData[ticker].dividend = prev[ticker].dividend;\n                                                }\n                                            }\n                                            // Se ex_div_date è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].ex_div_date === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.ex_div_date) !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                } else if (updatedData[ticker].ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = prev[ticker].ex_div_date;\n                                                }\n                                            }\n                                        } else {\n                                            // Se è un nuovo ticker, aggiungi i nuovi dati e integra con quelli salvati\n                                            updatedData[ticker] = {\n                                                ...newFilteredData[ticker]\n                                            };\n                                            // Aggiungi i dati salvati se non sono presenti nei nuovi dati\n                                            if (savedMarketData) {\n                                                if (updatedData[ticker].dividend === undefined && savedMarketData.dividend !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                }\n                                                if (updatedData[ticker].ex_div_date === undefined && savedMarketData.ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                }\n                                            }\n                                        }\n                                    }\n                                }[\"MarketDataProvider.useEffect\"]);\n                                return updatedData;\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Disconnesso dal server Socket.io\");\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            return ({\n                \"MarketDataProvider.useEffect\": ()=>{\n                    socket.disconnect();\n                    console.log(\"Socket disconnesso (cleanup del MarketDataProvider)\");\n                }\n            })[\"MarketDataProvider.useEffect\"];\n        }\n    }[\"MarketDataProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MarketDataContext.Provider, {\n        value: {\n            marketData,\n            filteredData\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\testingWebsocket\\\\MarketDataContext.js\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketDataProvider, \"XB21fpjhncuXgJrNPUSPM0AjI2M=\", false, function() {\n    return [\n        _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData\n    ];\n});\n_c = MarketDataProvider;\nfunction useMarketData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MarketDataContext);\n}\n_s1(useMarketData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"MarketDataProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js\n"));

/***/ })

});