import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import cors from "cors";
import WebSocket from "ws";
import { validateStockSymbols } from "./lib/server-validation.js";
import rateLimit from "express-rate-limit";
import { RateLimiterMemory } from "rate-limiter-flexible";
import dotenv from "dotenv";
import { getToken } from "next-auth/jwt"; // npm install next-auth
import https from "https";
import { getUserSymbols, setUserSymbols } from './lib/user-symbols.js';
import fs from "fs";
import path from "path";



// Load environment variables
dotenv.config();

// Determine environment
const isDevelopment = process.env.NODE_ENV === "development";

// Set CORS origins based on environment
const corsOrigins = isDevelopment
  ? ["https://127.0.0.1:3000", "http://localhost:3000", "https://localhost:3000"]
  : ["https://zap1.dev"];

console.log(`Running in ${isDevelopment ? "development" : "production"} mode`);
console.log(`CORS origins: ${corsOrigins.join(", ")}`);

const app = express();
app.use(
  cors({
    origin: corsOrigins,
    methods: ["GET", "POST"],
    credentials: true,
  })
);
app.use(express.json());

// Rate limiting middleware for HTTP requests to prevent abuse
const apiLimiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 100, // Limit each IP to 100 requests per window
	standardHeaders: true,
	legacyHeaders: false,
  message: { error: "Too many requests, please try again later." },
});
app.use(apiLimiter);

// Security headers middleware
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Referrer-Policy', 'no-referrer');
  res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
  next();
});

// Load SSL certificates
const privateKey = fs.readFileSync("certificates/localhost-key.pem", "utf8");
const certificate = fs.readFileSync("certificates/localhost.pem", "utf8");
const credentials = { key: privateKey, cert: certificate };

const server = https.createServer(credentials, app);
const io = new Server(server, {
  cors: {
    origin: corsOrigins,
    methods: ["GET", "POST"],
    credentials: true,
  },
  // Mitigate DoS attacks by limiting the size of incoming payloads.
  maxHttpBufferSize: 1e5, // 100 KB, adjust as needed
});

const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET;
const userConnections = {}; // { customerId: { schwabSocket, accessToken, correlId, symbols } }
const userSocketMap = {}; // { socketId: customerId }

// Database-based symbol management (replacing file-based system)







// Rate limiting for WebSocket connections
const wsConnectionLimiter = new RateLimiterMemory({
  points: 10, // 10 connection attempts
  duration: 60, // per minute from a single IP
});

// Rate limiter for file operations (symbols update)
const fileOperationLimiter = new RateLimiterMemory({
  points: 10, // 10 file operations
  duration: 60, // per 60 seconds per user
});

// Custom middleware to extract session data from request
function extractSession(req, res, next) {
  // Try to get token, correlId, customerId from body, headers, or cookies
  const token = req.body.token || req.headers["authorization"]?.replace("Bearer ", "") || req.cookies?.access_token;
  const clientCorrelId = req.body.clientCorrelId || req.headers["x-client-correlid"] || req.cookies?.client_correlId;
  const clientCustomerId = req.body.clientCustomerId || req.headers["x-client-customerid"] || req.cookies?.client_customerId;
  const userEmail = req.body.userEmail || req.headers["x-user-email"] || req.cookies?.user_email;

  // Attach to req.user for downstream use
  req.user = {
    token,
    correlId: clientCorrelId,
    customerId: clientCustomerId,
    email: userEmail
  };

  // If any are missing, return 401
  if (!token || !clientCorrelId || !clientCustomerId) {
    return res.status(401).json({ error: "Missing session data", details: { token, clientCorrelId, clientCustomerId } });
  }
  next();
}

// Middleware to check NextAuth session
async function requireAuth(req, res, next) {
  const token = await getToken({ req, secret: NEXTAUTH_SECRET });
  if (!token || !token.sub) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  req.user = token;
  next();
}

io.use(async (socket, next) => {
  try {
    // Use the real IP address if behind a proxy, otherwise fallback to the remote address.
    const ip = socket.handshake.headers["x-forwarded-for"] || socket.handshake.address;
    await wsConnectionLimiter.consume(ip);
  } catch (e) {
    return next(new Error("Too many connection attempts. Please try again later."));
  }

  const token = await getToken({ req: socket.request, secret: NEXTAUTH_SECRET });

  if (!token || !token.sub) {
    // Alternative: Allow connection but require manual auth
    socket.user = { sub: 'pending', customerId: null };
    socket.authenticated = false;
    userSocketMap[socket.id] = 'pending';
    return next();
  }

  socket.user = token;
  socket.authenticated = true;
  userSocketMap[socket.id] = token.customerId;
  next();
});

io.on("connection", (socket) => {
  if (!socket.authenticated) {
    console.log(`Client Socket.io connected: ${socket.id} - PENDING AUTH`);

    // Listen for manual authentication
    socket.on('authenticate', async (authData) => {
      try {
        // Verify the auth data
        if (authData && authData.customerId && authData.correlId) {
          socket.user = {
            sub: authData.customerId,
            customerId: authData.customerId,
            correlId: authData.correlId,
            accessToken: authData.accessToken
          };
          socket.authenticated = true;
          userSocketMap[socket.id] = authData.customerId;
          socket.join(authData.customerId);
          socket.emit('authenticated', { success: true });
          console.log(`Manual auth success for ${authData.customerId}`);

          // Now handle the authenticated socket like a normal connection
          handleAuthenticatedSocket(socket);
        } else {
          socket.emit('authenticated', { success: false, error: 'Invalid auth data' });
        }
      } catch (error) {
        console.error('Auth error:', error);
        socket.emit('authenticated', { success: false, error: 'Auth failed' });
      }
    });

    return;
  }

  const customerId = socket.user.customerId;
  console.log(`Client Socket.io connected: ${socket.id} for customer ${customerId}`);
  socket.join(customerId); // Join a room specific to this customer

  handleAuthenticatedSocket(socket);
});

// Function to handle authenticated sockets (both NextAuth and manual)
function handleAuthenticatedSocket(socket) {
  const customerId = socket.user.customerId;

  socket.on("disconnect", () => {
    console.log(`Client Socket.io disconnected: ${socket.id} for customer ${customerId}`);
    delete userSocketMap[socket.id];

    // Use a timeout to see if this was the last connection for the user.
    // This prevents closing the Schwab connection on a temporary network blip or page refresh.
    setTimeout(async () => {
      const room = io.sockets.adapter.rooms.get(customerId);
      if ((!room || room.size === 0) && userConnections[customerId]) {
        console.log(`Last client for ${customerId} disconnected. Closing Schwab connection.`);
        userConnections[customerId].schwabSocket.close();
      }
    }, 5000); // 5-second grace period
  });
}

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Server listening on https://localhost:${PORT}`);
});

const schwabWsUrl = "wss://streamer-api.schwab.com/ws";

function manageSchwabConnection(customerId, accessToken, correlId, symbols) {
  if (userConnections[customerId] && userConnections[customerId].schwabSocket.readyState === WebSocket.OPEN) {
    console.log(`Schwab connection already active for customer ${customerId}`);
    return;
  }

  console.log(`Creating new Schwab connection for customer ${customerId}`);
  const schwabSocket = new WebSocket(schwabWsUrl);

  // Store the connection details
  userConnections[customerId] = {
    schwabSocket,
    accessToken,
    correlId,
    symbols,
    heartbeatInterval: null,
    isAlive: true,
    missedHeartbeats: 0,
    maxMissedHeartbeats: 3, // Allow 3 missed heartbeats before terminating
  };

  schwabSocket.on("open", () => {
    console.log(`Connected to Schwab WebSocket for customer ${customerId}`);

    const loginRequest = {
      requestid: "1",
      service: "ADMIN",
      command: "LOGIN",
      SchwabClientCustomerId: customerId,
      SchwabClientCorrelId: correlId,
      parameters: {
        Authorization: accessToken,
        SchwabClientChannel: "IO",
        SchwabClientFunctionId: "Tradeticket",
      },
    };

    schwabSocket.send(JSON.stringify({ requests: [loginRequest] }));
  });

  schwabSocket.on("message", (data) => {
    const message = data.toString();
    //console.log(`Data received from Schwab for ${customerId}:`, message);

    try { // The rest of this logic remains largely the same, but targets the specific user
      const parsedMessage = JSON.parse(message);


      if (
        parsedMessage.response &&
        parsedMessage.response[0].service === "ADMIN" &&
        parsedMessage.response[0].command === "LOGIN"
      ) {
        const loginContent = parsedMessage.response[0].content;
        if (loginContent && loginContent.code === 0) {
          console.log(`Schwab login successful for customer ${customerId}`);

          // Start heartbeat mechanism
          const connection = userConnections[customerId];
          if (connection) {
            connection.isAlive = true;
            connection.missedHeartbeats = 0;
            clearInterval(connection.heartbeatInterval); // Clear any old interval

            connection.heartbeatInterval = setInterval(() => {
              if (connection.isAlive === false) {
                connection.missedHeartbeats++;
                console.log(`Missed heartbeat ${connection.missedHeartbeats}/${connection.maxMissedHeartbeats} for customer ${customerId}`);

                if (connection.missedHeartbeats >= connection.maxMissedHeartbeats) {
                  console.error(`Heartbeat timeout for customer ${customerId}. Terminating connection.`);
                  return connection.schwabSocket.terminate(); // Force close
                }
              } else {
                // Reset missed heartbeats if we got a response
                connection.missedHeartbeats = 0;
              }

              connection.isAlive = false; // Assume connection is dead until we get a pong
              const heartbeatRequest = {
                service: "ADMIN",
                requestid: `hb-${Date.now()}`,
                command: "HEARTBEAT",
                SchwabClientCustomerId: customerId,
                SchwabClientCorrelId: correlId,
              };

              try {
                connection.schwabSocket.send(JSON.stringify({ requests: [heartbeatRequest] }));
                console.log(`Sent heartbeat to Schwab for customer ${customerId}`);
              } catch (error) {
                console.error(`Failed to send heartbeat for customer ${customerId}:`, error.message);
                connection.missedHeartbeats++;
              }
            }, 45000); // Send heartbeat every 45 seconds (increased from 30)
          }

          const subsRequest = {
            requestid: "3",
            service: "LEVELONE_EQUITIES",
            command: "SUBS",
            SchwabClientCustomerId: customerId,
            SchwabClientCorrelId: correlId,
            parameters: {
              keys: symbols,
              fields: "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30"
            },
          };

            schwabSocket.send(JSON.stringify({ requests: [subsRequest] }));
        } else {
          console.error("Schwab login failed for customer", customerId, ":", loginContent.msg);
          console.error("Full login response:", loginContent);
          io.to(customerId).emit("schwabError", { error: "Schwab login failed", details: loginContent.msg });
        }
      } else if (
        parsedMessage.notify &&
        parsedMessage.notify[0].service === "HEARTBEAT"
      ) {
        if (userConnections[customerId]) {
          userConnections[customerId].isAlive = true;
          userConnections[customerId].missedHeartbeats = 0; // Reset missed count
          console.log(`Heartbeat received from Schwab for customer ${customerId}`);
        }
      } else if (
        parsedMessage.response &&
        parsedMessage.response[0].service === "ADMIN" &&
        parsedMessage.response[0].command === "HEARTBEAT"
      ) {
        // Handle heartbeat response (not just notify)
        if (userConnections[customerId]) {
          userConnections[customerId].isAlive = true;
          userConnections[customerId].missedHeartbeats = 0; // Reset missed count
          console.log(`Heartbeat response received from Schwab for customer ${customerId}`);
        }
      } else if (
        parsedMessage.response &&
        parsedMessage.response[0].service === "ACCT_ACTIVITY"
      ) {
        // Emit account data only to the specific user's room
        io.to(customerId).emit("accountData", message);
      } else {
        // Emit market data only to the specific user's room
        io.to(customerId).emit("marketData", message);
      }
    } catch (error) {
      console.error(`Error parsing message for ${customerId}:`, error);
    }
  });

  schwabSocket.on("error", (error) => {
    console.error(`Schwab WebSocket error for customer ${customerId}:`, error);
    io.to(customerId).emit("schwabError", { error: "Schwab connection error" });
  });

  schwabSocket.on("close", (code, reason) => {
    const reasonText = reason ? reason.toString() : 'No reason given';
    console.log(`Schwab WebSocket for customer ${customerId} closed. Code: ${code}, Reason: ${reasonText}`);

    // Clean up heartbeat interval and connection state
    const connection = userConnections[customerId];
    if (connection) {
      clearInterval(connection.heartbeatInterval);
    }

    // Check if there are still active Socket.IO clients for this user
    const room = io.sockets.adapter.rooms.get(customerId);
    if (room && room.size > 0) {
      console.log(`Attempting to reconnect Schwab WebSocket for customer ${customerId} (${room.size} clients connected)`);

      // Attempt reconnection after a delay
      setTimeout(() => {
        try {
          console.log(`Reconnecting Schwab WebSocket for customer ${customerId}...`);
          manageSchwabConnection(customerId, connection.accessToken, connection.correlId, connection.symbols);
        } catch (reconnectError) {
          console.error(`Failed to reconnect for customer ${customerId}:`, reconnectError.message);
          io.to(customerId).emit("schwabStatus", {
            status: "disconnected",
            message: "Schwab connection lost and reconnection failed."
          });
        }
      }, 5000); // Wait 5 seconds before reconnecting
    } else {
      // No active clients, clean up completely
      delete userConnections[customerId];
      console.log(`No active clients for customer ${customerId}, connection cleaned up`);
    }

    io.to(customerId).emit("schwabStatus", { status: "disconnected", message: "Schwab connection closed." });
  });

  schwabSocket.on("error", (error) => {
    console.error(`Schwab WebSocket error for customer ${customerId}:`, error.message);

    // Don't immediately close on error, let the close handler deal with it
    io.to(customerId).emit("schwabStatus", {
      status: "error",
      message: `Schwab connection error: ${error.message}`
    });
  });

  schwabSocket.on("pong", () => {
    // Handle WebSocket pong (if Schwab sends them)
    if (userConnections[customerId]) {
      userConnections[customerId].isAlive = true;
      userConnections[customerId].missedHeartbeats = 0;
      console.log(`Pong received from Schwab for customer ${customerId}`);
    }
  });
}

app.post("/init-schwab", extractSession, async (req, res) => {
  const { token, correlId, customerId } = req.user;

  // Validate required parameters
  if (!token || !correlId || !customerId) {
    return res.status(400).json({
      error: "Missing parameters",
      message: "token, correlId, and customerId are required"
    });
  }

  // If a connection already exists, do nothing.
  if (userConnections[customerId]) {
    console.log(`Connection already active for ${customerId}.`);
    return res.json({ message: "Schwab connection already active" });
  }

  // Get saved symbols for this user or use defaults
  const initialSymbols = await getUserSymbols(customerId);
  console.log(`Loading saved symbols for user`);

  manageSchwabConnection(customerId, token, correlId, initialSymbols);

  return res.json({ message: "Schwab connection initiated" });
});

// Endpoint to update stock symbols
app.post("/update-stock-symbols", extractSession, async (req, res) => {
  try {
    // Apply rate limiting for file operations
    await fileOperationLimiter.consume(req.user.customerId);
  } catch (rateLimitError) {
    return res.status(429).json({ error: "Too many symbol updates. Please try again later." });
  }
  try {
    console.log('Received POST request to /update-stock-symbols');
    const { token, correlId, customerId } = req.user;
    const { symbols } = req.body;

    // Validate required parameters
    if (!token || !correlId || !customerId) {
      return res.status(400).json({
        error: "Missing parameters",
        message: "token, correlId, and customerId are required"
      });
    }

    // Validate stock symbols
    const validation = validateStockSymbols(symbols);
    if (!validation.valid) {
      console.log('Validation error:', validation.message);
      return res.status(400).json({
        error: "Invalid symbols",
        message: validation.message
      });
    }

    const connection = userConnections[customerId];
    if (connection) {
      console.log(`- Socket exists: ${!!connection.schwabSocket}`);
      if (connection.schwabSocket) {
        console.log(`- Socket state: ${connection.schwabSocket.readyState} (OPEN=${WebSocket.OPEN})`);
      }
    }

    if (!connection || !connection.schwabSocket || connection.schwabSocket.readyState !== WebSocket.OPEN) {
      return res.status(400).json({ error: "Schwab connection not active for this user." });
    }

    const { schwabSocket } = connection;
    // Use correlId from session/body
    connection.correlId = correlId;
    const currentSymbolsSet = new Set(connection.symbols ? connection.symbols.split(',') : []);
    const newSymbolsSet = new Set(validation.sanitizedSymbols);

    const symbolsToUnsubscribe = [...currentSymbolsSet].filter(s => !newSymbolsSet.has(s));
    const symbolsToSubscribe = [...newSymbolsSet].filter(s => !currentSymbolsSet.has(s));

    // Send UNSUBS request for symbols that are no longer needed
    if (symbolsToUnsubscribe.length > 0) {
      const unsubRequest = {
        requestid: `unsub-${Date.now()}`,
        service: "LEVELONE_EQUITIES",
        command: "UNSUBS",
        SchwabClientCustomerId: customerId,
        SchwabClientCorrelId: correlId,
        parameters: {
          keys: symbolsToUnsubscribe.join(','),
        },
      };
      schwabSocket.send(JSON.stringify({ requests: [unsubRequest] }));
      console.log(`Sent UNSUBS request for ${customerId}:`, symbolsToUnsubscribe.join(','));
    }

    // Send SUBS request for new symbols
    if (symbolsToSubscribe.length > 0) {
      const subsRequest = {
        requestid: `sub-${Date.now()}`,
        service: "LEVELONE_EQUITIES",
        command: "SUBS",
        SchwabClientCustomerId: customerId,
        SchwabClientCorrelId: correlId,
        parameters: {
          keys: symbolsToSubscribe.join(','),
          fields: "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30"
        },
      };
      schwabSocket.send(JSON.stringify({ requests: [subsRequest] }));
      console.log(`Sent SUBS request for ${customerId}:`, symbolsToSubscribe.join(','));
    }

    // Update the stored symbols for the connection
    const updatedSymbolList = [...newSymbolsSet].join(',');
    connection.symbols = updatedSymbolList;

    // Save symbols persistently to database
    try {
      const { email } = req.user;
      if (!email) {
        console.warn('No user email provided, symbols not saved to database');
      } else {
        await setUserSymbols(customerId, updatedSymbolList, email);
        console.log(`Symbols updated and saved for user`);
      }
    } catch (saveError) {
      console.error('Failed to save symbols:', saveError.message);
      return res.status(500).json({ error: "Failed to save symbols persistently" });
    }

    return res.json({ message: "Symbol subscriptions updated successfully", symbols: updatedSymbolList });
  } catch (error) {
    console.error('Errore durante l\'aggiornamento dei simboli:', error);
    return res.status(500).json({ error: "Errore interno del server" });
  }
});

// Endpoint to get saved symbols for a user
app.get("/get-user-symbols", extractSession, async (req, res) => {
  try {
    // Apply rate limiting
    await fileOperationLimiter.consume(req.user.customerId);
  } catch (rateLimitError) {
    return res.status(429).json({ error: "Too many requests. Please try again later." });
  }
  try {
    const { customerId } = req.user;

    if (!customerId) {
      return res.status(400).json({
        error: "Missing customerId"
      });
    }

    const symbols = await getUserSymbols(customerId);

    // Sanitize response - don't expose internal structure
    const symbolsArray = symbols.split(',').map(s => s.trim()).filter(s => s.length > 0);

    return res.json({
      symbols: symbolsArray.join(','),
      symbolsArray: symbolsArray,
      count: symbolsArray.length,
      source: 'database'
    });
  } catch (error) {
    console.error('Error getting user symbols:', error.message);
    return res.status(500).json({ error: "Failed to retrieve symbols" });
  }
});

// Test endpoint to verify server is working
app.get("/test", (_req, res) => {
  console.log('Received GET request to /test');
  return res.json({
    message: "Server is working",
    activeConnections: Object.keys(userConnections).length,
    timestamp: new Date().toISOString()
  });
});
/*app.post("/api/account-activity", (req, res) => {
  const { SchwabClientCustomerId, SchwabClientCorrelId } = req.body;
  if (!SchwabClientCustomerId || !SchwabClientCorrelId) {
    return res.status(400).json({ error: "Parametri mancanti" });
  }
  const acctRequest = {
    requestid: "2",
    service: "ACCT_ACTIVITY",
    command: "SUBS",
    SchwabClientCustomerId: SchwabClientCustomerId,
    SchwabClientCorrelId: SchwabClientCorrelId,
    parameters: {
      keys: "Account Activity",
      fields: "0,1,2,3",
    },
  };

  if (schwabSocket && schwabSocket.readyState === WebSocket.OPEN) {
    schwabSocket.send(JSON.stringify({ requests: [acctRequest] }));
    console.log("Richiesta ACCT_ACTIVITY inviata:", acctRequest);
    return res.json({ message: "Richiesta di subscription inviata" });
  } else {
    return res.status(500).json({ error: "Connessione WebSocket non disponibile" });
  }
}); */
