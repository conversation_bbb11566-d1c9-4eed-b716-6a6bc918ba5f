/**
 * Generates a Content Security Policy (CSP) to protect the application from XSS attacks
 * @returns {string} - The CSP policy
 */
export function generateCSP() {
  // Use environment variable to determine development mode
  // Set CSP_IS_DEVELOPMENT in your .env file to 'true' or 'false'
  const isDevelopment = process.env.CSP_IS_DEVELOPMENT === 'true';

  console.log(`CSP: Running in ${isDevelopment ? 'development' : 'production'} mode`);

  // Common directives for both development and production
  const commonDirectives = {
    'default-src': ["'self'"],
    'style-src': ["'self'", "'unsafe-inline'"], // unsafe-inline needed for Next.js in both environments
    'img-src': ["'self'", "data:", "blob:"],
    'connect-src': ["'self'", "https://streamer-api.schwab.com", "wss://streamer-api.schwab.com", "wss://localhost:3001", "https://localhost:3001", "https://zap1.dev", "wss://zap1.dev"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"], // Prevents clickjacking
    'object-src': ["'none'"], // Prevents object/embed tags
    'base-uri': ["'self'"], // Restricts base tags
  };

  // Environment-specific directives
  const developmentDirectives = {
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // unsafe-eval needed for hot reloading in development
  };

  const productionDirectives = {
    'script-src': ["'self'", "'unsafe-inline'"], // unsafe-inline still needed for Next.js in production
  };

  // Combine common directives with environment-specific ones
  const policy = {
    ...commonDirectives,
    ...(isDevelopment ? developmentDirectives : productionDirectives)
  };

  // Convert the policy object to a string
  const cspString = Object.entries(policy)
    .map(([key, values]) => `${key} ${values.join(' ')}`)
    .join('; ');

  return cspString;
}
