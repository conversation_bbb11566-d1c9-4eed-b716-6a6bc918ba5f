"use client";

import { MarketDataProvider } from "@/app/testingWebsocket/MarketDataContext";
import { EndpointAppProvider } from "@/components/EndpointAppContext";
import { ComponentArrayProvider } from "@/app/pairArray/PairComponent";
import { PairArrayProvider } from "@/app/pairArray/PairArray";

export default function WBLayout({ children }) {
  return (
    <EndpointAppProvider>
      <MarketDataProvider>
        <ComponentArrayProvider>
          <PairArrayProvider>
            {children}
          </PairArrayProvider>
        </ComponentArrayProvider>
      </MarketDataProvider>
    </EndpointAppProvider>
  );
}
