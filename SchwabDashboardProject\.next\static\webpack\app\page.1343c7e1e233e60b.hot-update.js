"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ProtectedRoute */ \"(app-pages-browser)/./components/ProtectedRoute.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isSchwabCallback, setIsSchwabCallback] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isLoggedInToSchwab, setIsLoggedInToSchwab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            console.log(\"HomePage session:\", session, \"status:\", status);\n        }\n    }[\"HomePage.useEffect\"], [\n        session,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if we're on the callback URL\n            const url = new URL(window.location.href);\n            const code = url.searchParams.get(\"code\");\n            if (code) {\n                setIsSchwabCallback(true);\n            }\n            // Always check Schwab login status on page load\n            checkSchwabLogin();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const checkSchwabLogin = async ()=>{\n        try {\n            // Chiedi lo stato Schwab al backend (cookie httpOnly)\n            const res = await fetch(\"/api/schwab-status\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            const data = await res.json();\n            setIsLoggedInToSchwab(data.loggedIn);\n            if (data.loggedIn) {\n                localStorage.setItem('schwabLoggedIn', 'true');\n            } else {\n                localStorage.removeItem('schwabLoggedIn');\n            }\n        } catch (error) {\n            console.error(\"Error checking Schwab login status:\", error);\n        }\n    };\n    const handleLogInToSchwab = async ()=>{\n        try {\n            console.log(\"Getting Auth URL...\");\n            const url = new URL(window.location.href);\n            let code = url.searchParams.get(\"code\");\n            if (!code) {\n                // Set a flag to indicate we're attempting to log in\n                localStorage.setItem('schwabLoginAttempt', 'true');\n                window.location.href = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeURL)();\n                return;\n            }\n            // If we have a code, we're logged in\n            localStorage.setItem('schwabLoggedIn', 'true');\n            setIsLoggedInToSchwab(true);\n        } catch (error) {\n            console.error(\"Error getting OAuth Token:\", error);\n        }\n    };\n    const handleLogOutFromSchwab = ()=>{\n        try {\n            // Clear Schwab-related cookies\n            document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            // Clear localStorage flags\n            localStorage.removeItem('schwabLoggedIn');\n            localStorage.removeItem('schwabLoginAttempt');\n            // Update state\n            setIsLoggedInToSchwab(false);\n            // Refresh the page to ensure all state is reset\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error logging out from Schwab:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        localStorage.clear();\n        sessionStorage.clear();\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/generalLogin\"\n        });\n    // window.location.href = \"/generalLogin\";\n    };\n    // Navigation cards data - More professional color scheme and icons\n    const navigationCards = [\n        {\n            title: \"Account Summary\",\n            description: \"View your account summary and balances\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this),\n            href: \"/accountsSummary\",\n            color: \"bg-slate-700\",\n            hoverColor: \"hover:bg-slate-800\",\n            borderColor: \"border-slate-600\"\n        },\n        {\n            title: \"WB Dashboard\",\n            description: \"Manage your WB trading pairs\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/dashboard\",\n            color: \"bg-blue-700\",\n            hoverColor: \"hover:bg-blue-800\",\n            borderColor: \"border-blue-600\"\n        },\n        {\n            title: \"WB Configuration\",\n            description: \"Configure your WB trading strategy\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/configuration\",\n            color: \"bg-gray-700\",\n            hoverColor: \"hover:bg-gray-800\",\n            borderColor: \"border-gray-600\"\n        },\n        {\n            title: \"Saved Pairs\",\n            description: \"View your saved trading pairs\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            href: \"/savedPairs\",\n            color: \"bg-indigo-700\",\n            hoverColor: \"hover:bg-indigo-800\",\n            borderColor: \"border-indigo-600\"\n        },\n        {\n            title: \"File Handler\",\n            description: \"Manage your data files\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            href: \"/fileHandler\",\n            color: \"bg-slate-700\",\n            hoverColor: \"hover:bg-slate-800\",\n            borderColor: \"border-slate-600\"\n        },\n        {\n            title: \"Account Activity\",\n            description: \"View your account activity\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            href: \"/testingAccountActivity\",\n            color: \"bg-blue-700\",\n            hoverColor: \"hover:bg-blue-800\",\n            borderColor: \"border-blue-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 dark:from-slate-900 dark:via-slate-800 dark:to-black text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 lg:mb-0 lg:mr-12 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mb-4\",\n                                                children: \"Professional Trading Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold tracking-tight mb-6 leading-tight\",\n                                            children: isSchwabCallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: \"Connected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" Successfully\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    \"Investment \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: \"Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 34\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 83\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Platform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-300 max-w-2xl mb-8 leading-relaxed\",\n                                            children: isSchwabCallback ? \"Your Schwab connection is active. Access real-time market data and manage your investment strategies with confidence.\" : \"Streamline your investment workflow with advanced analytics, real-time data integration, and comprehensive portfolio management tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n                                            onClick: ()=>handleLogInToSchwab(),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-2\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Connect to Schwab\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 lg:flex-1 lg:max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-64 h-64 lg:w-80 lg:h-80 mx-auto bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-2xl backdrop-blur-sm border border-white/10 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-24 w-24 mx-auto mb-4 text-blue-400\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1,\n                                                                d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-300 font-medium\",\n                                                            children: \"Real-time Analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-green-400/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-8 w-8 text-green-400\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-4 -left-4 w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-blue-400/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-8 w-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 shadow-sm dark:shadow-gray-900 rounded-lg p-4 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 dark:bg-blue-900 rounded-full p-2 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-5 w-5 text-blue-600 dark:text-blue-400\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: isLoggedInToSchwab ? \"Connected to Schwab\" : \"Not connected to Schwab\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleLogout(),\n                                className: \"px-3 py-1.5 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-md hover:bg-red-200 dark:hover:bg-red-900/50 transition duration-300 flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 12.586V8z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 shadow-sm dark:shadow-gray-900 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-0\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogOutFromSchwab,\n                                        className: \"px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300 flex items-center text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-1\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 12.586V8z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Disconnect from Schwab\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-3 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2.5 h-2.5 rounded-full \".concat(isLoggedInToSchwab ? 'bg-green-500' : 'bg-red-500', \" mr-2\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    \"Schwab Connection: \",\n                                                    isLoggedInToSchwab ? 'Active' : 'Inactive'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2.5 h-2.5 rounded-full bg-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: \"Dashboard Services: Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2.5 h-2.5 rounded-full bg-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: \"Data Processing: Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4\",\n                            children: \"Quick Navigation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: navigationCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                    href: card.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(card.color, \" \").concat(card.hoverColor, \" text-white rounded-lg shadow-md dark:shadow-gray-900 p-4 transition duration-300 transform hover:scale-105 hover:shadow-lg cursor-pointer h-full flex flex-col\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl mb-2\",\n                                                children: card.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-1\",\n                                                children: card.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-white text-opacity-90 flex-grow\",\n                                                children: card.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex justify-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vggbpo6EXw96Kx48ex04Bhn8Jac=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});