"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  CardTit<PERSON>,
  CardDescription,
  CardContent,
} from "../../../../components/ui/card";

import { OpenPositionsDataTable } from "./OpenPositionsDataTable";
import { LoadedPairsOrdersInDataTable } from "./LoadedPairsOrdersInDataTable";
import { ClosedPositionsDataTable } from "./ClosedPositionsDataTable";

export function TableCardsSection() {
  return (
    <div className="">
      <div className="my-4">
        <Card className="bg-gray-900">
          <CardHeader className="flex flex-row justify-between">
            <div className="flex flex-col">
              <CardTitle>Open Positions</CardTitle>
              <CardDescription></CardDescription>
            </div>
          </CardHeader>
          <CardContent className="flex flex-row justify-between">
            <OpenPositionsDataTable />
          </CardContent>
          <CardFooter className="flex flex-row justify-between"></CardFooter>
        </Card>
      </div>

      {/* <div className="my-4">
        <Card>
          <CardHeader className="flex flex-row justify-between">
            <div className="flex flex-col">
              <CardTitle>Loaded Pairs / Orders In</CardTitle>
              <CardDescription></CardDescription>
            </div>
          </CardHeader>
          <CardContent className="flex flex-row justify-between">
            <LoadedPairsOrdersInDataTable />
          </CardContent>
          <CardFooter className="flex flex-row justify-between"></CardFooter>
        </Card>
      </div>

      <div className="my-4">
        <Card>
          <CardHeader className="flex flex-row justify-between">
            <div className="flex flex-col">
              <CardTitle>Closed Positions</CardTitle>
              <CardDescription></CardDescription>
            </div>
          </CardHeader>
          <CardContent className="flex flex-row justify-between">
            <ClosedPositionsDataTable />
          </CardContent>
          <CardFooter className="flex flex-row justify-between"></CardFooter>
        </Card>
      </div> */}
    </div>
  );
}
