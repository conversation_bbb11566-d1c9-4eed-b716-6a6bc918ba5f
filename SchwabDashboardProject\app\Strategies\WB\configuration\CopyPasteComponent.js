"use client";
import { useState } from "react";
import { useExcelData } from "./page";
import { useRouter } from "next/navigation";
import { retrieveAccessToken, retrieveCustomerId, retrieveCorrelId } from "@/actions/schwabAccess";

export function CopyPasteComponent() {
  const router = useRouter();
  const [pasteData, setPasteData] = useState("");
  const [updateStatus, setUpdateStatus] = useState("");
  const [redirectAfterSubmit, setRedirectAfterSubmit] = useState(true);
  const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData } = useExcelData();

  const handleChange = (e) => {
    setPasteData(e.target.value);
  };

  const handleSubmit = async () => {
    const rows = pasteData.split(/\r?\n/).filter((row) => row.trim() !== "");
    const parsedData = rows.map((row) => row.split("\t"))
    const existingDataShort = (shortLoadedTableData.length + shortOpenTableData.length) || 0;
    const existingDataLong = (longLoadedTableData.length + longOpenTableData.length) || 0;

    const shortKeys = ["ticker", "shares", "sector", "spread", "volume", "status"];
    const longKeys = ["ticker", "shares", "sector", "spread", "volume", "status"];

    const shortData = parsedData.map((row, index) => {
      const obj = {};
      shortKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else {
          obj[key] = row[idx] || "";
        }
      });
      return { ...obj, id: (existingDataShort + index).toString() };
    });

    const longData = parsedData.map((row, index) => {
      const obj = {};
      longKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else {
          obj[key] = row[idx + 5] || "";
        }
      });
      return { ...obj, id: (existingDataLong + index).toString() };
    });

    setShortLoadedTableData((prev) => [...prev, ...shortData]);
    setLongLoadedTableData((prev) => [...prev, ...longData]);
    setPasteData("");

    // Display status message
    setUpdateStatus("Data processed successfully. Pairs will be saved to database when created.");

    // Redirect to dashboard if option is enabled
    if (redirectAfterSubmit) {
      // Wait a moment to ensure data is saved to localStorage
      setTimeout(() => {
        router.push("/strategies/WB/dashboard");
      }, 500);
    }
  };

  // Function to update stock symbols on the server
  const updateStockSymbols = async () => {
    try {
      setUpdateStatus("Update in progress...");

      // Collect all ticker symbols from both short and long data
      const shortTickers = [...shortLoadedTableData, ...shortOpenTableData]
        .map(item => item.ticker)
        .filter(ticker => ticker && ticker.trim() !== "");

      const longTickers = [...longLoadedTableData, ...longOpenTableData]
        .map(item => item.ticker)
        .filter(ticker => ticker && ticker.trim() !== "");

      // Combine and remove duplicates
      const allTickers = [...new Set([...shortTickers, ...longTickers])];

      if (allTickers.length === 0) {
        setUpdateStatus("No symbols found to update");
        return;
      }

      console.log("Symbols to send to server:", allTickers);

      // First, test if the server is responding
      try {
        const testUrl = "https://localhost:3001/test";
        setUpdateStatus(`Verifying server connection: ${testUrl}...`);

        const testResponse = await fetch(testUrl, { credentials: "include" });
        if (!testResponse.ok) {
          setUpdateStatus(`Error: The server is not responding correctly. Code: ${testResponse.status}`);
          return;
        }

        const testData = await testResponse.json();
        console.log("Test server response:", testData);
        setUpdateStatus(`Server connected. Current symbols: ${testData.currentSymbols}. Sending new symbols...`);
      } catch (testError) {
        console.error("Error in connection test:", testError);
        setUpdateStatus(`Connection error: ${testError.message}. Make sure the server is running on https://localhost:3001`);
        return;
      }

      // Send to server
      const url = "https://localhost:3001/update-stock-symbols";

      try {
        // Recupera token, customerId, correlId
        const accessToken = await retrieveAccessToken();
        const customerId = await retrieveCustomerId();
        const correlId = await retrieveCorrelId();

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            symbols: allTickers,
            token: accessToken,
            clientCustomerId: customerId,
            clientCorrelId: correlId
          }),
          credentials: "include",
        });

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const textResponse = await response.text();
          console.error("Server returned non-JSON response:", textResponse);
          setUpdateStatus(`Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001`);
          return;
        }

        const data = await response.json();

        if (response.ok) {
          setUpdateStatus(`Symbols updated successfully: ${data.symbols}`);
        } else {
          setUpdateStatus(`Error: ${data.error || 'Unknown error'}`);
        }
      } catch (fetchError) {
        console.error("Error in fetch request:", fetchError);
        setUpdateStatus(`Connection error: ${fetchError.message}. Make sure the server is running on http://localhost:3001`);
      }
    } catch (error) {
      console.error("Error updating symbols:", error);
      setUpdateStatus(`Error: ${error.message || 'Unknown error'}`);
    }
  };

  // Function to clear all data
  const clearData = () => {
    const { setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData } = useExcelData();

    setShortOpenTableData([]);
    setShortLoadedTableData([]);
    setLongOpenTableData([]);
    setLongLoadedTableData([]);

    if (typeof window !== "undefined") {
      localStorage.removeItem("shortOpenTableData");
      localStorage.removeItem("shortLoadedTableData");
      localStorage.removeItem("longOpenTableData");
      localStorage.removeItem("longLoadedTableData");
    }

    setUpdateStatus("All data has been cleared");
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Paste Data from Excel</h2>

      <div className="mb-4">
        <textarea
          onChange={handleChange}
          value={pasteData}
          placeholder="Paste Excel data here (tabular format)"
          rows="10"
          className="w-full p-2 border border-gray-300 rounded-md"
        />
      </div>

      <div className="flex flex-wrap gap-3 mb-6">
        <button
          onClick={handleSubmit}
          className="bg-blue-500 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
        >
          Process Data
        </button>

        <button
          onClick={updateStockSymbols}
          className="bg-green-500 hover:bg-green-700 text-white font-medium py-2 px-4 rounded"
        >
          Update Symbols on Server
        </button>

        <button
          onClick={clearData}
          className="bg-red-500 hover:bg-red-700 text-white font-medium py-2 px-4 rounded"
        >
          Clear Data
        </button>
      </div>

      <div className="mb-4 flex items-center">
        <input
          type="checkbox"
          id="redirectToggle"
          checked={redirectAfterSubmit}
          onChange={() => setRedirectAfterSubmit(!redirectAfterSubmit)}
          className="mr-2 h-4 w-4"
        />
        <label htmlFor="redirectToggle" className="text-sm text-gray-700">
          Redirect to dashboard after processing data
        </label>
      </div>

      {updateStatus && (
        <div className={`mt-4 p-3 rounded-md ${updateStatus.includes('Error') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
          {updateStatus}
        </div>
      )}
    </div>
  );
}
