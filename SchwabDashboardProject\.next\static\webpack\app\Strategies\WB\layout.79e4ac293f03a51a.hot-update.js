"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/layout",{

/***/ "(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js":
/*!***************************************************!*\
  !*** ./app/testingWebsocket/MarketDataContext.js ***!
  \***************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataProvider: () => (/* binding */ MarketDataProvider),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Strategies/WB/configuration/page */ \"(app-pages-browser)/./app/Strategies/WB/configuration/page.js\");\n/* __next_internal_client_entry_do_not_use__ MarketDataProvider,useMarketData auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst MarketDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction MarketDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [accountData, setAccountData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accountFilteredData, setAccountFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Accesso al sistema di cache per i dati di mercato\n    const { updateMarketData, getMarketData } = (0,_app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData)() || {};\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"https://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ]\n            });\n            socket.on(\"connect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Connesso al server Socket.io\");\n                    handleNewToken();\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on('authenticated', {\n                \"MarketDataProvider.useEffect\": (response)=>{\n                    if (response.success) {\n                        console.log('Socket authenticated successfully');\n                    } else {\n                        console.error('Socket authentication failed:', response.error);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            async function handleNewToken() {\n                try {\n                    // Assicurati che il customerId e correlId siano salvati prima di usarli\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCustomerId)();\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCorrelId)();\n                    const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                    const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                    const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                    if (!customerId || !correlId) {\n                        console.error(\"CustomerId o correlId non disponibili, devi prima richiederli!\");\n                        return;\n                    }\n                    // Send authentication data to WebSocket\n                    socket.emit('authenticate', {\n                        customerId: customerId,\n                        correlId: correlId,\n                        accessToken: accessToken\n                    });\n                    const res = await fetch(\"https://localhost:3001/init-schwab\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            token: accessToken,\n                            clientCorrelId: correlId,\n                            clientCustomerId: customerId\n                        }),\n                        credentials: \"include\"\n                    });\n                    await res.json();\n                } catch (err) {\n                    console.error(\"Errore nell'aggiornare il token:\", err);\n                }\n            }\n            socket.on(\"marketData\", {\n                \"MarketDataProvider.useEffect\": (data)=>{\n                    let parsedData;\n                    try {\n                        parsedData = JSON.parse(data);\n                    } catch (error) {\n                        console.error(\"Errore nel parsing dei dati:\", error);\n                        return;\n                    }\n                    setMarketData({\n                        \"MarketDataProvider.useEffect\": (prev)=>[\n                                ...prev,\n                                parsedData\n                            ]\n                    }[\"MarketDataProvider.useEffect\"]);\n                    if (parsedData.data && Array.isArray(parsedData.data)) {\n                        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati\n                        parsedData.data.forEach({\n                            \"MarketDataProvider.useEffect\": (item)=>{\n                                if (item.content && Array.isArray(item.content)) {\n                                    item.content.forEach({\n                                        \"MarketDataProvider.useEffect\": (stock)=>{\n                                            const ticker = stock.key;\n                                            const value1 = stock[\"1\"];\n                                            const value2 = stock[\"2\"];\n                                            const value3 = stock[\"3\"];\n                                            const value5 = stock[\"18\"];\n                                            const value6 = stock[\"8\"];\n                                            const value7 = stock[\"22\"];\n                                            const value8 = stock[\"26\"];\n                                            if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {\n                                                // Crea un nuovo oggetto per questo ticker con i valori principali\n                                                const tickerData = {\n                                                    bid_prc: value1,\n                                                    ask_prc: value2,\n                                                    last_prc: value3,\n                                                    timestamp: item.timestamp\n                                                };\n                                                // Aggiungi change e volume solo se sono definiti\n                                                if (value5 !== undefined) {\n                                                    tickerData.change = value5;\n                                                }\n                                                if (value6 !== undefined) {\n                                                    tickerData.volume = value6;\n                                                }\n                                                // Aggiungi dividend solo se è definito\n                                                if (value7 !== undefined) {\n                                                    tickerData.dividend = value7;\n                                                }\n                                                if (value8 !== undefined) {\n                                                    tickerData.ex_div_date = value8;\n                                                }\n                                                // Salva l'oggetto completo\n                                                newFilteredData[ticker] = tickerData;\n                                            }\n                                        }\n                                    }[\"MarketDataProvider.useEffect\"]);\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati\n                        setFilteredData({\n                            \"MarketDataProvider.useEffect\": (prev)=>{\n                                const updatedData = {\n                                    ...prev\n                                };\n                                // Per ogni ticker nei nuovi dati\n                                Object.keys(newFilteredData).forEach({\n                                    \"MarketDataProvider.useEffect\": (ticker)=>{\n                                        // Se il ticker esiste già nello stato precedente\n                                        if (updatedData[ticker]) {\n                                            // Crea un nuovo oggetto per questo ticker\n                                            updatedData[ticker] = {\n                                                ...updatedData[ticker],\n                                                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori\n                                            };\n                                            // Se dividend è undefined nei nuovi dati ma esiste nei dati precedenti, mantieni il valore precedente\n                                            if (newFilteredData[ticker].dividend === undefined && updatedData[ticker].dividend !== undefined) {\n                                                updatedData[ticker].dividend = prev[ticker].dividend;\n                                            }\n                                        } else {\n                                            // Se è un nuovo ticker, aggiungi semplicemente i nuovi dati\n                                            updatedData[ticker] = newFilteredData[ticker];\n                                        }\n                                    }\n                                }[\"MarketDataProvider.useEffect\"]);\n                                return updatedData;\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Disconnesso dal server Socket.io\");\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            return ({\n                \"MarketDataProvider.useEffect\": ()=>{\n                    socket.disconnect();\n                    console.log(\"Socket disconnesso (cleanup del MarketDataProvider)\");\n                }\n            })[\"MarketDataProvider.useEffect\"];\n        }\n    }[\"MarketDataProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MarketDataContext.Provider, {\n        value: {\n            marketData,\n            filteredData\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\testingWebsocket\\\\MarketDataContext.js\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketDataProvider, \"1guidLdNFU8u7NOo5fKj5tpL9h4=\", false, function() {\n    return [\n        _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData\n    ];\n});\n_c = MarketDataProvider;\nfunction useMarketData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MarketDataContext);\n}\n_s1(useMarketData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"MarketDataProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js\n"));

/***/ })

});