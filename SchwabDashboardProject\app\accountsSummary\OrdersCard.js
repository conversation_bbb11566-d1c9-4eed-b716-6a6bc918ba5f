
import React, { useState } from 'react';
import { useEndpointAppContext } from '@/components/EndpointAppContext';
import { formatNumber } from '@/utils/formatNumber';
import { cancelOrder } from '@/actions/schwabTraderAPIactions';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import DateRangeSelector from '@/components/DateRangeSelector';



const OrdersCard = () => {
  const {
    ordersData,
    setOrdersData,
    ordersDateRange,
    updateOrdersDateRange
  } = useEndpointAppContext();
  // All hooks must be declared before any return
  const [canceling, setCanceling] = useState({}); // { [orderId]: true/false }
  const [error, setError] = useState(null);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [pendingCancel, setPendingCancel] = useState(null); // {accountNumber, orderId}

  // Cancel order dialog state (must be declared before any early return)
  // (REMOVE these duplicate hooks, already declared at the top)

  // Cancel order handler (after confirmation)
  const handleCancel = async (accountNumber, orderId) => {
    setCanceling((prev) => ({ ...prev, [orderId]: true }));
    setError(null);
    try {
      await cancelOrder(accountNumber, orderId);
      setOrdersData((prev) => prev.filter((order) => order.orderId !== orderId));
    } catch (err) {
      setError(`Failed to cancel order ${orderId}`);
    } finally {
      setCanceling((prev) => ({ ...prev, [orderId]: false }));
      setCancelDialogOpen(false);
      setPendingCancel(null);
    }
  };

  return (
    <div className="overflow-x-auto">
      {/* Date Range Selector */}
      <div className="mb-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg flex items-center gap-2">
        <span className="text-xs font-medium text-green-800 dark:text-green-200">
          Date Range:
        </span>
        <DateRangeSelector
          selectedRange={ordersDateRange}
          onRangeChange={updateOrdersDateRange}
        />
      </div>

      {error && <div className="text-red-600 text-center mb-2">{error}</div>}

      {/* Content area */}
      {!ordersData ? (
        <div className="flex items-center justify-center h-full w-full">
          <div className="text-center text-gray-500 dark:text-gray-400 p-4">No orders data</div>
        </div>
      ) : ordersData.length === 0 ? (
        <div className="flex items-center justify-center h-full w-full">
          <div className="text-center text-gray-500 dark:text-gray-400 p-4">No orders data</div>
        </div>
      ) : (
      <TooltipProvider delayDuration={20}>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-amber-50 dark:bg-amber-900">
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-10">Action</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-32">Account #</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-32">Order ID</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Duration</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Order Type</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Quantity</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Filled Qty</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Remaining</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-24">CUSIP</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Symbol</th>
              <th className="border-b-2 border-amber-200 dark:border-amber-700 py-2 px-1 text-left text-xs font-medium text-amber-800 dark:text-amber-100 w-20">Price</th>
            </tr>
          </thead>
          <tbody>
            {ordersData.map((order, index) => {
              const instrument =
                order.orderLegCollection &&
                order.orderLegCollection[0] &&
                order.orderLegCollection[0].instrument;
              const accountNumber = order.accountNumber || order.account_id || order.account || '--';
              const orderId = order.orderId || order.id || '--';
              // All fields to display in the table, in order
              const fields = [
                accountNumber,
                orderId,
                order.duration ?? '--',
                order.orderType ?? '--',
                formatNumber(order.quantity, 0),
                formatNumber(order.filledQuantity, 0),
                formatNumber(order.remainingQuantity, 0),
                instrument && instrument.cusip ? instrument.cusip : '--',
                instrument && instrument.symbol ? instrument.symbol : '--',
                formatNumber(order.price, 2, true),
              ];
              return (
                <tr key={index} className="hover:bg-amber-50 dark:hover:bg-amber-900/30 transition-colors">
                  <td className="border-b border-amber-100 dark:border-amber-800 py-2 px-1 text-xs">
                    <TooltipProvider delayDuration={20}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            className="px-2 py-1 rounded bg-red-600 text-white hover:bg-red-700 disabled:opacity-60"
                            disabled={canceling[orderId] || !orderId || !accountNumber}
                            onClick={() => {
                              setPendingCancel({ accountNumber, orderId });
                              setCancelDialogOpen(true);
                            }}
                            aria-label="Cancel Order"
                          >
                            {canceling[orderId] ? 'Canceling...' : 'Cancel'}
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <span>Cancel this order</span>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                  {fields.map((val, i) => (
                    <td key={i} className="border-b border-amber-100 dark:border-amber-800 py-2 px-1 text-xs max-w-[8rem] truncate">
                      <TooltipProvider delayDuration={20}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate cursor-help">{val ?? '--'}</span>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <span className="break-all">{val ?? '--'}</span>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      {/* Cancel Order Confirmation Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent className="max-w-md w-full p-6 rounded-lg">
          <div className="mb-4 text-lg font-semibold">Are you sure you want to cancel this order?</div>
          <div className="flex justify-end gap-2 mt-6">
            <DialogClose asChild>
              <button type="button" className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600">No</button>
            </DialogClose>
            <button
              type="button"
              className="px-4 py-2 rounded bg-red-600 text-white font-semibold hover:bg-red-700 disabled:opacity-60"
              disabled={canceling[pendingCancel?.orderId]}
              onClick={() => {
                if (pendingCancel) {
                  handleCancel(pendingCancel.accountNumber, pendingCancel.orderId);
                }
              }}
            >
              {canceling[pendingCancel?.orderId] ? 'Canceling...' : 'Yes, Cancel Order'}
            </button>
          </div>
        </DialogContent>
      </Dialog>
      </TooltipProvider>
      )}
    </div>
  );
};


export default OrdersCard;
