"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/layout",{

/***/ "(app-pages-browser)/./app/Strategies/WB/configuration/page.js":
/*!*************************************************!*\
  !*** ./app/Strategies/WB/configuration/page.js ***!
  \*************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExcelDataProvider: () => (/* binding */ ExcelDataProvider),\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   useExcelData: () => (/* binding */ useExcelData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TooltipButton */ \"(app-pages-browser)/./components/TooltipButton.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ ExcelDataProvider,useExcelData,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst shortKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst longKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst ExcelDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    shortOpenTableData: [],\n    shortLoadedTableData: [],\n    longOpenTableData: [],\n    longLoadedTableData: [],\n    shortClosedTableData: [],\n    longClosedTableData: [],\n    setShortOpenTableData: ()=>{},\n    setShortLoadedTableData: ()=>{},\n    setLongOpenTableData: ()=>{},\n    setLongLoadedTableData: ()=>{},\n    setShortClosedTableData: ()=>{},\n    setLongClosedTableData: ()=>{},\n    updateLongStatus: ()=>{},\n    updateShortStatus: ()=>{}\n});\nfunction ExcelDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [shortOpenTableData, setShortOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortLoadedTableData, setShortLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longOpenTableData, setLongOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longLoadedTableData, setLongLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortClosedTableData, setShortClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longClosedTableData, setLongClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelDataProvider.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem(\"shortOpenTableData\", JSON.stringify(shortOpenTableData));\n                localStorage.setItem(\"shortLoadedTableData\", JSON.stringify(shortLoadedTableData));\n                localStorage.setItem(\"longOpenTableData\", JSON.stringify(longOpenTableData));\n                localStorage.setItem(\"longLoadedTableData\", JSON.stringify(longLoadedTableData));\n                localStorage.setItem(\"shortClosedTableData\", JSON.stringify(shortClosedTableData));\n                localStorage.setItem(\"longClosedTableData\", JSON.stringify(longClosedTableData));\n            }\n        }\n    }[\"ExcelDataProvider.useEffect\"], [\n        shortOpenTableData,\n        shortLoadedTableData,\n        shortClosedTableData,\n        longOpenTableData,\n        longLoadedTableData,\n        longClosedTableData\n    ]);\n    // Move a long row from any table to any other table, like the old system but for all tables\n    const updateLongStatus = (rowId, newStatus)=>{\n        // Try to find the row in all tables\n        let row = longLoadedTableData.find((r)=>r.id === rowId) || longOpenTableData.find((r)=>r.id === rowId) || longClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        // Remove from all tables\n        setLongLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        // Add to the right table\n        if (newStatus === \"WB_LoadedPairs\") {\n            setLongLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setLongOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setLongClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    // Move a short row from any table to any other table, like the old system but for all tables\n    const updateShortStatus = (rowId, newStatus)=>{\n        let row = shortLoadedTableData.find((r)=>r.id === rowId) || shortOpenTableData.find((r)=>r.id === rowId) || shortClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        setShortLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        if (newStatus === \"WB_LoadedPairs\") {\n            setShortLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setShortOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setShortClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    const updateShortClosedStatus = (rowId, newStatus)=>{\n        setShortOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setShortClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    const updateLongClosedStatus = (rowId, newStatus)=>{\n        setLongOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setLongClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelDataContext.Provider, {\n        value: {\n            shortOpenTableData,\n            shortLoadedTableData,\n            longOpenTableData,\n            longLoadedTableData,\n            shortClosedTableData,\n            longClosedTableData,\n            setShortOpenTableData,\n            setShortLoadedTableData,\n            setLongOpenTableData,\n            setLongLoadedTableData,\n            setShortClosedTableData,\n            setLongClosedTableData,\n            updateLongStatus,\n            updateShortStatus,\n            updateShortClosedStatus,\n            updateLongClosedStatus\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcelDataProvider, \"E1LSBqPBGk1KTN7p7jAqQ4cSOp0=\");\n_c = ExcelDataProvider;\nfunction useExcelData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ExcelDataContext);\n}\n_s1(useExcelData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction ExcelInput() {\n    _s2();\n    const [pasteData, setPasteData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [updateStatus, setUpdateStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [redirectAfterSubmit, setRedirectAfterSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [savedSymbols, setSavedSymbols] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData, shortClosedTableData, longClosedTableData } = useExcelData();\n    // Load saved symbols on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelInput.useEffect\": ()=>{\n            const loadSavedSymbols = {\n                \"ExcelInput.useEffect.loadSavedSymbols\": async ()=>{\n                    try {\n                        const response = await fetch('/api/get-saved-symbols');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setSavedSymbols(data.symbolsArray || []);\n                            console.log('Loaded saved symbols:', data.symbolsArray);\n                        }\n                    } catch (error) {\n                        console.error('Error loading saved symbols:', error);\n                    }\n                }\n            }[\"ExcelInput.useEffect.loadSavedSymbols\"];\n            loadSavedSymbols();\n        }\n    }[\"ExcelInput.useEffect\"], []);\n    const handleChange = (e)=>{\n        setPasteData(e.target.value);\n    };\n    const handleSubmit = async ()=>{\n        const rows = pasteData.split(/\\r?\\n/).filter((row)=>row.trim() !== \"\");\n        const parsedData = rows.map((row)=>row.split(\"\\t\"));\n        const existingDataShort = shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length || 0;\n        const existingDataLong = longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length || 0;\n        const shortData = parsedData.map((row, index)=>{\n            const obj = {};\n            shortKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataShort + index).toString()\n            };\n        });\n        const longData = parsedData.map((row, index)=>{\n            const obj = {};\n            longKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx + 5] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataLong + index).toString()\n            };\n        });\n        setShortLoadedTableData((prev)=>[\n                ...prev,\n                ...shortData\n            ]);\n        setLongLoadedTableData((prev)=>[\n                ...prev,\n                ...longData\n            ]);\n        setPasteData(\"\");\n        // Display status message\n        setUpdateStatus(\"Data processed successfully. Pairs will be saved to database when created.\");\n        // Redirect to dashboard if option is enabled\n        if (redirectAfterSubmit) {\n            // Wait a moment to ensure data is saved to localStorage\n            setTimeout(()=>{\n                window.location.href = \"/Strategies/WB/dashboard\";\n            }, 500);\n        }\n    };\n    // Function to update stock symbols on the server\n    const updateStockSymbols = async ()=>{\n        try {\n            setUpdateStatus(\"Update in progress...\");\n            // Collect all ticker symbols from both short and long data\n            const shortTickers = [\n                ...shortLoadedTableData,\n                ...shortOpenTableData,\n                ...shortClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            const longTickers = [\n                ...longLoadedTableData,\n                ...longOpenTableData,\n                ...longClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            // Combine and remove duplicates\n            const allTickers = [\n                ...new Set([\n                    ...shortTickers,\n                    ...longTickers\n                ])\n            ];\n            if (allTickers.length === 0) {\n                setUpdateStatus(\"No symbols found to update\");\n                return;\n            }\n            console.log(\"Symbols to send to server:\", allTickers);\n            // First, test if the server is responding\n            try {\n                const testUrl = \"https://localhost:3001/test\";\n                setUpdateStatus(\"Verifying server connection: \".concat(testUrl, \"...\"));\n                const testResponse = await fetch(testUrl);\n                if (!testResponse.ok) {\n                    setUpdateStatus(\"Error: The server is not responding correctly. Code: \".concat(testResponse.status));\n                    return;\n                }\n                const testData = await testResponse.json();\n                console.log(\"Test server response:\", testData);\n                setUpdateStatus(\"Server connected. Current symbols: \".concat(testData.currentSymbols, \". Sending new symbols...\"));\n            } catch (testError) {\n                console.error(\"Error in connection test:\", testError);\n                setUpdateStatus(\"Connection error: \".concat(testError.message, \". Make sure the server is running on http://localhost:3001\"));\n                return;\n            }\n            // Send to server\n            const url = \"https://localhost:3001/update-stock-symbols\";\n            try {\n                var _sessionData_user;\n                const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                // Get user session for email\n                const session = await fetch('/api/auth/session');\n                const sessionData = await session.json();\n                const userEmail = sessionData === null || sessionData === void 0 ? void 0 : (_sessionData_user = sessionData.user) === null || _sessionData_user === void 0 ? void 0 : _sessionData_user.email;\n                if (!userEmail) {\n                    console.warn('No user email found in session, symbols will not be associated with user account');\n                }\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        symbols: allTickers,\n                        token: accessToken,\n                        clientCustomerId: customerId,\n                        clientCorrelId: correlId,\n                        userEmail: userEmail\n                    }),\n                    credentials: \"include\"\n                });\n                // Check if response is JSON\n                const contentType = response.headers.get(\"content-type\");\n                if (!contentType || !contentType.includes(\"application/json\")) {\n                    const textResponse = await response.text();\n                    console.error(\"Server returned non-JSON response:\", textResponse);\n                    setUpdateStatus(\"Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001\");\n                    return;\n                }\n                const data = await response.json();\n                if (response.ok) {\n                    setUpdateStatus(\"Symbols updated successfully: \".concat(data.symbols));\n                    // Update the saved symbols display\n                    setSavedSymbols(data.symbols.split(','));\n                } else {\n                    setUpdateStatus(\"Error: \".concat(data.error || 'Unknown error'));\n                }\n            } catch (fetchError) {\n                console.error(\"Error in fetch request:\", fetchError);\n                setUpdateStatus(\"Connection error: \".concat(fetchError.message, \". Make sure the server is running on http://localhost:3001\"));\n            }\n        } catch (error) {\n            console.error(\"Error updating symbols:\", error);\n            setUpdateStatus(\"Error: \".concat(error.message || 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Paste Data from Excel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            onChange: handleChange,\n                            value: pasteData,\n                            placeholder: \"Paste Excel data here (tabular format)\",\n                            rows: \"10\",\n                            className: \"w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: handleSubmit,\n                                        className: \"bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Process the pasted Excel data and update the tables\",\n                                        children: \"\\uD83D\\uDCCA Process Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: updateStockSymbols,\n                                        className: \"bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Update stock symbols on the server for real-time data\",\n                                        children: \"\\uD83D\\uDD04 Update Symbols\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            savedSymbols.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-blue-800 dark:text-blue-200 flex items-center gap-2\",\n                                            children: [\n                                                \"\\uD83D\\uDCC8 Currently Saved Symbols\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-600 rounded-full\",\n                                                    children: savedSymbols.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1.5\",\n                                        children: savedSymbols.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-1 text-xs font-medium text-blue-800 dark:text-blue-200 bg-blue-100 dark:bg-blue-800/30 rounded-full border border-blue-200 dark:border-blue-700\",\n                                                children: symbol\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    updateStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-xl shadow-sm border \".concat(updateStatus.includes('Error') ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800' : 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: updateStatus.includes('Error') ? '❌' : '✅'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: updateStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 366,\n        columnNumber: 5\n    }, this);\n}\n_s2(ExcelInput, \"3pu7fML+lOyH2fWTWG9569dVCXw=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c1 = ExcelInput;\nfunction ClearButton() {\n    _s3();\n    const { setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData, setShortClosedTableData, setLongClosedTableData } = useExcelData();\n    const [clearStatus, setClearStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const clearData = ()=>{\n        setShortOpenTableData([]);\n        setShortLoadedTableData([]);\n        setLongOpenTableData([]);\n        setLongLoadedTableData([]);\n        setShortClosedTableData([]);\n        setLongClosedTableData([]);\n        if (true) {\n            localStorage.removeItem(\"shortOpenTableData\");\n            localStorage.removeItem(\"shortLoadedTableData\");\n            localStorage.removeItem(\"longOpenTableData\");\n            localStorage.removeItem(\"longLoadedTableData\");\n            localStorage.removeItem(\"shortClosedTableData\");\n            localStorage.removeItem(\"longClosedTableData\");\n        }\n        setClearStatus(\"All data has been cleared\");\n        setTimeout(()=>setClearStatus(\"\"), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Data Management\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Clear all Excel data from memory and local storage. This action cannot be undone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: clearData,\n                                        className: \"bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Clear all Excel data from memory and local storage\",\n                                        children: \"\\uD83D\\uDDD1️ Clear All Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 text-red-200 dark:text-red-900\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this),\n                    clearStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-md bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800\",\n                        children: clearStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 515,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 490,\n        columnNumber: 5\n    }, this);\n}\n_s3(ClearButton, \"rVGbK71ktCLVokc7Lo2a2EMXKWA=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c2 = ClearButton;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 shadow-md dark:shadow-indigo-950 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"WB Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mt-2\",\n                            children: \"Configure your WB trading strategy settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelInput, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClearButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 526,\n        columnNumber: 5\n    }, this);\n}\n_c3 = Home;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ExcelDataProvider\");\n$RefreshReg$(_c1, \"ExcelInput\");\n$RefreshReg$(_c2, \"ClearButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/Strategies/WB/configuration/page.js\n"));

/***/ })

});