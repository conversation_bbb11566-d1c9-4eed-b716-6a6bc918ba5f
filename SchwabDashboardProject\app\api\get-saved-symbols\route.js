import { NextResponse } from "next/server";
import { retrieveAccessToken, retrieveCustomerId, retrieveCorrelId } from "@/actions/schwabAccess";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth-options";

export async function GET() {
  try {
    // Get NextAuth session for user email
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    // Get auth data
    const accessToken = await retrieveAccessToken();
    const customerId = await retrieveCustomerId();
    const correlId = await retrieveCorrelId();

    if (!accessToken || !customerId || !correlId) {
      return NextResponse.json(
        { error: "Missing authentication data" },
        { status: 401 }
      );
    }

    // Call server endpoint to get saved symbols
    const response = await fetch("https://localhost:3001/get-user-symbols", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "x-client-customerid": customerId,
        "x-client-correlid": correlId,
        "x-user-email": session.user.email,
        "authorization": `Bearer ${accessToken}`
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to get saved symbols" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error getting saved symbols:", error);
    return NextResponse.json(
      { error: "Failed to get saved symbols" },
      { status: 500 }
    );
  }
}
