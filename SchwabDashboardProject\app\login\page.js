"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation.js";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

import {
  getAuthorizationCodeURL,
  storeAuthorizationCode,
} from "../../actions/schwabAccess";

export default function LoginPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleLogInToSchwab = async () => {
    toast({
      title: "Signing in to <PERSON>hwab",
      description: "Please wait, this will take a few seconds",
      duration: 5000,
    });
    //setLoading(true);
    const authorizationCodeUrl = await getAuthorizationCodeURL();
    console.log("backend Authorization Code URL: " + authorizationCodeUrl);
    const newTab = window.open(authorizationCodeUrl, "_blank");

    //await storeAuthorizationCode(authorizationCodeUrl);
    //setLoading(false);
  };

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="w-full max-w-md space-y-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Welcome back</h1>
          <p className="text-gray-600">Sign in to your account</p>

          <button
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            // onClick={handleLogInToSchwab}
            onClick={() => signIn("Schwab", { redirectTo: "/dashboard" })}
          >
            Sign in with Schwab
          </button>
        </div>
      </div>
    </div>
  );
}
