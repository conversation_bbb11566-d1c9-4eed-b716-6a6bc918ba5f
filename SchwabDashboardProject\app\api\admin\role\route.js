"use server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST(req) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user.role || session.user.role.toLowerCase() !== "admin") {
    return new Response(JSON.stringify({ error: "Unauthorized" }), { status: 401 });
  }
  const { userId, role } = await req.json();
  if (!userId || !role) {
    return new Response(JSON.stringify({ error: "Missing userId or role" }), { status: 400 });
  }
  // Always store role in original case (e.g., "admin" or "user")
  await prisma.users.update({ where: { id: userId }, data: { role: role.toLowerCase() } });
  return new Response(JSON.stringify({ success: true }), { status: 200 });
}
