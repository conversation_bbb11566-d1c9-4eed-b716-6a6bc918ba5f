"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
} from "../../../../components/ui/card";

export function SummarySection() {
  const today = new Date().toLocaleDateString("en-US", {
    month: "numeric",
    day: "numeric",
  });
  const todayPL = Math.floor(Math.random() * 201) - 100;
  const openToday = Math.floor(Math.random() * 201) - 100;
  const closedToday = Math.floor(Math.random() * 201) - 100;
  const total = Math.floor(Math.random() * 201) - 100;
  const longDollars = Math.floor(Math.random() * 101000);
  const shortDollars = -Math.floor(Math.random() * 101000);
  const accountValue = Math.floor(Math.random() * 201000) - 100;
  const cashSweep = Math.floor(Math.random() * 20100) - 100;
  const shortMarginableValue = Math.floor(Math.random() * 20100) - 100;
  const plYear = Math.floor(Math.random() * 201000);

  return (
    <Card>
      {/* <<PERSON><PERSON><PERSON>er></CardHeader> */}
      <CardContent className="flex flex-row justify-between mt-1">
        <div className="flex flex-col">
          <p>
            {`Today is: `}
            <span className="text-yellow-600">{today}</span>
          </p>
          <p>
            {`Long: `}
            <span style={{ color: longDollars > 0 ? "green" : "red" }}>
              {longDollars}
            </span>
          </p>
          <p>
            {`Short: `}
            <span style={{ color: shortDollars > 0 ? "green" : "red" }}>
              {shortDollars}
            </span>
          </p>
        </div>
        <div className="flex flex-col">
          <p>
            {`Today's P/L: `}
            <span style={{ color: todayPL > 0 ? "green" : "red" }}>
              {todayPL}
            </span>
          </p>
          <p>
            {`Open Today: `}
            <span style={{ color: openToday > 0 ? "green" : "red" }}>
              {openToday}
            </span>
          </p>
          <p>
            {`Closed Today: `}
            <span style={{ color: closedToday > 0 ? "green" : "red" }}>
              {closedToday}
            </span>
          </p>
          <p>
            {`Total: `}
            <span style={{ color: total > 0 ? "green" : "red" }}>{total}</span>
          </p>
        </div>
        <div className="flex flex-col">
          <p>
            {`Account Value: `}
            <span style={{ color: accountValue > 0 ? "green" : "red" }}>
              {accountValue}
            </span>
          </p>
          <p>
            {`Cash & Sweep: `}
            <span style={{ color: cashSweep > 0 ? "green" : "red" }}>
              {cashSweep}
            </span>
          </p>
          <p>
            {`Short Marginable Value: `}
            <span style={{ color: shortMarginableValue > 0 ? "green" : "red" }}>
              {shortMarginableValue}
            </span>
          </p>
          <p>
            {`P/L Year: `}
            <span style={{ color: plYear > 0 ? "green" : "red" }}>
              {plYear}
            </span>
          </p>
        </div>
      </CardContent>
      {/* <CardFooter></CardFooter> */}
    </Card>
  );
}
