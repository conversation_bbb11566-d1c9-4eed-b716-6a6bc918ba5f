/**
 * Formats a number with commas as thousands separators
 * @param {number|string} value - The number to format
 * @param {number} [decimals=2] - Number of decimal places
 * @param {boolean} [forceDecimals=false] - Whether to always show decimal places even if they're zeros
 * @returns {string} - The formatted number as a string
 */
export function formatNumber(value, decimals = 2, forceDecimals = false) {
  // Handle null, undefined, or empty string
  if (value === null || value === undefined || value === '') {
    return '--';
  }
  
  // Convert to number if it's a string
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  // Check if it's a valid number
  if (isNaN(num)) {
    return '--';
  }
  
  // Format the number with commas and decimal places
  const options = {
    minimumFractionDigits: forceDecimals ? decimals : 0,
    maximumFractionDigits: decimals,
    useGrouping: true
  };
  
  return num.toLocaleString('en-US', options);
}
