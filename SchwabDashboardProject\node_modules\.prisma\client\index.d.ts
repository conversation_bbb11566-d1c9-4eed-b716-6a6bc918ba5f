
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model ExcelDataLongLoadedTableData
 * 
 */
export type ExcelDataLongLoadedTableData = $Result.DefaultSelection<Prisma.$ExcelDataLongLoadedTableDataPayload>
/**
 * Model ExcelDataLongOpenTableData
 * 
 */
export type ExcelDataLongOpenTableData = $Result.DefaultSelection<Prisma.$ExcelDataLongOpenTableDataPayload>
/**
 * Model ExcelDataShortLoadedTableData
 * 
 */
export type ExcelDataShortLoadedTableData = $Result.DefaultSelection<Prisma.$ExcelDataShortLoadedTableDataPayload>
/**
 * Model ExcelDataShortOpenTableData
 * 
 */
export type ExcelDataShortOpenTableData = $Result.DefaultSelection<Prisma.$ExcelDataShortOpenTableDataPayload>
/**
 * Model ExcelDataShortClosedTableData
 * 
 */
export type ExcelDataShortClosedTableData = $Result.DefaultSelection<Prisma.$ExcelDataShortClosedTableDataPayload>
/**
 * Model ExcelDataLongClosedTableData
 * 
 */
export type ExcelDataLongClosedTableData = $Result.DefaultSelection<Prisma.$ExcelDataLongClosedTableDataPayload>
/**
 * Model TradingPairLongComponent
 * 
 */
export type TradingPairLongComponent = $Result.DefaultSelection<Prisma.$TradingPairLongComponentPayload>
/**
 * Model TradingPairShortComponent
 * 
 */
export type TradingPairShortComponent = $Result.DefaultSelection<Prisma.$TradingPairShortComponentPayload>
/**
 * Model ExcelData
 * 
 */
export type ExcelData = $Result.DefaultSelection<Prisma.$ExcelDataPayload>
/**
 * Model TradingPair
 * 
 */
export type TradingPair = $Result.DefaultSelection<Prisma.$TradingPairPayload>
/**
 * Model users
 * 
 */
export type users = $Result.DefaultSelection<Prisma.$usersPayload>
/**
 * Model SchwabToken
 * 
 */
export type SchwabToken = $Result.DefaultSelection<Prisma.$SchwabTokenPayload>
/**
 * Model UserSymbols
 * 
 */
export type UserSymbols = $Result.DefaultSelection<Prisma.$UserSymbolsPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more ExcelData
 * const excelData = await prisma.excelData.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more ExcelData
   * const excelData = await prisma.excelData.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P]): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number }): $Utils.JsPromise<R>

  /**
   * Executes a raw MongoDB command and returns the result of it.
   * @example
   * ```
   * const user = await prisma.$runCommandRaw({
   *   aggregate: 'User',
   *   pipeline: [{ $match: { name: 'Bob' } }, { $project: { email: true, _id: false } }],
   *   explain: false,
   * })
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $runCommandRaw(command: Prisma.InputJsonObject): Prisma.PrismaPromise<Prisma.JsonObject>

  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.excelData`: Exposes CRUD operations for the **ExcelData** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ExcelData
    * const excelData = await prisma.excelData.findMany()
    * ```
    */
  get excelData(): Prisma.ExcelDataDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tradingPair`: Exposes CRUD operations for the **TradingPair** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TradingPairs
    * const tradingPairs = await prisma.tradingPair.findMany()
    * ```
    */
  get tradingPair(): Prisma.TradingPairDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.users`: Exposes CRUD operations for the **users** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.users.findMany()
    * ```
    */
  get users(): Prisma.usersDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.schwabToken`: Exposes CRUD operations for the **SchwabToken** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more SchwabTokens
    * const schwabTokens = await prisma.schwabToken.findMany()
    * ```
    */
  get schwabToken(): Prisma.SchwabTokenDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.userSymbols`: Exposes CRUD operations for the **UserSymbols** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more UserSymbols
    * const userSymbols = await prisma.userSymbols.findMany()
    * ```
    */
  get userSymbols(): Prisma.UserSymbolsDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.7.0
   * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    ExcelData: 'ExcelData',
    TradingPair: 'TradingPair',
    users: 'users',
    SchwabToken: 'SchwabToken',
    UserSymbols: 'UserSymbols'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "excelData" | "tradingPair" | "users" | "schwabToken" | "userSymbols"
      txIsolationLevel: never
    }
    model: {
      ExcelData: {
        payload: Prisma.$ExcelDataPayload<ExtArgs>
        fields: Prisma.ExcelDataFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ExcelDataFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ExcelDataFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          findFirst: {
            args: Prisma.ExcelDataFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ExcelDataFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          findMany: {
            args: Prisma.ExcelDataFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>[]
          }
          create: {
            args: Prisma.ExcelDataCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          createMany: {
            args: Prisma.ExcelDataCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.ExcelDataDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          update: {
            args: Prisma.ExcelDataUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          deleteMany: {
            args: Prisma.ExcelDataDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ExcelDataUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.ExcelDataUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ExcelDataPayload>
          }
          aggregate: {
            args: Prisma.ExcelDataAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateExcelData>
          }
          groupBy: {
            args: Prisma.ExcelDataGroupByArgs<ExtArgs>
            result: $Utils.Optional<ExcelDataGroupByOutputType>[]
          }
          findRaw: {
            args: Prisma.ExcelDataFindRawArgs<ExtArgs>
            result: JsonObject
          }
          aggregateRaw: {
            args: Prisma.ExcelDataAggregateRawArgs<ExtArgs>
            result: JsonObject
          }
          count: {
            args: Prisma.ExcelDataCountArgs<ExtArgs>
            result: $Utils.Optional<ExcelDataCountAggregateOutputType> | number
          }
        }
      }
      TradingPair: {
        payload: Prisma.$TradingPairPayload<ExtArgs>
        fields: Prisma.TradingPairFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TradingPairFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TradingPairFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          findFirst: {
            args: Prisma.TradingPairFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TradingPairFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          findMany: {
            args: Prisma.TradingPairFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>[]
          }
          create: {
            args: Prisma.TradingPairCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          createMany: {
            args: Prisma.TradingPairCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.TradingPairDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          update: {
            args: Prisma.TradingPairUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          deleteMany: {
            args: Prisma.TradingPairDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TradingPairUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.TradingPairUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TradingPairPayload>
          }
          aggregate: {
            args: Prisma.TradingPairAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTradingPair>
          }
          groupBy: {
            args: Prisma.TradingPairGroupByArgs<ExtArgs>
            result: $Utils.Optional<TradingPairGroupByOutputType>[]
          }
          findRaw: {
            args: Prisma.TradingPairFindRawArgs<ExtArgs>
            result: JsonObject
          }
          aggregateRaw: {
            args: Prisma.TradingPairAggregateRawArgs<ExtArgs>
            result: JsonObject
          }
          count: {
            args: Prisma.TradingPairCountArgs<ExtArgs>
            result: $Utils.Optional<TradingPairCountAggregateOutputType> | number
          }
        }
      }
      users: {
        payload: Prisma.$usersPayload<ExtArgs>
        fields: Prisma.usersFieldRefs
        operations: {
          findUnique: {
            args: Prisma.usersFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.usersFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          findFirst: {
            args: Prisma.usersFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.usersFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          findMany: {
            args: Prisma.usersFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>[]
          }
          create: {
            args: Prisma.usersCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          createMany: {
            args: Prisma.usersCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.usersDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          update: {
            args: Prisma.usersUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          deleteMany: {
            args: Prisma.usersDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.usersUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.usersUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          aggregate: {
            args: Prisma.UsersAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUsers>
          }
          groupBy: {
            args: Prisma.usersGroupByArgs<ExtArgs>
            result: $Utils.Optional<UsersGroupByOutputType>[]
          }
          findRaw: {
            args: Prisma.usersFindRawArgs<ExtArgs>
            result: JsonObject
          }
          aggregateRaw: {
            args: Prisma.usersAggregateRawArgs<ExtArgs>
            result: JsonObject
          }
          count: {
            args: Prisma.usersCountArgs<ExtArgs>
            result: $Utils.Optional<UsersCountAggregateOutputType> | number
          }
        }
      }
      SchwabToken: {
        payload: Prisma.$SchwabTokenPayload<ExtArgs>
        fields: Prisma.SchwabTokenFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SchwabTokenFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SchwabTokenFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          findFirst: {
            args: Prisma.SchwabTokenFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SchwabTokenFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          findMany: {
            args: Prisma.SchwabTokenFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>[]
          }
          create: {
            args: Prisma.SchwabTokenCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          createMany: {
            args: Prisma.SchwabTokenCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.SchwabTokenDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          update: {
            args: Prisma.SchwabTokenUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          deleteMany: {
            args: Prisma.SchwabTokenDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SchwabTokenUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.SchwabTokenUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SchwabTokenPayload>
          }
          aggregate: {
            args: Prisma.SchwabTokenAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSchwabToken>
          }
          groupBy: {
            args: Prisma.SchwabTokenGroupByArgs<ExtArgs>
            result: $Utils.Optional<SchwabTokenGroupByOutputType>[]
          }
          findRaw: {
            args: Prisma.SchwabTokenFindRawArgs<ExtArgs>
            result: JsonObject
          }
          aggregateRaw: {
            args: Prisma.SchwabTokenAggregateRawArgs<ExtArgs>
            result: JsonObject
          }
          count: {
            args: Prisma.SchwabTokenCountArgs<ExtArgs>
            result: $Utils.Optional<SchwabTokenCountAggregateOutputType> | number
          }
        }
      }
      UserSymbols: {
        payload: Prisma.$UserSymbolsPayload<ExtArgs>
        fields: Prisma.UserSymbolsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserSymbolsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserSymbolsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          findFirst: {
            args: Prisma.UserSymbolsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserSymbolsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          findMany: {
            args: Prisma.UserSymbolsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>[]
          }
          create: {
            args: Prisma.UserSymbolsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          createMany: {
            args: Prisma.UserSymbolsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.UserSymbolsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          update: {
            args: Prisma.UserSymbolsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          deleteMany: {
            args: Prisma.UserSymbolsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserSymbolsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserSymbolsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserSymbolsPayload>
          }
          aggregate: {
            args: Prisma.UserSymbolsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUserSymbols>
          }
          groupBy: {
            args: Prisma.UserSymbolsGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserSymbolsGroupByOutputType>[]
          }
          findRaw: {
            args: Prisma.UserSymbolsFindRawArgs<ExtArgs>
            result: JsonObject
          }
          aggregateRaw: {
            args: Prisma.UserSymbolsAggregateRawArgs<ExtArgs>
            result: JsonObject
          }
          count: {
            args: Prisma.UserSymbolsCountArgs<ExtArgs>
            result: $Utils.Optional<UserSymbolsCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $runCommandRaw: {
          args: Prisma.InputJsonObject,
          result: Prisma.JsonObject
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    excelData?: ExcelDataOmit
    tradingPair?: TradingPairOmit
    users?: usersOmit
    schwabToken?: SchwabTokenOmit
    userSymbols?: UserSymbolsOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model ExcelDataLongLoadedTableData
   */





  export type ExcelDataLongLoadedTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataLongLoadedTableData"]>



  export type ExcelDataLongLoadedTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataLongLoadedTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataLongLoadedTableData"]>

  export type $ExcelDataLongLoadedTableDataPayload = {
    name: "ExcelDataLongLoadedTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataLongLoadedTableDataGetPayload<S extends boolean | null | undefined | ExcelDataLongLoadedTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataLongLoadedTableDataPayload, S>





  /**
   * Fields of the ExcelDataLongLoadedTableData model
   */
  interface ExcelDataLongLoadedTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly status: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataLongLoadedTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataLongLoadedTableData without action
   */
  export type ExcelDataLongLoadedTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataLongLoadedTableData
     */
    select?: ExcelDataLongLoadedTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataLongLoadedTableData
     */
    omit?: ExcelDataLongLoadedTableDataOmit<ExtArgs> | null
  }


  /**
   * Model ExcelDataLongOpenTableData
   */





  export type ExcelDataLongOpenTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataLongOpenTableData"]>



  export type ExcelDataLongOpenTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataLongOpenTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataLongOpenTableData"]>

  export type $ExcelDataLongOpenTableDataPayload = {
    name: "ExcelDataLongOpenTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataLongOpenTableDataGetPayload<S extends boolean | null | undefined | ExcelDataLongOpenTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataLongOpenTableDataPayload, S>





  /**
   * Fields of the ExcelDataLongOpenTableData model
   */
  interface ExcelDataLongOpenTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly status: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataLongOpenTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataLongOpenTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataLongOpenTableData without action
   */
  export type ExcelDataLongOpenTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataLongOpenTableData
     */
    select?: ExcelDataLongOpenTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataLongOpenTableData
     */
    omit?: ExcelDataLongOpenTableDataOmit<ExtArgs> | null
  }


  /**
   * Model ExcelDataShortLoadedTableData
   */





  export type ExcelDataShortLoadedTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataShortLoadedTableData"]>



  export type ExcelDataShortLoadedTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataShortLoadedTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataShortLoadedTableData"]>

  export type $ExcelDataShortLoadedTableDataPayload = {
    name: "ExcelDataShortLoadedTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataShortLoadedTableDataGetPayload<S extends boolean | null | undefined | ExcelDataShortLoadedTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataShortLoadedTableDataPayload, S>





  /**
   * Fields of the ExcelDataShortLoadedTableData model
   */
  interface ExcelDataShortLoadedTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly status: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataShortLoadedTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataShortLoadedTableData without action
   */
  export type ExcelDataShortLoadedTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataShortLoadedTableData
     */
    select?: ExcelDataShortLoadedTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataShortLoadedTableData
     */
    omit?: ExcelDataShortLoadedTableDataOmit<ExtArgs> | null
  }


  /**
   * Model ExcelDataShortOpenTableData
   */





  export type ExcelDataShortOpenTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataShortOpenTableData"]>



  export type ExcelDataShortOpenTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataShortOpenTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataShortOpenTableData"]>

  export type $ExcelDataShortOpenTableDataPayload = {
    name: "ExcelDataShortOpenTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataShortOpenTableDataGetPayload<S extends boolean | null | undefined | ExcelDataShortOpenTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataShortOpenTableDataPayload, S>





  /**
   * Fields of the ExcelDataShortOpenTableData model
   */
  interface ExcelDataShortOpenTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly status: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataShortOpenTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataShortOpenTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataShortOpenTableData without action
   */
  export type ExcelDataShortOpenTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataShortOpenTableData
     */
    select?: ExcelDataShortOpenTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataShortOpenTableData
     */
    omit?: ExcelDataShortOpenTableDataOmit<ExtArgs> | null
  }


  /**
   * Model ExcelDataShortClosedTableData
   */





  export type ExcelDataShortClosedTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataShortClosedTableData"]>



  export type ExcelDataShortClosedTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataShortClosedTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataShortClosedTableData"]>

  export type $ExcelDataShortClosedTableDataPayload = {
    name: "ExcelDataShortClosedTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataShortClosedTableDataGetPayload<S extends boolean | null | undefined | ExcelDataShortClosedTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataShortClosedTableDataPayload, S>





  /**
   * Fields of the ExcelDataShortClosedTableData model
   */
  interface ExcelDataShortClosedTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly status: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataShortClosedTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataShortClosedTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataShortClosedTableData without action
   */
  export type ExcelDataShortClosedTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataShortClosedTableData
     */
    select?: ExcelDataShortClosedTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataShortClosedTableData
     */
    omit?: ExcelDataShortClosedTableDataOmit<ExtArgs> | null
  }


  /**
   * Model ExcelDataLongClosedTableData
   */





  export type ExcelDataLongClosedTableDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }, ExtArgs["result"]["excelDataLongClosedTableData"]>



  export type ExcelDataLongClosedTableDataSelectScalar = {
    id?: boolean
    sector?: boolean
    shares?: boolean
    spread?: boolean
    status?: boolean
    ticker?: boolean
    volume?: boolean
    dividend?: boolean
  }

  export type ExcelDataLongClosedTableDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sector" | "shares" | "spread" | "status" | "ticker" | "volume" | "dividend", ExtArgs["result"]["excelDataLongClosedTableData"]>

  export type $ExcelDataLongClosedTableDataPayload = {
    name: "ExcelDataLongClosedTableData"
    objects: {}
    scalars: {
      id: string
      sector: string
      shares: string
      spread: string
      status: string
      ticker: string
      volume: string
      dividend: string
    }
    composites: {}
  }

  type ExcelDataLongClosedTableDataGetPayload<S extends boolean | null | undefined | ExcelDataLongClosedTableDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataLongClosedTableDataPayload, S>





  /**
   * Fields of the ExcelDataLongClosedTableData model
   */
  interface ExcelDataLongClosedTableDataFieldRefs {
    readonly id: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly sector: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly shares: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly spread: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly status: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly ticker: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly volume: FieldRef<"ExcelDataLongClosedTableData", 'String'>
    readonly dividend: FieldRef<"ExcelDataLongClosedTableData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelDataLongClosedTableData without action
   */
  export type ExcelDataLongClosedTableDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelDataLongClosedTableData
     */
    select?: ExcelDataLongClosedTableDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelDataLongClosedTableData
     */
    omit?: ExcelDataLongClosedTableDataOmit<ExtArgs> | null
  }


  /**
   * Model TradingPairLongComponent
   */





  export type TradingPairLongComponentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    dividendUserValue?: boolean
    dollarCost?: boolean
    expectedQuantity?: boolean
    formattedAmt?: boolean
    formattedAsk?: boolean
    formattedBid?: boolean
    formattedChange?: boolean
    formattedCost?: boolean
    formattedDividend?: boolean
    formattedLast?: boolean
    formattedLoadedVolume?: boolean
    formattedSpreadUser?: boolean
    formattedUserDividend?: boolean
    formattedVolume?: boolean
    id?: boolean
    pnl?: boolean
    sectorValue?: boolean
    spreadUserValue?: boolean
    spreadValue?: boolean
    statusValue?: boolean
    ticker?: boolean
  }, ExtArgs["result"]["tradingPairLongComponent"]>



  export type TradingPairLongComponentSelectScalar = {
    dividendUserValue?: boolean
    dollarCost?: boolean
    expectedQuantity?: boolean
    formattedAmt?: boolean
    formattedAsk?: boolean
    formattedBid?: boolean
    formattedChange?: boolean
    formattedCost?: boolean
    formattedDividend?: boolean
    formattedLast?: boolean
    formattedLoadedVolume?: boolean
    formattedSpreadUser?: boolean
    formattedUserDividend?: boolean
    formattedVolume?: boolean
    id?: boolean
    pnl?: boolean
    sectorValue?: boolean
    spreadUserValue?: boolean
    spreadValue?: boolean
    statusValue?: boolean
    ticker?: boolean
  }

  export type TradingPairLongComponentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"dividendUserValue" | "dollarCost" | "expectedQuantity" | "formattedAmt" | "formattedAsk" | "formattedBid" | "formattedChange" | "formattedCost" | "formattedDividend" | "formattedLast" | "formattedLoadedVolume" | "formattedSpreadUser" | "formattedUserDividend" | "formattedVolume" | "id" | "pnl" | "sectorValue" | "spreadUserValue" | "spreadValue" | "statusValue" | "ticker", ExtArgs["result"]["tradingPairLongComponent"]>

  export type $TradingPairLongComponentPayload = {
    name: "TradingPairLongComponent"
    objects: {}
    scalars: {
      dividendUserValue: number
      dollarCost: number
      /**
       * Multiple data types found: String: 71.4%, Int: 28.6% out of 7 sampled entries
       */
      expectedQuantity: Prisma.JsonValue
      formattedAmt: string
      formattedAsk: string
      formattedBid: string
      formattedChange: string
      formattedCost: string
      formattedDividend: string
      formattedLast: string
      formattedLoadedVolume: string
      formattedSpreadUser: string
      formattedUserDividend: string
      formattedVolume: string
      id: string
      pnl: number
      sectorValue: string
      /**
       * Multiple data types found: String: 71.4%, Int: 28.6% out of 7 sampled entries
       */
      spreadUserValue: Prisma.JsonValue
      spreadValue: number
      statusValue: string
      ticker: string
    }
    composites: {}
  }

  type TradingPairLongComponentGetPayload<S extends boolean | null | undefined | TradingPairLongComponentDefaultArgs> = $Result.GetResult<Prisma.$TradingPairLongComponentPayload, S>





  /**
   * Fields of the TradingPairLongComponent model
   */
  interface TradingPairLongComponentFieldRefs {
    readonly dividendUserValue: FieldRef<"TradingPairLongComponent", 'Int'>
    readonly dollarCost: FieldRef<"TradingPairLongComponent", 'Int'>
    readonly expectedQuantity: FieldRef<"TradingPairLongComponent", 'Json'>
    readonly formattedAmt: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedAsk: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedBid: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedChange: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedCost: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedDividend: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedLast: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedLoadedVolume: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedSpreadUser: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedUserDividend: FieldRef<"TradingPairLongComponent", 'String'>
    readonly formattedVolume: FieldRef<"TradingPairLongComponent", 'String'>
    readonly id: FieldRef<"TradingPairLongComponent", 'String'>
    readonly pnl: FieldRef<"TradingPairLongComponent", 'Int'>
    readonly sectorValue: FieldRef<"TradingPairLongComponent", 'String'>
    readonly spreadUserValue: FieldRef<"TradingPairLongComponent", 'Json'>
    readonly spreadValue: FieldRef<"TradingPairLongComponent", 'Int'>
    readonly statusValue: FieldRef<"TradingPairLongComponent", 'String'>
    readonly ticker: FieldRef<"TradingPairLongComponent", 'String'>
  }
    

  // Custom InputTypes
  /**
   * TradingPairLongComponent without action
   */
  export type TradingPairLongComponentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPairLongComponent
     */
    select?: TradingPairLongComponentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPairLongComponent
     */
    omit?: TradingPairLongComponentOmit<ExtArgs> | null
  }


  /**
   * Model TradingPairShortComponent
   */





  export type TradingPairShortComponentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    dividendUserValue?: boolean
    dollarCost?: boolean
    expectedQuantity?: boolean
    formattedAmt?: boolean
    formattedAsk?: boolean
    formattedBid?: boolean
    formattedChange?: boolean
    formattedCost?: boolean
    formattedDividend?: boolean
    formattedLast?: boolean
    formattedLoadedVolume?: boolean
    formattedSpreadUser?: boolean
    formattedUserDividend?: boolean
    formattedVolume?: boolean
    id?: boolean
    pnl?: boolean
    sectorValue?: boolean
    spreadUserValue?: boolean
    spreadValue?: boolean
    statusValue?: boolean
    ticker?: boolean
  }, ExtArgs["result"]["tradingPairShortComponent"]>



  export type TradingPairShortComponentSelectScalar = {
    dividendUserValue?: boolean
    dollarCost?: boolean
    expectedQuantity?: boolean
    formattedAmt?: boolean
    formattedAsk?: boolean
    formattedBid?: boolean
    formattedChange?: boolean
    formattedCost?: boolean
    formattedDividend?: boolean
    formattedLast?: boolean
    formattedLoadedVolume?: boolean
    formattedSpreadUser?: boolean
    formattedUserDividend?: boolean
    formattedVolume?: boolean
    id?: boolean
    pnl?: boolean
    sectorValue?: boolean
    spreadUserValue?: boolean
    spreadValue?: boolean
    statusValue?: boolean
    ticker?: boolean
  }

  export type TradingPairShortComponentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"dividendUserValue" | "dollarCost" | "expectedQuantity" | "formattedAmt" | "formattedAsk" | "formattedBid" | "formattedChange" | "formattedCost" | "formattedDividend" | "formattedLast" | "formattedLoadedVolume" | "formattedSpreadUser" | "formattedUserDividend" | "formattedVolume" | "id" | "pnl" | "sectorValue" | "spreadUserValue" | "spreadValue" | "statusValue" | "ticker", ExtArgs["result"]["tradingPairShortComponent"]>

  export type $TradingPairShortComponentPayload = {
    name: "TradingPairShortComponent"
    objects: {}
    scalars: {
      dividendUserValue: number
      dollarCost: number
      expectedQuantity: string
      formattedAmt: string
      formattedAsk: string
      formattedBid: string
      formattedChange: string
      formattedCost: string
      formattedDividend: string
      formattedLast: string
      formattedLoadedVolume: string
      formattedSpreadUser: string
      formattedUserDividend: string
      formattedVolume: string
      id: string
      pnl: number
      sectorValue: string
      spreadUserValue: string
      /**
       * Multiple data types found: Float: 14.3%, Int: 85.7% out of 7 sampled entries
       */
      spreadValue: Prisma.JsonValue
      statusValue: string
      ticker: string
    }
    composites: {}
  }

  type TradingPairShortComponentGetPayload<S extends boolean | null | undefined | TradingPairShortComponentDefaultArgs> = $Result.GetResult<Prisma.$TradingPairShortComponentPayload, S>





  /**
   * Fields of the TradingPairShortComponent model
   */
  interface TradingPairShortComponentFieldRefs {
    readonly dividendUserValue: FieldRef<"TradingPairShortComponent", 'Int'>
    readonly dollarCost: FieldRef<"TradingPairShortComponent", 'Int'>
    readonly expectedQuantity: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedAmt: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedAsk: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedBid: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedChange: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedCost: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedDividend: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedLast: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedLoadedVolume: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedSpreadUser: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedUserDividend: FieldRef<"TradingPairShortComponent", 'String'>
    readonly formattedVolume: FieldRef<"TradingPairShortComponent", 'String'>
    readonly id: FieldRef<"TradingPairShortComponent", 'String'>
    readonly pnl: FieldRef<"TradingPairShortComponent", 'Int'>
    readonly sectorValue: FieldRef<"TradingPairShortComponent", 'String'>
    readonly spreadUserValue: FieldRef<"TradingPairShortComponent", 'String'>
    readonly spreadValue: FieldRef<"TradingPairShortComponent", 'Json'>
    readonly statusValue: FieldRef<"TradingPairShortComponent", 'String'>
    readonly ticker: FieldRef<"TradingPairShortComponent", 'String'>
  }
    

  // Custom InputTypes
  /**
   * TradingPairShortComponent without action
   */
  export type TradingPairShortComponentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPairShortComponent
     */
    select?: TradingPairShortComponentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPairShortComponent
     */
    omit?: TradingPairShortComponentOmit<ExtArgs> | null
  }


  /**
   * Model ExcelData
   */

  export type AggregateExcelData = {
    _count: ExcelDataCountAggregateOutputType | null
    _min: ExcelDataMinAggregateOutputType | null
    _max: ExcelDataMaxAggregateOutputType | null
  }

  export type ExcelDataMinAggregateOutputType = {
    id: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: string | null
  }

  export type ExcelDataMaxAggregateOutputType = {
    id: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: string | null
  }

  export type ExcelDataCountAggregateOutputType = {
    id: number
    createdAt: number
    updatedAt: number
    userId: number
    _all: number
  }


  export type ExcelDataMinAggregateInputType = {
    id?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
  }

  export type ExcelDataMaxAggregateInputType = {
    id?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
  }

  export type ExcelDataCountAggregateInputType = {
    id?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    _all?: true
  }

  export type ExcelDataAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ExcelData to aggregate.
     */
    where?: ExcelDataWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ExcelData to fetch.
     */
    orderBy?: ExcelDataOrderByWithRelationInput | ExcelDataOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ExcelDataWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ExcelData from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ExcelData.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ExcelData
    **/
    _count?: true | ExcelDataCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ExcelDataMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ExcelDataMaxAggregateInputType
  }

  export type GetExcelDataAggregateType<T extends ExcelDataAggregateArgs> = {
        [P in keyof T & keyof AggregateExcelData]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateExcelData[P]>
      : GetScalarType<T[P], AggregateExcelData[P]>
  }




  export type ExcelDataGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ExcelDataWhereInput
    orderBy?: ExcelDataOrderByWithAggregationInput | ExcelDataOrderByWithAggregationInput[]
    by: ExcelDataScalarFieldEnum[] | ExcelDataScalarFieldEnum
    having?: ExcelDataScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ExcelDataCountAggregateInputType | true
    _min?: ExcelDataMinAggregateInputType
    _max?: ExcelDataMaxAggregateInputType
  }

  export type ExcelDataGroupByOutputType = {
    id: string
    createdAt: Date
    updatedAt: Date
    userId: string
    _count: ExcelDataCountAggregateOutputType | null
    _min: ExcelDataMinAggregateOutputType | null
    _max: ExcelDataMaxAggregateOutputType | null
  }

  type GetExcelDataGroupByPayload<T extends ExcelDataGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ExcelDataGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ExcelDataGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ExcelDataGroupByOutputType[P]>
            : GetScalarType<T[P], ExcelDataGroupByOutputType[P]>
        }
      >
    >


  export type ExcelDataSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    createdAt?: boolean
    longLoadedTableData?: boolean | ExcelDataLongLoadedTableDataDefaultArgs<ExtArgs>
    longOpenTableData?: boolean | ExcelDataLongOpenTableDataDefaultArgs<ExtArgs>
    shortLoadedTableData?: boolean | ExcelDataShortLoadedTableDataDefaultArgs<ExtArgs>
    shortOpenTableData?: boolean | ExcelDataShortOpenTableDataDefaultArgs<ExtArgs>
    shortClosedTableData?: boolean | ExcelDataShortClosedTableDataDefaultArgs<ExtArgs>
    longClosedTableData?: boolean | ExcelDataLongClosedTableDataDefaultArgs<ExtArgs>
    updatedAt?: boolean
    userId?: boolean
  }, ExtArgs["result"]["excelData"]>



  export type ExcelDataSelectScalar = {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
  }

  export type ExcelDataOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "createdAt" | "longLoadedTableData" | "longOpenTableData" | "shortLoadedTableData" | "shortOpenTableData" | "shortClosedTableData" | "longClosedTableData" | "updatedAt" | "userId", ExtArgs["result"]["excelData"]>
  export type ExcelDataInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $ExcelDataPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ExcelData"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      createdAt: Date
      updatedAt: Date
      userId: string
    }, ExtArgs["result"]["excelData"]>
    composites: {
      longLoadedTableData: Prisma.$ExcelDataLongLoadedTableDataPayload[]
      longOpenTableData: Prisma.$ExcelDataLongOpenTableDataPayload[]
      shortLoadedTableData: Prisma.$ExcelDataShortLoadedTableDataPayload[]
      shortOpenTableData: Prisma.$ExcelDataShortOpenTableDataPayload[]
      shortClosedTableData: Prisma.$ExcelDataShortClosedTableDataPayload[]
      longClosedTableData: Prisma.$ExcelDataLongClosedTableDataPayload[]
    }
  }

  type ExcelDataGetPayload<S extends boolean | null | undefined | ExcelDataDefaultArgs> = $Result.GetResult<Prisma.$ExcelDataPayload, S>

  type ExcelDataCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ExcelDataFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ExcelDataCountAggregateInputType | true
    }

  export interface ExcelDataDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ExcelData'], meta: { name: 'ExcelData' } }
    /**
     * Find zero or one ExcelData that matches the filter.
     * @param {ExcelDataFindUniqueArgs} args - Arguments to find a ExcelData
     * @example
     * // Get one ExcelData
     * const excelData = await prisma.excelData.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ExcelDataFindUniqueArgs>(args: SelectSubset<T, ExcelDataFindUniqueArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ExcelData that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ExcelDataFindUniqueOrThrowArgs} args - Arguments to find a ExcelData
     * @example
     * // Get one ExcelData
     * const excelData = await prisma.excelData.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ExcelDataFindUniqueOrThrowArgs>(args: SelectSubset<T, ExcelDataFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ExcelData that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataFindFirstArgs} args - Arguments to find a ExcelData
     * @example
     * // Get one ExcelData
     * const excelData = await prisma.excelData.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ExcelDataFindFirstArgs>(args?: SelectSubset<T, ExcelDataFindFirstArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ExcelData that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataFindFirstOrThrowArgs} args - Arguments to find a ExcelData
     * @example
     * // Get one ExcelData
     * const excelData = await prisma.excelData.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ExcelDataFindFirstOrThrowArgs>(args?: SelectSubset<T, ExcelDataFindFirstOrThrowArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ExcelData that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ExcelData
     * const excelData = await prisma.excelData.findMany()
     * 
     * // Get first 10 ExcelData
     * const excelData = await prisma.excelData.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const excelDataWithIdOnly = await prisma.excelData.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ExcelDataFindManyArgs>(args?: SelectSubset<T, ExcelDataFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ExcelData.
     * @param {ExcelDataCreateArgs} args - Arguments to create a ExcelData.
     * @example
     * // Create one ExcelData
     * const ExcelData = await prisma.excelData.create({
     *   data: {
     *     // ... data to create a ExcelData
     *   }
     * })
     * 
     */
    create<T extends ExcelDataCreateArgs>(args: SelectSubset<T, ExcelDataCreateArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ExcelData.
     * @param {ExcelDataCreateManyArgs} args - Arguments to create many ExcelData.
     * @example
     * // Create many ExcelData
     * const excelData = await prisma.excelData.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ExcelDataCreateManyArgs>(args?: SelectSubset<T, ExcelDataCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a ExcelData.
     * @param {ExcelDataDeleteArgs} args - Arguments to delete one ExcelData.
     * @example
     * // Delete one ExcelData
     * const ExcelData = await prisma.excelData.delete({
     *   where: {
     *     // ... filter to delete one ExcelData
     *   }
     * })
     * 
     */
    delete<T extends ExcelDataDeleteArgs>(args: SelectSubset<T, ExcelDataDeleteArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ExcelData.
     * @param {ExcelDataUpdateArgs} args - Arguments to update one ExcelData.
     * @example
     * // Update one ExcelData
     * const excelData = await prisma.excelData.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ExcelDataUpdateArgs>(args: SelectSubset<T, ExcelDataUpdateArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ExcelData.
     * @param {ExcelDataDeleteManyArgs} args - Arguments to filter ExcelData to delete.
     * @example
     * // Delete a few ExcelData
     * const { count } = await prisma.excelData.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ExcelDataDeleteManyArgs>(args?: SelectSubset<T, ExcelDataDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ExcelData.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ExcelData
     * const excelData = await prisma.excelData.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ExcelDataUpdateManyArgs>(args: SelectSubset<T, ExcelDataUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one ExcelData.
     * @param {ExcelDataUpsertArgs} args - Arguments to update or create a ExcelData.
     * @example
     * // Update or create a ExcelData
     * const excelData = await prisma.excelData.upsert({
     *   create: {
     *     // ... data to create a ExcelData
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ExcelData we want to update
     *   }
     * })
     */
    upsert<T extends ExcelDataUpsertArgs>(args: SelectSubset<T, ExcelDataUpsertArgs<ExtArgs>>): Prisma__ExcelDataClient<$Result.GetResult<Prisma.$ExcelDataPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ExcelData that matches the filter.
     * @param {ExcelDataFindRawArgs} args - Select which filters you would like to apply.
     * @example
     * const excelData = await prisma.excelData.findRaw({
     *   filter: { age: { $gt: 25 } }
     * })
     */
    findRaw(args?: ExcelDataFindRawArgs): Prisma.PrismaPromise<JsonObject>

    /**
     * Perform aggregation operations on a ExcelData.
     * @param {ExcelDataAggregateRawArgs} args - Select which aggregations you would like to apply.
     * @example
     * const excelData = await prisma.excelData.aggregateRaw({
     *   pipeline: [
     *     { $match: { status: "registered" } },
     *     { $group: { _id: "$country", total: { $sum: 1 } } }
     *   ]
     * })
     */
    aggregateRaw(args?: ExcelDataAggregateRawArgs): Prisma.PrismaPromise<JsonObject>


    /**
     * Count the number of ExcelData.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataCountArgs} args - Arguments to filter ExcelData to count.
     * @example
     * // Count the number of ExcelData
     * const count = await prisma.excelData.count({
     *   where: {
     *     // ... the filter for the ExcelData we want to count
     *   }
     * })
    **/
    count<T extends ExcelDataCountArgs>(
      args?: Subset<T, ExcelDataCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ExcelDataCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ExcelData.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ExcelDataAggregateArgs>(args: Subset<T, ExcelDataAggregateArgs>): Prisma.PrismaPromise<GetExcelDataAggregateType<T>>

    /**
     * Group by ExcelData.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ExcelDataGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ExcelDataGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ExcelDataGroupByArgs['orderBy'] }
        : { orderBy?: ExcelDataGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ExcelDataGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetExcelDataGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ExcelData model
   */
  readonly fields: ExcelDataFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ExcelData.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ExcelDataClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ExcelData model
   */
  interface ExcelDataFieldRefs {
    readonly id: FieldRef<"ExcelData", 'String'>
    readonly createdAt: FieldRef<"ExcelData", 'DateTime'>
    readonly updatedAt: FieldRef<"ExcelData", 'DateTime'>
    readonly userId: FieldRef<"ExcelData", 'String'>
  }
    

  // Custom InputTypes
  /**
   * ExcelData findUnique
   */
  export type ExcelDataFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter, which ExcelData to fetch.
     */
    where: ExcelDataWhereUniqueInput
  }

  /**
   * ExcelData findUniqueOrThrow
   */
  export type ExcelDataFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter, which ExcelData to fetch.
     */
    where: ExcelDataWhereUniqueInput
  }

  /**
   * ExcelData findFirst
   */
  export type ExcelDataFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter, which ExcelData to fetch.
     */
    where?: ExcelDataWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ExcelData to fetch.
     */
    orderBy?: ExcelDataOrderByWithRelationInput | ExcelDataOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ExcelData.
     */
    cursor?: ExcelDataWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ExcelData from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ExcelData.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ExcelData.
     */
    distinct?: ExcelDataScalarFieldEnum | ExcelDataScalarFieldEnum[]
  }

  /**
   * ExcelData findFirstOrThrow
   */
  export type ExcelDataFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter, which ExcelData to fetch.
     */
    where?: ExcelDataWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ExcelData to fetch.
     */
    orderBy?: ExcelDataOrderByWithRelationInput | ExcelDataOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ExcelData.
     */
    cursor?: ExcelDataWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ExcelData from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ExcelData.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ExcelData.
     */
    distinct?: ExcelDataScalarFieldEnum | ExcelDataScalarFieldEnum[]
  }

  /**
   * ExcelData findMany
   */
  export type ExcelDataFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter, which ExcelData to fetch.
     */
    where?: ExcelDataWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ExcelData to fetch.
     */
    orderBy?: ExcelDataOrderByWithRelationInput | ExcelDataOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ExcelData.
     */
    cursor?: ExcelDataWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ExcelData from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ExcelData.
     */
    skip?: number
    distinct?: ExcelDataScalarFieldEnum | ExcelDataScalarFieldEnum[]
  }

  /**
   * ExcelData create
   */
  export type ExcelDataCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * The data needed to create a ExcelData.
     */
    data: XOR<ExcelDataCreateInput, ExcelDataUncheckedCreateInput>
  }

  /**
   * ExcelData createMany
   */
  export type ExcelDataCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ExcelData.
     */
    data: ExcelDataCreateManyInput | ExcelDataCreateManyInput[]
  }

  /**
   * ExcelData update
   */
  export type ExcelDataUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * The data needed to update a ExcelData.
     */
    data: XOR<ExcelDataUpdateInput, ExcelDataUncheckedUpdateInput>
    /**
     * Choose, which ExcelData to update.
     */
    where: ExcelDataWhereUniqueInput
  }

  /**
   * ExcelData updateMany
   */
  export type ExcelDataUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ExcelData.
     */
    data: XOR<ExcelDataUpdateManyMutationInput, ExcelDataUncheckedUpdateManyInput>
    /**
     * Filter which ExcelData to update
     */
    where?: ExcelDataWhereInput
    /**
     * Limit how many ExcelData to update.
     */
    limit?: number
  }

  /**
   * ExcelData upsert
   */
  export type ExcelDataUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * The filter to search for the ExcelData to update in case it exists.
     */
    where: ExcelDataWhereUniqueInput
    /**
     * In case the ExcelData found by the `where` argument doesn't exist, create a new ExcelData with this data.
     */
    create: XOR<ExcelDataCreateInput, ExcelDataUncheckedCreateInput>
    /**
     * In case the ExcelData was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ExcelDataUpdateInput, ExcelDataUncheckedUpdateInput>
  }

  /**
   * ExcelData delete
   */
  export type ExcelDataDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
    /**
     * Filter which ExcelData to delete.
     */
    where: ExcelDataWhereUniqueInput
  }

  /**
   * ExcelData deleteMany
   */
  export type ExcelDataDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ExcelData to delete
     */
    where?: ExcelDataWhereInput
    /**
     * Limit how many ExcelData to delete.
     */
    limit?: number
  }

  /**
   * ExcelData findRaw
   */
  export type ExcelDataFindRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The query predicate filter. If unspecified, then all documents in the collection will match the predicate. ${@link https://docs.mongodb.com/manual/reference/operator/query MongoDB Docs}.
     */
    filter?: InputJsonValue
    /**
     * Additional options to pass to the `find` command ${@link https://docs.mongodb.com/manual/reference/command/find/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * ExcelData aggregateRaw
   */
  export type ExcelDataAggregateRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * An array of aggregation stages to process and transform the document stream via the aggregation pipeline. ${@link https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline MongoDB Docs}.
     */
    pipeline?: InputJsonValue[]
    /**
     * Additional options to pass to the `aggregate` command ${@link https://docs.mongodb.com/manual/reference/command/aggregate/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * ExcelData without action
   */
  export type ExcelDataDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ExcelData
     */
    select?: ExcelDataSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ExcelData
     */
    omit?: ExcelDataOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ExcelDataInclude<ExtArgs> | null
  }


  /**
   * Model TradingPair
   */

  export type AggregateTradingPair = {
    _count: TradingPairCountAggregateOutputType | null
    _min: TradingPairMinAggregateOutputType | null
    _max: TradingPairMaxAggregateOutputType | null
  }

  export type TradingPairMinAggregateOutputType = {
    id: string | null
    combinedPNL: string | null
    createdAt: Date | null
    status: string | null
    updatedAt: Date | null
    userId: string | null
  }

  export type TradingPairMaxAggregateOutputType = {
    id: string | null
    combinedPNL: string | null
    createdAt: Date | null
    status: string | null
    updatedAt: Date | null
    userId: string | null
  }

  export type TradingPairCountAggregateOutputType = {
    id: number
    combinedPNL: number
    createdAt: number
    pairKey: number
    status: number
    updatedAt: number
    userId: number
    _all: number
  }


  export type TradingPairMinAggregateInputType = {
    id?: true
    combinedPNL?: true
    createdAt?: true
    status?: true
    updatedAt?: true
    userId?: true
  }

  export type TradingPairMaxAggregateInputType = {
    id?: true
    combinedPNL?: true
    createdAt?: true
    status?: true
    updatedAt?: true
    userId?: true
  }

  export type TradingPairCountAggregateInputType = {
    id?: true
    combinedPNL?: true
    createdAt?: true
    pairKey?: true
    status?: true
    updatedAt?: true
    userId?: true
    _all?: true
  }

  export type TradingPairAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TradingPair to aggregate.
     */
    where?: TradingPairWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TradingPairs to fetch.
     */
    orderBy?: TradingPairOrderByWithRelationInput | TradingPairOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TradingPairWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TradingPairs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TradingPairs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TradingPairs
    **/
    _count?: true | TradingPairCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TradingPairMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TradingPairMaxAggregateInputType
  }

  export type GetTradingPairAggregateType<T extends TradingPairAggregateArgs> = {
        [P in keyof T & keyof AggregateTradingPair]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTradingPair[P]>
      : GetScalarType<T[P], AggregateTradingPair[P]>
  }




  export type TradingPairGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TradingPairWhereInput
    orderBy?: TradingPairOrderByWithAggregationInput | TradingPairOrderByWithAggregationInput[]
    by: TradingPairScalarFieldEnum[] | TradingPairScalarFieldEnum
    having?: TradingPairScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TradingPairCountAggregateInputType | true
    _min?: TradingPairMinAggregateInputType
    _max?: TradingPairMaxAggregateInputType
  }

  export type TradingPairGroupByOutputType = {
    id: string
    combinedPNL: string
    createdAt: Date
    pairKey: JsonValue | null
    status: string
    updatedAt: Date
    userId: string
    _count: TradingPairCountAggregateOutputType | null
    _min: TradingPairMinAggregateOutputType | null
    _max: TradingPairMaxAggregateOutputType | null
  }

  type GetTradingPairGroupByPayload<T extends TradingPairGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TradingPairGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TradingPairGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TradingPairGroupByOutputType[P]>
            : GetScalarType<T[P], TradingPairGroupByOutputType[P]>
        }
      >
    >


  export type TradingPairSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    combinedPNL?: boolean
    createdAt?: boolean
    longComponent?: boolean | TradingPairLongComponentDefaultArgs<ExtArgs>
    pairKey?: boolean
    shortComponent?: boolean | TradingPairShortComponentDefaultArgs<ExtArgs>
    status?: boolean
    updatedAt?: boolean
    userId?: boolean
  }, ExtArgs["result"]["tradingPair"]>



  export type TradingPairSelectScalar = {
    id?: boolean
    combinedPNL?: boolean
    createdAt?: boolean
    pairKey?: boolean
    status?: boolean
    updatedAt?: boolean
    userId?: boolean
  }

  export type TradingPairOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "combinedPNL" | "createdAt" | "longComponent" | "pairKey" | "shortComponent" | "status" | "updatedAt" | "userId", ExtArgs["result"]["tradingPair"]>
  export type TradingPairInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $TradingPairPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TradingPair"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      combinedPNL: string
      createdAt: Date
      /**
       * Could not determine type: the field only had null or empty values in the sample set.
       */
      pairKey: Prisma.JsonValue | null
      status: string
      updatedAt: Date
      userId: string
    }, ExtArgs["result"]["tradingPair"]>
    composites: {
      longComponent: Prisma.$TradingPairLongComponentPayload
      shortComponent: Prisma.$TradingPairShortComponentPayload
    }
  }

  type TradingPairGetPayload<S extends boolean | null | undefined | TradingPairDefaultArgs> = $Result.GetResult<Prisma.$TradingPairPayload, S>

  type TradingPairCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TradingPairFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TradingPairCountAggregateInputType | true
    }

  export interface TradingPairDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TradingPair'], meta: { name: 'TradingPair' } }
    /**
     * Find zero or one TradingPair that matches the filter.
     * @param {TradingPairFindUniqueArgs} args - Arguments to find a TradingPair
     * @example
     * // Get one TradingPair
     * const tradingPair = await prisma.tradingPair.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TradingPairFindUniqueArgs>(args: SelectSubset<T, TradingPairFindUniqueArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TradingPair that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TradingPairFindUniqueOrThrowArgs} args - Arguments to find a TradingPair
     * @example
     * // Get one TradingPair
     * const tradingPair = await prisma.tradingPair.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TradingPairFindUniqueOrThrowArgs>(args: SelectSubset<T, TradingPairFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TradingPair that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairFindFirstArgs} args - Arguments to find a TradingPair
     * @example
     * // Get one TradingPair
     * const tradingPair = await prisma.tradingPair.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TradingPairFindFirstArgs>(args?: SelectSubset<T, TradingPairFindFirstArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TradingPair that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairFindFirstOrThrowArgs} args - Arguments to find a TradingPair
     * @example
     * // Get one TradingPair
     * const tradingPair = await prisma.tradingPair.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TradingPairFindFirstOrThrowArgs>(args?: SelectSubset<T, TradingPairFindFirstOrThrowArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TradingPairs that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TradingPairs
     * const tradingPairs = await prisma.tradingPair.findMany()
     * 
     * // Get first 10 TradingPairs
     * const tradingPairs = await prisma.tradingPair.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const tradingPairWithIdOnly = await prisma.tradingPair.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TradingPairFindManyArgs>(args?: SelectSubset<T, TradingPairFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TradingPair.
     * @param {TradingPairCreateArgs} args - Arguments to create a TradingPair.
     * @example
     * // Create one TradingPair
     * const TradingPair = await prisma.tradingPair.create({
     *   data: {
     *     // ... data to create a TradingPair
     *   }
     * })
     * 
     */
    create<T extends TradingPairCreateArgs>(args: SelectSubset<T, TradingPairCreateArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TradingPairs.
     * @param {TradingPairCreateManyArgs} args - Arguments to create many TradingPairs.
     * @example
     * // Create many TradingPairs
     * const tradingPair = await prisma.tradingPair.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TradingPairCreateManyArgs>(args?: SelectSubset<T, TradingPairCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a TradingPair.
     * @param {TradingPairDeleteArgs} args - Arguments to delete one TradingPair.
     * @example
     * // Delete one TradingPair
     * const TradingPair = await prisma.tradingPair.delete({
     *   where: {
     *     // ... filter to delete one TradingPair
     *   }
     * })
     * 
     */
    delete<T extends TradingPairDeleteArgs>(args: SelectSubset<T, TradingPairDeleteArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TradingPair.
     * @param {TradingPairUpdateArgs} args - Arguments to update one TradingPair.
     * @example
     * // Update one TradingPair
     * const tradingPair = await prisma.tradingPair.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TradingPairUpdateArgs>(args: SelectSubset<T, TradingPairUpdateArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TradingPairs.
     * @param {TradingPairDeleteManyArgs} args - Arguments to filter TradingPairs to delete.
     * @example
     * // Delete a few TradingPairs
     * const { count } = await prisma.tradingPair.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TradingPairDeleteManyArgs>(args?: SelectSubset<T, TradingPairDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TradingPairs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TradingPairs
     * const tradingPair = await prisma.tradingPair.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TradingPairUpdateManyArgs>(args: SelectSubset<T, TradingPairUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one TradingPair.
     * @param {TradingPairUpsertArgs} args - Arguments to update or create a TradingPair.
     * @example
     * // Update or create a TradingPair
     * const tradingPair = await prisma.tradingPair.upsert({
     *   create: {
     *     // ... data to create a TradingPair
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TradingPair we want to update
     *   }
     * })
     */
    upsert<T extends TradingPairUpsertArgs>(args: SelectSubset<T, TradingPairUpsertArgs<ExtArgs>>): Prisma__TradingPairClient<$Result.GetResult<Prisma.$TradingPairPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TradingPairs that matches the filter.
     * @param {TradingPairFindRawArgs} args - Select which filters you would like to apply.
     * @example
     * const tradingPair = await prisma.tradingPair.findRaw({
     *   filter: { age: { $gt: 25 } }
     * })
     */
    findRaw(args?: TradingPairFindRawArgs): Prisma.PrismaPromise<JsonObject>

    /**
     * Perform aggregation operations on a TradingPair.
     * @param {TradingPairAggregateRawArgs} args - Select which aggregations you would like to apply.
     * @example
     * const tradingPair = await prisma.tradingPair.aggregateRaw({
     *   pipeline: [
     *     { $match: { status: "registered" } },
     *     { $group: { _id: "$country", total: { $sum: 1 } } }
     *   ]
     * })
     */
    aggregateRaw(args?: TradingPairAggregateRawArgs): Prisma.PrismaPromise<JsonObject>


    /**
     * Count the number of TradingPairs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairCountArgs} args - Arguments to filter TradingPairs to count.
     * @example
     * // Count the number of TradingPairs
     * const count = await prisma.tradingPair.count({
     *   where: {
     *     // ... the filter for the TradingPairs we want to count
     *   }
     * })
    **/
    count<T extends TradingPairCountArgs>(
      args?: Subset<T, TradingPairCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TradingPairCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TradingPair.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TradingPairAggregateArgs>(args: Subset<T, TradingPairAggregateArgs>): Prisma.PrismaPromise<GetTradingPairAggregateType<T>>

    /**
     * Group by TradingPair.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TradingPairGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TradingPairGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TradingPairGroupByArgs['orderBy'] }
        : { orderBy?: TradingPairGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TradingPairGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTradingPairGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TradingPair model
   */
  readonly fields: TradingPairFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TradingPair.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TradingPairClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TradingPair model
   */
  interface TradingPairFieldRefs {
    readonly id: FieldRef<"TradingPair", 'String'>
    readonly combinedPNL: FieldRef<"TradingPair", 'String'>
    readonly createdAt: FieldRef<"TradingPair", 'DateTime'>
    readonly pairKey: FieldRef<"TradingPair", 'Json'>
    readonly status: FieldRef<"TradingPair", 'String'>
    readonly updatedAt: FieldRef<"TradingPair", 'DateTime'>
    readonly userId: FieldRef<"TradingPair", 'String'>
  }
    

  // Custom InputTypes
  /**
   * TradingPair findUnique
   */
  export type TradingPairFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter, which TradingPair to fetch.
     */
    where: TradingPairWhereUniqueInput
  }

  /**
   * TradingPair findUniqueOrThrow
   */
  export type TradingPairFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter, which TradingPair to fetch.
     */
    where: TradingPairWhereUniqueInput
  }

  /**
   * TradingPair findFirst
   */
  export type TradingPairFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter, which TradingPair to fetch.
     */
    where?: TradingPairWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TradingPairs to fetch.
     */
    orderBy?: TradingPairOrderByWithRelationInput | TradingPairOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TradingPairs.
     */
    cursor?: TradingPairWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TradingPairs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TradingPairs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TradingPairs.
     */
    distinct?: TradingPairScalarFieldEnum | TradingPairScalarFieldEnum[]
  }

  /**
   * TradingPair findFirstOrThrow
   */
  export type TradingPairFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter, which TradingPair to fetch.
     */
    where?: TradingPairWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TradingPairs to fetch.
     */
    orderBy?: TradingPairOrderByWithRelationInput | TradingPairOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TradingPairs.
     */
    cursor?: TradingPairWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TradingPairs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TradingPairs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TradingPairs.
     */
    distinct?: TradingPairScalarFieldEnum | TradingPairScalarFieldEnum[]
  }

  /**
   * TradingPair findMany
   */
  export type TradingPairFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter, which TradingPairs to fetch.
     */
    where?: TradingPairWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TradingPairs to fetch.
     */
    orderBy?: TradingPairOrderByWithRelationInput | TradingPairOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TradingPairs.
     */
    cursor?: TradingPairWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TradingPairs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TradingPairs.
     */
    skip?: number
    distinct?: TradingPairScalarFieldEnum | TradingPairScalarFieldEnum[]
  }

  /**
   * TradingPair create
   */
  export type TradingPairCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * The data needed to create a TradingPair.
     */
    data: XOR<TradingPairCreateInput, TradingPairUncheckedCreateInput>
  }

  /**
   * TradingPair createMany
   */
  export type TradingPairCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TradingPairs.
     */
    data: TradingPairCreateManyInput | TradingPairCreateManyInput[]
  }

  /**
   * TradingPair update
   */
  export type TradingPairUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * The data needed to update a TradingPair.
     */
    data: XOR<TradingPairUpdateInput, TradingPairUncheckedUpdateInput>
    /**
     * Choose, which TradingPair to update.
     */
    where: TradingPairWhereUniqueInput
  }

  /**
   * TradingPair updateMany
   */
  export type TradingPairUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TradingPairs.
     */
    data: XOR<TradingPairUpdateManyMutationInput, TradingPairUncheckedUpdateManyInput>
    /**
     * Filter which TradingPairs to update
     */
    where?: TradingPairWhereInput
    /**
     * Limit how many TradingPairs to update.
     */
    limit?: number
  }

  /**
   * TradingPair upsert
   */
  export type TradingPairUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * The filter to search for the TradingPair to update in case it exists.
     */
    where: TradingPairWhereUniqueInput
    /**
     * In case the TradingPair found by the `where` argument doesn't exist, create a new TradingPair with this data.
     */
    create: XOR<TradingPairCreateInput, TradingPairUncheckedCreateInput>
    /**
     * In case the TradingPair was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TradingPairUpdateInput, TradingPairUncheckedUpdateInput>
  }

  /**
   * TradingPair delete
   */
  export type TradingPairDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
    /**
     * Filter which TradingPair to delete.
     */
    where: TradingPairWhereUniqueInput
  }

  /**
   * TradingPair deleteMany
   */
  export type TradingPairDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TradingPairs to delete
     */
    where?: TradingPairWhereInput
    /**
     * Limit how many TradingPairs to delete.
     */
    limit?: number
  }

  /**
   * TradingPair findRaw
   */
  export type TradingPairFindRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The query predicate filter. If unspecified, then all documents in the collection will match the predicate. ${@link https://docs.mongodb.com/manual/reference/operator/query MongoDB Docs}.
     */
    filter?: InputJsonValue
    /**
     * Additional options to pass to the `find` command ${@link https://docs.mongodb.com/manual/reference/command/find/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * TradingPair aggregateRaw
   */
  export type TradingPairAggregateRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * An array of aggregation stages to process and transform the document stream via the aggregation pipeline. ${@link https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline MongoDB Docs}.
     */
    pipeline?: InputJsonValue[]
    /**
     * Additional options to pass to the `aggregate` command ${@link https://docs.mongodb.com/manual/reference/command/aggregate/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * TradingPair without action
   */
  export type TradingPairDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TradingPair
     */
    select?: TradingPairSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TradingPair
     */
    omit?: TradingPairOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TradingPairInclude<ExtArgs> | null
  }


  /**
   * Model users
   */

  export type AggregateUsers = {
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  export type UsersMinAggregateOutputType = {
    id: string | null
    createdAt: Date | null
    email: string | null
    name: string | null
    password: string | null
    role: string | null
  }

  export type UsersMaxAggregateOutputType = {
    id: string | null
    createdAt: Date | null
    email: string | null
    name: string | null
    password: string | null
    role: string | null
  }

  export type UsersCountAggregateOutputType = {
    id: number
    createdAt: number
    email: number
    name: number
    password: number
    role: number
    _all: number
  }


  export type UsersMinAggregateInputType = {
    id?: true
    createdAt?: true
    email?: true
    name?: true
    password?: true
    role?: true
  }

  export type UsersMaxAggregateInputType = {
    id?: true
    createdAt?: true
    email?: true
    name?: true
    password?: true
    role?: true
  }

  export type UsersCountAggregateInputType = {
    id?: true
    createdAt?: true
    email?: true
    name?: true
    password?: true
    role?: true
    _all?: true
  }

  export type UsersAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which users to aggregate.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned users
    **/
    _count?: true | UsersCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UsersMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UsersMaxAggregateInputType
  }

  export type GetUsersAggregateType<T extends UsersAggregateArgs> = {
        [P in keyof T & keyof AggregateUsers]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUsers[P]>
      : GetScalarType<T[P], AggregateUsers[P]>
  }




  export type usersGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: usersWhereInput
    orderBy?: usersOrderByWithAggregationInput | usersOrderByWithAggregationInput[]
    by: UsersScalarFieldEnum[] | UsersScalarFieldEnum
    having?: usersScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UsersCountAggregateInputType | true
    _min?: UsersMinAggregateInputType
    _max?: UsersMaxAggregateInputType
  }

  export type UsersGroupByOutputType = {
    id: string
    createdAt: Date
    email: string
    name: string
    password: string
    role: string | null
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  type GetUsersGroupByPayload<T extends usersGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UsersGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UsersGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UsersGroupByOutputType[P]>
            : GetScalarType<T[P], UsersGroupByOutputType[P]>
        }
      >
    >


  export type usersSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    createdAt?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    role?: boolean
    schwabToken?: boolean | users$schwabTokenArgs<ExtArgs>
    userSymbols?: boolean | users$userSymbolsArgs<ExtArgs>
  }, ExtArgs["result"]["users"]>



  export type usersSelectScalar = {
    id?: boolean
    createdAt?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    role?: boolean
  }

  export type usersOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "createdAt" | "email" | "name" | "password" | "role", ExtArgs["result"]["users"]>
  export type usersInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    schwabToken?: boolean | users$schwabTokenArgs<ExtArgs>
    userSymbols?: boolean | users$userSymbolsArgs<ExtArgs>
  }

  export type $usersPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "users"
    objects: {
      schwabToken: Prisma.$SchwabTokenPayload<ExtArgs> | null
      userSymbols: Prisma.$UserSymbolsPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      createdAt: Date
      email: string
      name: string
      password: string
      role: string | null
    }, ExtArgs["result"]["users"]>
    composites: {}
  }

  type usersGetPayload<S extends boolean | null | undefined | usersDefaultArgs> = $Result.GetResult<Prisma.$usersPayload, S>

  type usersCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<usersFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UsersCountAggregateInputType | true
    }

  export interface usersDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['users'], meta: { name: 'users' } }
    /**
     * Find zero or one Users that matches the filter.
     * @param {usersFindUniqueArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends usersFindUniqueArgs>(args: SelectSubset<T, usersFindUniqueArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Users that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {usersFindUniqueOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends usersFindUniqueOrThrowArgs>(args: SelectSubset<T, usersFindUniqueOrThrowArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindFirstArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends usersFindFirstArgs>(args?: SelectSubset<T, usersFindFirstArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindFirstOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends usersFindFirstOrThrowArgs>(args?: SelectSubset<T, usersFindFirstOrThrowArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.users.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.users.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const usersWithIdOnly = await prisma.users.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends usersFindManyArgs>(args?: SelectSubset<T, usersFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Users.
     * @param {usersCreateArgs} args - Arguments to create a Users.
     * @example
     * // Create one Users
     * const Users = await prisma.users.create({
     *   data: {
     *     // ... data to create a Users
     *   }
     * })
     * 
     */
    create<T extends usersCreateArgs>(args: SelectSubset<T, usersCreateArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {usersCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const users = await prisma.users.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends usersCreateManyArgs>(args?: SelectSubset<T, usersCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a Users.
     * @param {usersDeleteArgs} args - Arguments to delete one Users.
     * @example
     * // Delete one Users
     * const Users = await prisma.users.delete({
     *   where: {
     *     // ... filter to delete one Users
     *   }
     * })
     * 
     */
    delete<T extends usersDeleteArgs>(args: SelectSubset<T, usersDeleteArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Users.
     * @param {usersUpdateArgs} args - Arguments to update one Users.
     * @example
     * // Update one Users
     * const users = await prisma.users.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends usersUpdateArgs>(args: SelectSubset<T, usersUpdateArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {usersDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.users.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends usersDeleteManyArgs>(args?: SelectSubset<T, usersDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const users = await prisma.users.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends usersUpdateManyArgs>(args: SelectSubset<T, usersUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Users.
     * @param {usersUpsertArgs} args - Arguments to update or create a Users.
     * @example
     * // Update or create a Users
     * const users = await prisma.users.upsert({
     *   create: {
     *     // ... data to create a Users
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Users we want to update
     *   }
     * })
     */
    upsert<T extends usersUpsertArgs>(args: SelectSubset<T, usersUpsertArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * @param {usersFindRawArgs} args - Select which filters you would like to apply.
     * @example
     * const users = await prisma.users.findRaw({
     *   filter: { age: { $gt: 25 } }
     * })
     */
    findRaw(args?: usersFindRawArgs): Prisma.PrismaPromise<JsonObject>

    /**
     * Perform aggregation operations on a Users.
     * @param {usersAggregateRawArgs} args - Select which aggregations you would like to apply.
     * @example
     * const users = await prisma.users.aggregateRaw({
     *   pipeline: [
     *     { $match: { status: "registered" } },
     *     { $group: { _id: "$country", total: { $sum: 1 } } }
     *   ]
     * })
     */
    aggregateRaw(args?: usersAggregateRawArgs): Prisma.PrismaPromise<JsonObject>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.users.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends usersCountArgs>(
      args?: Subset<T, usersCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UsersCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UsersAggregateArgs>(args: Subset<T, UsersAggregateArgs>): Prisma.PrismaPromise<GetUsersAggregateType<T>>

    /**
     * Group by Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends usersGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: usersGroupByArgs['orderBy'] }
        : { orderBy?: usersGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, usersGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsersGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the users model
   */
  readonly fields: usersFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for users.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__usersClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    schwabToken<T extends users$schwabTokenArgs<ExtArgs> = {}>(args?: Subset<T, users$schwabTokenArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    userSymbols<T extends users$userSymbolsArgs<ExtArgs> = {}>(args?: Subset<T, users$userSymbolsArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the users model
   */
  interface usersFieldRefs {
    readonly id: FieldRef<"users", 'String'>
    readonly createdAt: FieldRef<"users", 'DateTime'>
    readonly email: FieldRef<"users", 'String'>
    readonly name: FieldRef<"users", 'String'>
    readonly password: FieldRef<"users", 'String'>
    readonly role: FieldRef<"users", 'String'>
  }
    

  // Custom InputTypes
  /**
   * users findUnique
   */
  export type usersFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users findUniqueOrThrow
   */
  export type usersFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users findFirst
   */
  export type usersFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users findFirstOrThrow
   */
  export type usersFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users findMany
   */
  export type usersFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users create
   */
  export type usersCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The data needed to create a users.
     */
    data: XOR<usersCreateInput, usersUncheckedCreateInput>
  }

  /**
   * users createMany
   */
  export type usersCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many users.
     */
    data: usersCreateManyInput | usersCreateManyInput[]
  }

  /**
   * users update
   */
  export type usersUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The data needed to update a users.
     */
    data: XOR<usersUpdateInput, usersUncheckedUpdateInput>
    /**
     * Choose, which users to update.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users updateMany
   */
  export type usersUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update users.
     */
    data: XOR<usersUpdateManyMutationInput, usersUncheckedUpdateManyInput>
    /**
     * Filter which users to update
     */
    where?: usersWhereInput
    /**
     * Limit how many users to update.
     */
    limit?: number
  }

  /**
   * users upsert
   */
  export type usersUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The filter to search for the users to update in case it exists.
     */
    where: usersWhereUniqueInput
    /**
     * In case the users found by the `where` argument doesn't exist, create a new users with this data.
     */
    create: XOR<usersCreateInput, usersUncheckedCreateInput>
    /**
     * In case the users was found with the provided `where` argument, update it with this data.
     */
    update: XOR<usersUpdateInput, usersUncheckedUpdateInput>
  }

  /**
   * users delete
   */
  export type usersDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter which users to delete.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users deleteMany
   */
  export type usersDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which users to delete
     */
    where?: usersWhereInput
    /**
     * Limit how many users to delete.
     */
    limit?: number
  }

  /**
   * users findRaw
   */
  export type usersFindRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The query predicate filter. If unspecified, then all documents in the collection will match the predicate. ${@link https://docs.mongodb.com/manual/reference/operator/query MongoDB Docs}.
     */
    filter?: InputJsonValue
    /**
     * Additional options to pass to the `find` command ${@link https://docs.mongodb.com/manual/reference/command/find/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * users aggregateRaw
   */
  export type usersAggregateRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * An array of aggregation stages to process and transform the document stream via the aggregation pipeline. ${@link https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline MongoDB Docs}.
     */
    pipeline?: InputJsonValue[]
    /**
     * Additional options to pass to the `aggregate` command ${@link https://docs.mongodb.com/manual/reference/command/aggregate/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * users.schwabToken
   */
  export type users$schwabTokenArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    where?: SchwabTokenWhereInput
  }

  /**
   * users.userSymbols
   */
  export type users$userSymbolsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    where?: UserSymbolsWhereInput
  }

  /**
   * users without action
   */
  export type usersDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
  }


  /**
   * Model SchwabToken
   */

  export type AggregateSchwabToken = {
    _count: SchwabTokenCountAggregateOutputType | null
    _min: SchwabTokenMinAggregateOutputType | null
    _max: SchwabTokenMaxAggregateOutputType | null
  }

  export type SchwabTokenMinAggregateOutputType = {
    id: string | null
    userId: string | null
    accessToken: string | null
    refreshToken: string | null
    expiresAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type SchwabTokenMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    accessToken: string | null
    refreshToken: string | null
    expiresAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type SchwabTokenCountAggregateOutputType = {
    id: number
    userId: number
    accessToken: number
    refreshToken: number
    expiresAt: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type SchwabTokenMinAggregateInputType = {
    id?: true
    userId?: true
    accessToken?: true
    refreshToken?: true
    expiresAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type SchwabTokenMaxAggregateInputType = {
    id?: true
    userId?: true
    accessToken?: true
    refreshToken?: true
    expiresAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type SchwabTokenCountAggregateInputType = {
    id?: true
    userId?: true
    accessToken?: true
    refreshToken?: true
    expiresAt?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type SchwabTokenAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SchwabToken to aggregate.
     */
    where?: SchwabTokenWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SchwabTokens to fetch.
     */
    orderBy?: SchwabTokenOrderByWithRelationInput | SchwabTokenOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SchwabTokenWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SchwabTokens from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SchwabTokens.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned SchwabTokens
    **/
    _count?: true | SchwabTokenCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SchwabTokenMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SchwabTokenMaxAggregateInputType
  }

  export type GetSchwabTokenAggregateType<T extends SchwabTokenAggregateArgs> = {
        [P in keyof T & keyof AggregateSchwabToken]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSchwabToken[P]>
      : GetScalarType<T[P], AggregateSchwabToken[P]>
  }




  export type SchwabTokenGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SchwabTokenWhereInput
    orderBy?: SchwabTokenOrderByWithAggregationInput | SchwabTokenOrderByWithAggregationInput[]
    by: SchwabTokenScalarFieldEnum[] | SchwabTokenScalarFieldEnum
    having?: SchwabTokenScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SchwabTokenCountAggregateInputType | true
    _min?: SchwabTokenMinAggregateInputType
    _max?: SchwabTokenMaxAggregateInputType
  }

  export type SchwabTokenGroupByOutputType = {
    id: string
    userId: string
    accessToken: string
    refreshToken: string | null
    expiresAt: Date
    createdAt: Date
    updatedAt: Date
    _count: SchwabTokenCountAggregateOutputType | null
    _min: SchwabTokenMinAggregateOutputType | null
    _max: SchwabTokenMaxAggregateOutputType | null
  }

  type GetSchwabTokenGroupByPayload<T extends SchwabTokenGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SchwabTokenGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SchwabTokenGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SchwabTokenGroupByOutputType[P]>
            : GetScalarType<T[P], SchwabTokenGroupByOutputType[P]>
        }
      >
    >


  export type SchwabTokenSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    accessToken?: boolean
    refreshToken?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["schwabToken"]>



  export type SchwabTokenSelectScalar = {
    id?: boolean
    userId?: boolean
    accessToken?: boolean
    refreshToken?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type SchwabTokenOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "accessToken" | "refreshToken" | "expiresAt" | "createdAt" | "updatedAt", ExtArgs["result"]["schwabToken"]>
  export type SchwabTokenInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $SchwabTokenPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "SchwabToken"
    objects: {
      user: Prisma.$usersPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      accessToken: string
      refreshToken: string | null
      expiresAt: Date
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["schwabToken"]>
    composites: {}
  }

  type SchwabTokenGetPayload<S extends boolean | null | undefined | SchwabTokenDefaultArgs> = $Result.GetResult<Prisma.$SchwabTokenPayload, S>

  type SchwabTokenCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SchwabTokenFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SchwabTokenCountAggregateInputType | true
    }

  export interface SchwabTokenDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SchwabToken'], meta: { name: 'SchwabToken' } }
    /**
     * Find zero or one SchwabToken that matches the filter.
     * @param {SchwabTokenFindUniqueArgs} args - Arguments to find a SchwabToken
     * @example
     * // Get one SchwabToken
     * const schwabToken = await prisma.schwabToken.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SchwabTokenFindUniqueArgs>(args: SelectSubset<T, SchwabTokenFindUniqueArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one SchwabToken that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SchwabTokenFindUniqueOrThrowArgs} args - Arguments to find a SchwabToken
     * @example
     * // Get one SchwabToken
     * const schwabToken = await prisma.schwabToken.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SchwabTokenFindUniqueOrThrowArgs>(args: SelectSubset<T, SchwabTokenFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SchwabToken that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenFindFirstArgs} args - Arguments to find a SchwabToken
     * @example
     * // Get one SchwabToken
     * const schwabToken = await prisma.schwabToken.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SchwabTokenFindFirstArgs>(args?: SelectSubset<T, SchwabTokenFindFirstArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SchwabToken that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenFindFirstOrThrowArgs} args - Arguments to find a SchwabToken
     * @example
     * // Get one SchwabToken
     * const schwabToken = await prisma.schwabToken.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SchwabTokenFindFirstOrThrowArgs>(args?: SelectSubset<T, SchwabTokenFindFirstOrThrowArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more SchwabTokens that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all SchwabTokens
     * const schwabTokens = await prisma.schwabToken.findMany()
     * 
     * // Get first 10 SchwabTokens
     * const schwabTokens = await prisma.schwabToken.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const schwabTokenWithIdOnly = await prisma.schwabToken.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SchwabTokenFindManyArgs>(args?: SelectSubset<T, SchwabTokenFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a SchwabToken.
     * @param {SchwabTokenCreateArgs} args - Arguments to create a SchwabToken.
     * @example
     * // Create one SchwabToken
     * const SchwabToken = await prisma.schwabToken.create({
     *   data: {
     *     // ... data to create a SchwabToken
     *   }
     * })
     * 
     */
    create<T extends SchwabTokenCreateArgs>(args: SelectSubset<T, SchwabTokenCreateArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many SchwabTokens.
     * @param {SchwabTokenCreateManyArgs} args - Arguments to create many SchwabTokens.
     * @example
     * // Create many SchwabTokens
     * const schwabToken = await prisma.schwabToken.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SchwabTokenCreateManyArgs>(args?: SelectSubset<T, SchwabTokenCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a SchwabToken.
     * @param {SchwabTokenDeleteArgs} args - Arguments to delete one SchwabToken.
     * @example
     * // Delete one SchwabToken
     * const SchwabToken = await prisma.schwabToken.delete({
     *   where: {
     *     // ... filter to delete one SchwabToken
     *   }
     * })
     * 
     */
    delete<T extends SchwabTokenDeleteArgs>(args: SelectSubset<T, SchwabTokenDeleteArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one SchwabToken.
     * @param {SchwabTokenUpdateArgs} args - Arguments to update one SchwabToken.
     * @example
     * // Update one SchwabToken
     * const schwabToken = await prisma.schwabToken.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SchwabTokenUpdateArgs>(args: SelectSubset<T, SchwabTokenUpdateArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more SchwabTokens.
     * @param {SchwabTokenDeleteManyArgs} args - Arguments to filter SchwabTokens to delete.
     * @example
     * // Delete a few SchwabTokens
     * const { count } = await prisma.schwabToken.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SchwabTokenDeleteManyArgs>(args?: SelectSubset<T, SchwabTokenDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SchwabTokens.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many SchwabTokens
     * const schwabToken = await prisma.schwabToken.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SchwabTokenUpdateManyArgs>(args: SelectSubset<T, SchwabTokenUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one SchwabToken.
     * @param {SchwabTokenUpsertArgs} args - Arguments to update or create a SchwabToken.
     * @example
     * // Update or create a SchwabToken
     * const schwabToken = await prisma.schwabToken.upsert({
     *   create: {
     *     // ... data to create a SchwabToken
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the SchwabToken we want to update
     *   }
     * })
     */
    upsert<T extends SchwabTokenUpsertArgs>(args: SelectSubset<T, SchwabTokenUpsertArgs<ExtArgs>>): Prisma__SchwabTokenClient<$Result.GetResult<Prisma.$SchwabTokenPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more SchwabTokens that matches the filter.
     * @param {SchwabTokenFindRawArgs} args - Select which filters you would like to apply.
     * @example
     * const schwabToken = await prisma.schwabToken.findRaw({
     *   filter: { age: { $gt: 25 } }
     * })
     */
    findRaw(args?: SchwabTokenFindRawArgs): Prisma.PrismaPromise<JsonObject>

    /**
     * Perform aggregation operations on a SchwabToken.
     * @param {SchwabTokenAggregateRawArgs} args - Select which aggregations you would like to apply.
     * @example
     * const schwabToken = await prisma.schwabToken.aggregateRaw({
     *   pipeline: [
     *     { $match: { status: "registered" } },
     *     { $group: { _id: "$country", total: { $sum: 1 } } }
     *   ]
     * })
     */
    aggregateRaw(args?: SchwabTokenAggregateRawArgs): Prisma.PrismaPromise<JsonObject>


    /**
     * Count the number of SchwabTokens.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenCountArgs} args - Arguments to filter SchwabTokens to count.
     * @example
     * // Count the number of SchwabTokens
     * const count = await prisma.schwabToken.count({
     *   where: {
     *     // ... the filter for the SchwabTokens we want to count
     *   }
     * })
    **/
    count<T extends SchwabTokenCountArgs>(
      args?: Subset<T, SchwabTokenCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SchwabTokenCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a SchwabToken.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SchwabTokenAggregateArgs>(args: Subset<T, SchwabTokenAggregateArgs>): Prisma.PrismaPromise<GetSchwabTokenAggregateType<T>>

    /**
     * Group by SchwabToken.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SchwabTokenGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SchwabTokenGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SchwabTokenGroupByArgs['orderBy'] }
        : { orderBy?: SchwabTokenGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SchwabTokenGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSchwabTokenGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SchwabToken model
   */
  readonly fields: SchwabTokenFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for SchwabToken.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SchwabTokenClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the SchwabToken model
   */
  interface SchwabTokenFieldRefs {
    readonly id: FieldRef<"SchwabToken", 'String'>
    readonly userId: FieldRef<"SchwabToken", 'String'>
    readonly accessToken: FieldRef<"SchwabToken", 'String'>
    readonly refreshToken: FieldRef<"SchwabToken", 'String'>
    readonly expiresAt: FieldRef<"SchwabToken", 'DateTime'>
    readonly createdAt: FieldRef<"SchwabToken", 'DateTime'>
    readonly updatedAt: FieldRef<"SchwabToken", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * SchwabToken findUnique
   */
  export type SchwabTokenFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter, which SchwabToken to fetch.
     */
    where: SchwabTokenWhereUniqueInput
  }

  /**
   * SchwabToken findUniqueOrThrow
   */
  export type SchwabTokenFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter, which SchwabToken to fetch.
     */
    where: SchwabTokenWhereUniqueInput
  }

  /**
   * SchwabToken findFirst
   */
  export type SchwabTokenFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter, which SchwabToken to fetch.
     */
    where?: SchwabTokenWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SchwabTokens to fetch.
     */
    orderBy?: SchwabTokenOrderByWithRelationInput | SchwabTokenOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SchwabTokens.
     */
    cursor?: SchwabTokenWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SchwabTokens from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SchwabTokens.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SchwabTokens.
     */
    distinct?: SchwabTokenScalarFieldEnum | SchwabTokenScalarFieldEnum[]
  }

  /**
   * SchwabToken findFirstOrThrow
   */
  export type SchwabTokenFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter, which SchwabToken to fetch.
     */
    where?: SchwabTokenWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SchwabTokens to fetch.
     */
    orderBy?: SchwabTokenOrderByWithRelationInput | SchwabTokenOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SchwabTokens.
     */
    cursor?: SchwabTokenWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SchwabTokens from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SchwabTokens.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SchwabTokens.
     */
    distinct?: SchwabTokenScalarFieldEnum | SchwabTokenScalarFieldEnum[]
  }

  /**
   * SchwabToken findMany
   */
  export type SchwabTokenFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter, which SchwabTokens to fetch.
     */
    where?: SchwabTokenWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SchwabTokens to fetch.
     */
    orderBy?: SchwabTokenOrderByWithRelationInput | SchwabTokenOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing SchwabTokens.
     */
    cursor?: SchwabTokenWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SchwabTokens from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SchwabTokens.
     */
    skip?: number
    distinct?: SchwabTokenScalarFieldEnum | SchwabTokenScalarFieldEnum[]
  }

  /**
   * SchwabToken create
   */
  export type SchwabTokenCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * The data needed to create a SchwabToken.
     */
    data: XOR<SchwabTokenCreateInput, SchwabTokenUncheckedCreateInput>
  }

  /**
   * SchwabToken createMany
   */
  export type SchwabTokenCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many SchwabTokens.
     */
    data: SchwabTokenCreateManyInput | SchwabTokenCreateManyInput[]
  }

  /**
   * SchwabToken update
   */
  export type SchwabTokenUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * The data needed to update a SchwabToken.
     */
    data: XOR<SchwabTokenUpdateInput, SchwabTokenUncheckedUpdateInput>
    /**
     * Choose, which SchwabToken to update.
     */
    where: SchwabTokenWhereUniqueInput
  }

  /**
   * SchwabToken updateMany
   */
  export type SchwabTokenUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update SchwabTokens.
     */
    data: XOR<SchwabTokenUpdateManyMutationInput, SchwabTokenUncheckedUpdateManyInput>
    /**
     * Filter which SchwabTokens to update
     */
    where?: SchwabTokenWhereInput
    /**
     * Limit how many SchwabTokens to update.
     */
    limit?: number
  }

  /**
   * SchwabToken upsert
   */
  export type SchwabTokenUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * The filter to search for the SchwabToken to update in case it exists.
     */
    where: SchwabTokenWhereUniqueInput
    /**
     * In case the SchwabToken found by the `where` argument doesn't exist, create a new SchwabToken with this data.
     */
    create: XOR<SchwabTokenCreateInput, SchwabTokenUncheckedCreateInput>
    /**
     * In case the SchwabToken was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SchwabTokenUpdateInput, SchwabTokenUncheckedUpdateInput>
  }

  /**
   * SchwabToken delete
   */
  export type SchwabTokenDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
    /**
     * Filter which SchwabToken to delete.
     */
    where: SchwabTokenWhereUniqueInput
  }

  /**
   * SchwabToken deleteMany
   */
  export type SchwabTokenDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SchwabTokens to delete
     */
    where?: SchwabTokenWhereInput
    /**
     * Limit how many SchwabTokens to delete.
     */
    limit?: number
  }

  /**
   * SchwabToken findRaw
   */
  export type SchwabTokenFindRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The query predicate filter. If unspecified, then all documents in the collection will match the predicate. ${@link https://docs.mongodb.com/manual/reference/operator/query MongoDB Docs}.
     */
    filter?: InputJsonValue
    /**
     * Additional options to pass to the `find` command ${@link https://docs.mongodb.com/manual/reference/command/find/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * SchwabToken aggregateRaw
   */
  export type SchwabTokenAggregateRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * An array of aggregation stages to process and transform the document stream via the aggregation pipeline. ${@link https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline MongoDB Docs}.
     */
    pipeline?: InputJsonValue[]
    /**
     * Additional options to pass to the `aggregate` command ${@link https://docs.mongodb.com/manual/reference/command/aggregate/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * SchwabToken without action
   */
  export type SchwabTokenDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SchwabToken
     */
    select?: SchwabTokenSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SchwabToken
     */
    omit?: SchwabTokenOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SchwabTokenInclude<ExtArgs> | null
  }


  /**
   * Model UserSymbols
   */

  export type AggregateUserSymbols = {
    _count: UserSymbolsCountAggregateOutputType | null
    _min: UserSymbolsMinAggregateOutputType | null
    _max: UserSymbolsMaxAggregateOutputType | null
  }

  export type UserSymbolsMinAggregateOutputType = {
    id: string | null
    userId: string | null
    customerId: string | null
    symbols: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserSymbolsMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    customerId: string | null
    symbols: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserSymbolsCountAggregateOutputType = {
    id: number
    userId: number
    customerId: number
    symbols: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserSymbolsMinAggregateInputType = {
    id?: true
    userId?: true
    customerId?: true
    symbols?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserSymbolsMaxAggregateInputType = {
    id?: true
    userId?: true
    customerId?: true
    symbols?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserSymbolsCountAggregateInputType = {
    id?: true
    userId?: true
    customerId?: true
    symbols?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserSymbolsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserSymbols to aggregate.
     */
    where?: UserSymbolsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserSymbols to fetch.
     */
    orderBy?: UserSymbolsOrderByWithRelationInput | UserSymbolsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserSymbolsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserSymbols from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserSymbols.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned UserSymbols
    **/
    _count?: true | UserSymbolsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserSymbolsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserSymbolsMaxAggregateInputType
  }

  export type GetUserSymbolsAggregateType<T extends UserSymbolsAggregateArgs> = {
        [P in keyof T & keyof AggregateUserSymbols]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUserSymbols[P]>
      : GetScalarType<T[P], AggregateUserSymbols[P]>
  }




  export type UserSymbolsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserSymbolsWhereInput
    orderBy?: UserSymbolsOrderByWithAggregationInput | UserSymbolsOrderByWithAggregationInput[]
    by: UserSymbolsScalarFieldEnum[] | UserSymbolsScalarFieldEnum
    having?: UserSymbolsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserSymbolsCountAggregateInputType | true
    _min?: UserSymbolsMinAggregateInputType
    _max?: UserSymbolsMaxAggregateInputType
  }

  export type UserSymbolsGroupByOutputType = {
    id: string
    userId: string
    customerId: string
    symbols: string
    createdAt: Date
    updatedAt: Date
    _count: UserSymbolsCountAggregateOutputType | null
    _min: UserSymbolsMinAggregateOutputType | null
    _max: UserSymbolsMaxAggregateOutputType | null
  }

  type GetUserSymbolsGroupByPayload<T extends UserSymbolsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserSymbolsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserSymbolsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserSymbolsGroupByOutputType[P]>
            : GetScalarType<T[P], UserSymbolsGroupByOutputType[P]>
        }
      >
    >


  export type UserSymbolsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    customerId?: boolean
    symbols?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userSymbols"]>



  export type UserSymbolsSelectScalar = {
    id?: boolean
    userId?: boolean
    customerId?: boolean
    symbols?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserSymbolsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "customerId" | "symbols" | "createdAt" | "updatedAt", ExtArgs["result"]["userSymbols"]>
  export type UserSymbolsInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $UserSymbolsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "UserSymbols"
    objects: {
      user: Prisma.$usersPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      customerId: string
      symbols: string
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["userSymbols"]>
    composites: {}
  }

  type UserSymbolsGetPayload<S extends boolean | null | undefined | UserSymbolsDefaultArgs> = $Result.GetResult<Prisma.$UserSymbolsPayload, S>

  type UserSymbolsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserSymbolsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserSymbolsCountAggregateInputType | true
    }

  export interface UserSymbolsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserSymbols'], meta: { name: 'UserSymbols' } }
    /**
     * Find zero or one UserSymbols that matches the filter.
     * @param {UserSymbolsFindUniqueArgs} args - Arguments to find a UserSymbols
     * @example
     * // Get one UserSymbols
     * const userSymbols = await prisma.userSymbols.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserSymbolsFindUniqueArgs>(args: SelectSubset<T, UserSymbolsFindUniqueArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one UserSymbols that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserSymbolsFindUniqueOrThrowArgs} args - Arguments to find a UserSymbols
     * @example
     * // Get one UserSymbols
     * const userSymbols = await prisma.userSymbols.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserSymbolsFindUniqueOrThrowArgs>(args: SelectSubset<T, UserSymbolsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserSymbols that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsFindFirstArgs} args - Arguments to find a UserSymbols
     * @example
     * // Get one UserSymbols
     * const userSymbols = await prisma.userSymbols.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserSymbolsFindFirstArgs>(args?: SelectSubset<T, UserSymbolsFindFirstArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserSymbols that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsFindFirstOrThrowArgs} args - Arguments to find a UserSymbols
     * @example
     * // Get one UserSymbols
     * const userSymbols = await prisma.userSymbols.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserSymbolsFindFirstOrThrowArgs>(args?: SelectSubset<T, UserSymbolsFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more UserSymbols that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all UserSymbols
     * const userSymbols = await prisma.userSymbols.findMany()
     * 
     * // Get first 10 UserSymbols
     * const userSymbols = await prisma.userSymbols.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userSymbolsWithIdOnly = await prisma.userSymbols.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserSymbolsFindManyArgs>(args?: SelectSubset<T, UserSymbolsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a UserSymbols.
     * @param {UserSymbolsCreateArgs} args - Arguments to create a UserSymbols.
     * @example
     * // Create one UserSymbols
     * const UserSymbols = await prisma.userSymbols.create({
     *   data: {
     *     // ... data to create a UserSymbols
     *   }
     * })
     * 
     */
    create<T extends UserSymbolsCreateArgs>(args: SelectSubset<T, UserSymbolsCreateArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many UserSymbols.
     * @param {UserSymbolsCreateManyArgs} args - Arguments to create many UserSymbols.
     * @example
     * // Create many UserSymbols
     * const userSymbols = await prisma.userSymbols.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserSymbolsCreateManyArgs>(args?: SelectSubset<T, UserSymbolsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a UserSymbols.
     * @param {UserSymbolsDeleteArgs} args - Arguments to delete one UserSymbols.
     * @example
     * // Delete one UserSymbols
     * const UserSymbols = await prisma.userSymbols.delete({
     *   where: {
     *     // ... filter to delete one UserSymbols
     *   }
     * })
     * 
     */
    delete<T extends UserSymbolsDeleteArgs>(args: SelectSubset<T, UserSymbolsDeleteArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one UserSymbols.
     * @param {UserSymbolsUpdateArgs} args - Arguments to update one UserSymbols.
     * @example
     * // Update one UserSymbols
     * const userSymbols = await prisma.userSymbols.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserSymbolsUpdateArgs>(args: SelectSubset<T, UserSymbolsUpdateArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more UserSymbols.
     * @param {UserSymbolsDeleteManyArgs} args - Arguments to filter UserSymbols to delete.
     * @example
     * // Delete a few UserSymbols
     * const { count } = await prisma.userSymbols.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserSymbolsDeleteManyArgs>(args?: SelectSubset<T, UserSymbolsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserSymbols.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many UserSymbols
     * const userSymbols = await prisma.userSymbols.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserSymbolsUpdateManyArgs>(args: SelectSubset<T, UserSymbolsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one UserSymbols.
     * @param {UserSymbolsUpsertArgs} args - Arguments to update or create a UserSymbols.
     * @example
     * // Update or create a UserSymbols
     * const userSymbols = await prisma.userSymbols.upsert({
     *   create: {
     *     // ... data to create a UserSymbols
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the UserSymbols we want to update
     *   }
     * })
     */
    upsert<T extends UserSymbolsUpsertArgs>(args: SelectSubset<T, UserSymbolsUpsertArgs<ExtArgs>>): Prisma__UserSymbolsClient<$Result.GetResult<Prisma.$UserSymbolsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more UserSymbols that matches the filter.
     * @param {UserSymbolsFindRawArgs} args - Select which filters you would like to apply.
     * @example
     * const userSymbols = await prisma.userSymbols.findRaw({
     *   filter: { age: { $gt: 25 } }
     * })
     */
    findRaw(args?: UserSymbolsFindRawArgs): Prisma.PrismaPromise<JsonObject>

    /**
     * Perform aggregation operations on a UserSymbols.
     * @param {UserSymbolsAggregateRawArgs} args - Select which aggregations you would like to apply.
     * @example
     * const userSymbols = await prisma.userSymbols.aggregateRaw({
     *   pipeline: [
     *     { $match: { status: "registered" } },
     *     { $group: { _id: "$country", total: { $sum: 1 } } }
     *   ]
     * })
     */
    aggregateRaw(args?: UserSymbolsAggregateRawArgs): Prisma.PrismaPromise<JsonObject>


    /**
     * Count the number of UserSymbols.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsCountArgs} args - Arguments to filter UserSymbols to count.
     * @example
     * // Count the number of UserSymbols
     * const count = await prisma.userSymbols.count({
     *   where: {
     *     // ... the filter for the UserSymbols we want to count
     *   }
     * })
    **/
    count<T extends UserSymbolsCountArgs>(
      args?: Subset<T, UserSymbolsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserSymbolsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a UserSymbols.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserSymbolsAggregateArgs>(args: Subset<T, UserSymbolsAggregateArgs>): Prisma.PrismaPromise<GetUserSymbolsAggregateType<T>>

    /**
     * Group by UserSymbols.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserSymbolsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserSymbolsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserSymbolsGroupByArgs['orderBy'] }
        : { orderBy?: UserSymbolsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserSymbolsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserSymbolsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the UserSymbols model
   */
  readonly fields: UserSymbolsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for UserSymbols.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserSymbolsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the UserSymbols model
   */
  interface UserSymbolsFieldRefs {
    readonly id: FieldRef<"UserSymbols", 'String'>
    readonly userId: FieldRef<"UserSymbols", 'String'>
    readonly customerId: FieldRef<"UserSymbols", 'String'>
    readonly symbols: FieldRef<"UserSymbols", 'String'>
    readonly createdAt: FieldRef<"UserSymbols", 'DateTime'>
    readonly updatedAt: FieldRef<"UserSymbols", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * UserSymbols findUnique
   */
  export type UserSymbolsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter, which UserSymbols to fetch.
     */
    where: UserSymbolsWhereUniqueInput
  }

  /**
   * UserSymbols findUniqueOrThrow
   */
  export type UserSymbolsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter, which UserSymbols to fetch.
     */
    where: UserSymbolsWhereUniqueInput
  }

  /**
   * UserSymbols findFirst
   */
  export type UserSymbolsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter, which UserSymbols to fetch.
     */
    where?: UserSymbolsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserSymbols to fetch.
     */
    orderBy?: UserSymbolsOrderByWithRelationInput | UserSymbolsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserSymbols.
     */
    cursor?: UserSymbolsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserSymbols from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserSymbols.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserSymbols.
     */
    distinct?: UserSymbolsScalarFieldEnum | UserSymbolsScalarFieldEnum[]
  }

  /**
   * UserSymbols findFirstOrThrow
   */
  export type UserSymbolsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter, which UserSymbols to fetch.
     */
    where?: UserSymbolsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserSymbols to fetch.
     */
    orderBy?: UserSymbolsOrderByWithRelationInput | UserSymbolsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserSymbols.
     */
    cursor?: UserSymbolsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserSymbols from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserSymbols.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserSymbols.
     */
    distinct?: UserSymbolsScalarFieldEnum | UserSymbolsScalarFieldEnum[]
  }

  /**
   * UserSymbols findMany
   */
  export type UserSymbolsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter, which UserSymbols to fetch.
     */
    where?: UserSymbolsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserSymbols to fetch.
     */
    orderBy?: UserSymbolsOrderByWithRelationInput | UserSymbolsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing UserSymbols.
     */
    cursor?: UserSymbolsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserSymbols from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserSymbols.
     */
    skip?: number
    distinct?: UserSymbolsScalarFieldEnum | UserSymbolsScalarFieldEnum[]
  }

  /**
   * UserSymbols create
   */
  export type UserSymbolsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * The data needed to create a UserSymbols.
     */
    data: XOR<UserSymbolsCreateInput, UserSymbolsUncheckedCreateInput>
  }

  /**
   * UserSymbols createMany
   */
  export type UserSymbolsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many UserSymbols.
     */
    data: UserSymbolsCreateManyInput | UserSymbolsCreateManyInput[]
  }

  /**
   * UserSymbols update
   */
  export type UserSymbolsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * The data needed to update a UserSymbols.
     */
    data: XOR<UserSymbolsUpdateInput, UserSymbolsUncheckedUpdateInput>
    /**
     * Choose, which UserSymbols to update.
     */
    where: UserSymbolsWhereUniqueInput
  }

  /**
   * UserSymbols updateMany
   */
  export type UserSymbolsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update UserSymbols.
     */
    data: XOR<UserSymbolsUpdateManyMutationInput, UserSymbolsUncheckedUpdateManyInput>
    /**
     * Filter which UserSymbols to update
     */
    where?: UserSymbolsWhereInput
    /**
     * Limit how many UserSymbols to update.
     */
    limit?: number
  }

  /**
   * UserSymbols upsert
   */
  export type UserSymbolsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * The filter to search for the UserSymbols to update in case it exists.
     */
    where: UserSymbolsWhereUniqueInput
    /**
     * In case the UserSymbols found by the `where` argument doesn't exist, create a new UserSymbols with this data.
     */
    create: XOR<UserSymbolsCreateInput, UserSymbolsUncheckedCreateInput>
    /**
     * In case the UserSymbols was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserSymbolsUpdateInput, UserSymbolsUncheckedUpdateInput>
  }

  /**
   * UserSymbols delete
   */
  export type UserSymbolsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
    /**
     * Filter which UserSymbols to delete.
     */
    where: UserSymbolsWhereUniqueInput
  }

  /**
   * UserSymbols deleteMany
   */
  export type UserSymbolsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserSymbols to delete
     */
    where?: UserSymbolsWhereInput
    /**
     * Limit how many UserSymbols to delete.
     */
    limit?: number
  }

  /**
   * UserSymbols findRaw
   */
  export type UserSymbolsFindRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The query predicate filter. If unspecified, then all documents in the collection will match the predicate. ${@link https://docs.mongodb.com/manual/reference/operator/query MongoDB Docs}.
     */
    filter?: InputJsonValue
    /**
     * Additional options to pass to the `find` command ${@link https://docs.mongodb.com/manual/reference/command/find/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * UserSymbols aggregateRaw
   */
  export type UserSymbolsAggregateRawArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * An array of aggregation stages to process and transform the document stream via the aggregation pipeline. ${@link https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline MongoDB Docs}.
     */
    pipeline?: InputJsonValue[]
    /**
     * Additional options to pass to the `aggregate` command ${@link https://docs.mongodb.com/manual/reference/command/aggregate/#command-fields MongoDB Docs}.
     */
    options?: InputJsonValue
  }

  /**
   * UserSymbols without action
   */
  export type UserSymbolsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserSymbols
     */
    select?: UserSymbolsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserSymbols
     */
    omit?: UserSymbolsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserSymbolsInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const ExcelDataScalarFieldEnum: {
    id: 'id',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    userId: 'userId'
  };

  export type ExcelDataScalarFieldEnum = (typeof ExcelDataScalarFieldEnum)[keyof typeof ExcelDataScalarFieldEnum]


  export const TradingPairScalarFieldEnum: {
    id: 'id',
    combinedPNL: 'combinedPNL',
    createdAt: 'createdAt',
    pairKey: 'pairKey',
    status: 'status',
    updatedAt: 'updatedAt',
    userId: 'userId'
  };

  export type TradingPairScalarFieldEnum = (typeof TradingPairScalarFieldEnum)[keyof typeof TradingPairScalarFieldEnum]


  export const UsersScalarFieldEnum: {
    id: 'id',
    createdAt: 'createdAt',
    email: 'email',
    name: 'name',
    password: 'password',
    role: 'role'
  };

  export type UsersScalarFieldEnum = (typeof UsersScalarFieldEnum)[keyof typeof UsersScalarFieldEnum]


  export const SchwabTokenScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    accessToken: 'accessToken',
    refreshToken: 'refreshToken',
    expiresAt: 'expiresAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type SchwabTokenScalarFieldEnum = (typeof SchwabTokenScalarFieldEnum)[keyof typeof SchwabTokenScalarFieldEnum]


  export const UserSymbolsScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    customerId: 'customerId',
    symbols: 'symbols',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserSymbolsScalarFieldEnum = (typeof UserSymbolsScalarFieldEnum)[keyof typeof UserSymbolsScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type ExcelDataWhereInput = {
    AND?: ExcelDataWhereInput | ExcelDataWhereInput[]
    OR?: ExcelDataWhereInput[]
    NOT?: ExcelDataWhereInput | ExcelDataWhereInput[]
    id?: StringFilter<"ExcelData"> | string
    createdAt?: DateTimeFilter<"ExcelData"> | Date | string
    longLoadedTableData?: ExcelDataLongLoadedTableDataCompositeListFilter | ExcelDataLongLoadedTableDataObjectEqualityInput[]
    longOpenTableData?: ExcelDataLongOpenTableDataCompositeListFilter | ExcelDataLongOpenTableDataObjectEqualityInput[]
    shortLoadedTableData?: ExcelDataShortLoadedTableDataCompositeListFilter | ExcelDataShortLoadedTableDataObjectEqualityInput[]
    shortOpenTableData?: ExcelDataShortOpenTableDataCompositeListFilter | ExcelDataShortOpenTableDataObjectEqualityInput[]
    shortClosedTableData?: ExcelDataShortClosedTableDataCompositeListFilter | ExcelDataShortClosedTableDataObjectEqualityInput[]
    longClosedTableData?: ExcelDataLongClosedTableDataCompositeListFilter | ExcelDataLongClosedTableDataObjectEqualityInput[]
    updatedAt?: DateTimeFilter<"ExcelData"> | Date | string
    userId?: StringFilter<"ExcelData"> | string
  }

  export type ExcelDataOrderByWithRelationInput = {
    id?: SortOrder
    createdAt?: SortOrder
    longLoadedTableData?: ExcelDataLongLoadedTableDataOrderByCompositeAggregateInput
    longOpenTableData?: ExcelDataLongOpenTableDataOrderByCompositeAggregateInput
    shortLoadedTableData?: ExcelDataShortLoadedTableDataOrderByCompositeAggregateInput
    shortOpenTableData?: ExcelDataShortOpenTableDataOrderByCompositeAggregateInput
    shortClosedTableData?: ExcelDataShortClosedTableDataOrderByCompositeAggregateInput
    longClosedTableData?: ExcelDataLongClosedTableDataOrderByCompositeAggregateInput
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type ExcelDataWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: ExcelDataWhereInput | ExcelDataWhereInput[]
    OR?: ExcelDataWhereInput[]
    NOT?: ExcelDataWhereInput | ExcelDataWhereInput[]
    createdAt?: DateTimeFilter<"ExcelData"> | Date | string
    longLoadedTableData?: ExcelDataLongLoadedTableDataCompositeListFilter | ExcelDataLongLoadedTableDataObjectEqualityInput[]
    longOpenTableData?: ExcelDataLongOpenTableDataCompositeListFilter | ExcelDataLongOpenTableDataObjectEqualityInput[]
    shortLoadedTableData?: ExcelDataShortLoadedTableDataCompositeListFilter | ExcelDataShortLoadedTableDataObjectEqualityInput[]
    shortOpenTableData?: ExcelDataShortOpenTableDataCompositeListFilter | ExcelDataShortOpenTableDataObjectEqualityInput[]
    shortClosedTableData?: ExcelDataShortClosedTableDataCompositeListFilter | ExcelDataShortClosedTableDataObjectEqualityInput[]
    longClosedTableData?: ExcelDataLongClosedTableDataCompositeListFilter | ExcelDataLongClosedTableDataObjectEqualityInput[]
    updatedAt?: DateTimeFilter<"ExcelData"> | Date | string
    userId?: StringFilter<"ExcelData"> | string
  }, "id">

  export type ExcelDataOrderByWithAggregationInput = {
    id?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    _count?: ExcelDataCountOrderByAggregateInput
    _max?: ExcelDataMaxOrderByAggregateInput
    _min?: ExcelDataMinOrderByAggregateInput
  }

  export type ExcelDataScalarWhereWithAggregatesInput = {
    AND?: ExcelDataScalarWhereWithAggregatesInput | ExcelDataScalarWhereWithAggregatesInput[]
    OR?: ExcelDataScalarWhereWithAggregatesInput[]
    NOT?: ExcelDataScalarWhereWithAggregatesInput | ExcelDataScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ExcelData"> | string
    createdAt?: DateTimeWithAggregatesFilter<"ExcelData"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"ExcelData"> | Date | string
    userId?: StringWithAggregatesFilter<"ExcelData"> | string
  }

  export type TradingPairWhereInput = {
    AND?: TradingPairWhereInput | TradingPairWhereInput[]
    OR?: TradingPairWhereInput[]
    NOT?: TradingPairWhereInput | TradingPairWhereInput[]
    id?: StringFilter<"TradingPair"> | string
    combinedPNL?: StringFilter<"TradingPair"> | string
    createdAt?: DateTimeFilter<"TradingPair"> | Date | string
    longComponent?: XOR<TradingPairLongComponentCompositeFilter, TradingPairLongComponentObjectEqualityInput>
    pairKey?: JsonNullableFilter<"TradingPair">
    shortComponent?: XOR<TradingPairShortComponentCompositeFilter, TradingPairShortComponentObjectEqualityInput>
    status?: StringFilter<"TradingPair"> | string
    updatedAt?: DateTimeFilter<"TradingPair"> | Date | string
    userId?: StringFilter<"TradingPair"> | string
  }

  export type TradingPairOrderByWithRelationInput = {
    id?: SortOrder
    combinedPNL?: SortOrder
    createdAt?: SortOrder
    longComponent?: TradingPairLongComponentOrderByInput
    pairKey?: SortOrder
    shortComponent?: TradingPairShortComponentOrderByInput
    status?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type TradingPairWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: TradingPairWhereInput | TradingPairWhereInput[]
    OR?: TradingPairWhereInput[]
    NOT?: TradingPairWhereInput | TradingPairWhereInput[]
    combinedPNL?: StringFilter<"TradingPair"> | string
    createdAt?: DateTimeFilter<"TradingPair"> | Date | string
    longComponent?: XOR<TradingPairLongComponentCompositeFilter, TradingPairLongComponentObjectEqualityInput>
    pairKey?: JsonNullableFilter<"TradingPair">
    shortComponent?: XOR<TradingPairShortComponentCompositeFilter, TradingPairShortComponentObjectEqualityInput>
    status?: StringFilter<"TradingPair"> | string
    updatedAt?: DateTimeFilter<"TradingPair"> | Date | string
    userId?: StringFilter<"TradingPair"> | string
  }, "id">

  export type TradingPairOrderByWithAggregationInput = {
    id?: SortOrder
    combinedPNL?: SortOrder
    createdAt?: SortOrder
    pairKey?: SortOrder
    status?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    _count?: TradingPairCountOrderByAggregateInput
    _max?: TradingPairMaxOrderByAggregateInput
    _min?: TradingPairMinOrderByAggregateInput
  }

  export type TradingPairScalarWhereWithAggregatesInput = {
    AND?: TradingPairScalarWhereWithAggregatesInput | TradingPairScalarWhereWithAggregatesInput[]
    OR?: TradingPairScalarWhereWithAggregatesInput[]
    NOT?: TradingPairScalarWhereWithAggregatesInput | TradingPairScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"TradingPair"> | string
    combinedPNL?: StringWithAggregatesFilter<"TradingPair"> | string
    createdAt?: DateTimeWithAggregatesFilter<"TradingPair"> | Date | string
    pairKey?: JsonNullableWithAggregatesFilter<"TradingPair">
    status?: StringWithAggregatesFilter<"TradingPair"> | string
    updatedAt?: DateTimeWithAggregatesFilter<"TradingPair"> | Date | string
    userId?: StringWithAggregatesFilter<"TradingPair"> | string
  }

  export type usersWhereInput = {
    AND?: usersWhereInput | usersWhereInput[]
    OR?: usersWhereInput[]
    NOT?: usersWhereInput | usersWhereInput[]
    id?: StringFilter<"users"> | string
    createdAt?: DateTimeFilter<"users"> | Date | string
    email?: StringFilter<"users"> | string
    name?: StringFilter<"users"> | string
    password?: StringFilter<"users"> | string
    role?: StringNullableFilter<"users"> | string | null
    schwabToken?: XOR<SchwabTokenNullableScalarRelationFilter, SchwabTokenWhereInput> | null
    userSymbols?: XOR<UserSymbolsNullableScalarRelationFilter, UserSymbolsWhereInput> | null
  }

  export type usersOrderByWithRelationInput = {
    id?: SortOrder
    createdAt?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    role?: SortOrder
    schwabToken?: SchwabTokenOrderByWithRelationInput
    userSymbols?: UserSymbolsOrderByWithRelationInput
  }

  export type usersWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: usersWhereInput | usersWhereInput[]
    OR?: usersWhereInput[]
    NOT?: usersWhereInput | usersWhereInput[]
    createdAt?: DateTimeFilter<"users"> | Date | string
    name?: StringFilter<"users"> | string
    password?: StringFilter<"users"> | string
    role?: StringNullableFilter<"users"> | string | null
    schwabToken?: XOR<SchwabTokenNullableScalarRelationFilter, SchwabTokenWhereInput> | null
    userSymbols?: XOR<UserSymbolsNullableScalarRelationFilter, UserSymbolsWhereInput> | null
  }, "id" | "email">

  export type usersOrderByWithAggregationInput = {
    id?: SortOrder
    createdAt?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    role?: SortOrder
    _count?: usersCountOrderByAggregateInput
    _max?: usersMaxOrderByAggregateInput
    _min?: usersMinOrderByAggregateInput
  }

  export type usersScalarWhereWithAggregatesInput = {
    AND?: usersScalarWhereWithAggregatesInput | usersScalarWhereWithAggregatesInput[]
    OR?: usersScalarWhereWithAggregatesInput[]
    NOT?: usersScalarWhereWithAggregatesInput | usersScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"users"> | string
    createdAt?: DateTimeWithAggregatesFilter<"users"> | Date | string
    email?: StringWithAggregatesFilter<"users"> | string
    name?: StringWithAggregatesFilter<"users"> | string
    password?: StringWithAggregatesFilter<"users"> | string
    role?: StringNullableWithAggregatesFilter<"users"> | string | null
  }

  export type SchwabTokenWhereInput = {
    AND?: SchwabTokenWhereInput | SchwabTokenWhereInput[]
    OR?: SchwabTokenWhereInput[]
    NOT?: SchwabTokenWhereInput | SchwabTokenWhereInput[]
    id?: StringFilter<"SchwabToken"> | string
    userId?: StringFilter<"SchwabToken"> | string
    accessToken?: StringFilter<"SchwabToken"> | string
    refreshToken?: StringNullableFilter<"SchwabToken"> | string | null
    expiresAt?: DateTimeFilter<"SchwabToken"> | Date | string
    createdAt?: DateTimeFilter<"SchwabToken"> | Date | string
    updatedAt?: DateTimeFilter<"SchwabToken"> | Date | string
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }

  export type SchwabTokenOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    accessToken?: SortOrder
    refreshToken?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: usersOrderByWithRelationInput
  }

  export type SchwabTokenWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    userId?: string
    AND?: SchwabTokenWhereInput | SchwabTokenWhereInput[]
    OR?: SchwabTokenWhereInput[]
    NOT?: SchwabTokenWhereInput | SchwabTokenWhereInput[]
    accessToken?: StringFilter<"SchwabToken"> | string
    refreshToken?: StringNullableFilter<"SchwabToken"> | string | null
    expiresAt?: DateTimeFilter<"SchwabToken"> | Date | string
    createdAt?: DateTimeFilter<"SchwabToken"> | Date | string
    updatedAt?: DateTimeFilter<"SchwabToken"> | Date | string
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }, "id" | "userId">

  export type SchwabTokenOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    accessToken?: SortOrder
    refreshToken?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: SchwabTokenCountOrderByAggregateInput
    _max?: SchwabTokenMaxOrderByAggregateInput
    _min?: SchwabTokenMinOrderByAggregateInput
  }

  export type SchwabTokenScalarWhereWithAggregatesInput = {
    AND?: SchwabTokenScalarWhereWithAggregatesInput | SchwabTokenScalarWhereWithAggregatesInput[]
    OR?: SchwabTokenScalarWhereWithAggregatesInput[]
    NOT?: SchwabTokenScalarWhereWithAggregatesInput | SchwabTokenScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"SchwabToken"> | string
    userId?: StringWithAggregatesFilter<"SchwabToken"> | string
    accessToken?: StringWithAggregatesFilter<"SchwabToken"> | string
    refreshToken?: StringNullableWithAggregatesFilter<"SchwabToken"> | string | null
    expiresAt?: DateTimeWithAggregatesFilter<"SchwabToken"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"SchwabToken"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"SchwabToken"> | Date | string
  }

  export type UserSymbolsWhereInput = {
    AND?: UserSymbolsWhereInput | UserSymbolsWhereInput[]
    OR?: UserSymbolsWhereInput[]
    NOT?: UserSymbolsWhereInput | UserSymbolsWhereInput[]
    id?: StringFilter<"UserSymbols"> | string
    userId?: StringFilter<"UserSymbols"> | string
    customerId?: StringFilter<"UserSymbols"> | string
    symbols?: StringFilter<"UserSymbols"> | string
    createdAt?: DateTimeFilter<"UserSymbols"> | Date | string
    updatedAt?: DateTimeFilter<"UserSymbols"> | Date | string
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }

  export type UserSymbolsOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
    symbols?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: usersOrderByWithRelationInput
  }

  export type UserSymbolsWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    userId?: string
    customerId?: string
    AND?: UserSymbolsWhereInput | UserSymbolsWhereInput[]
    OR?: UserSymbolsWhereInput[]
    NOT?: UserSymbolsWhereInput | UserSymbolsWhereInput[]
    symbols?: StringFilter<"UserSymbols"> | string
    createdAt?: DateTimeFilter<"UserSymbols"> | Date | string
    updatedAt?: DateTimeFilter<"UserSymbols"> | Date | string
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }, "id" | "userId" | "customerId">

  export type UserSymbolsOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
    symbols?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserSymbolsCountOrderByAggregateInput
    _max?: UserSymbolsMaxOrderByAggregateInput
    _min?: UserSymbolsMinOrderByAggregateInput
  }

  export type UserSymbolsScalarWhereWithAggregatesInput = {
    AND?: UserSymbolsScalarWhereWithAggregatesInput | UserSymbolsScalarWhereWithAggregatesInput[]
    OR?: UserSymbolsScalarWhereWithAggregatesInput[]
    NOT?: UserSymbolsScalarWhereWithAggregatesInput | UserSymbolsScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"UserSymbols"> | string
    userId?: StringWithAggregatesFilter<"UserSymbols"> | string
    customerId?: StringWithAggregatesFilter<"UserSymbols"> | string
    symbols?: StringWithAggregatesFilter<"UserSymbols"> | string
    createdAt?: DateTimeWithAggregatesFilter<"UserSymbols"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"UserSymbols"> | Date | string
  }

  export type ExcelDataCreateInput = {
    id?: string
    createdAt: Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListCreateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListCreateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListCreateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListCreateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListCreateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListCreateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt: Date | string
    userId: string
  }

  export type ExcelDataUncheckedCreateInput = {
    id?: string
    createdAt: Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListCreateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListCreateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListCreateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListCreateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListCreateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListCreateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt: Date | string
    userId: string
  }

  export type ExcelDataUpdateInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListUpdateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListUpdateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListUpdateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListUpdateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListUpdateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListUpdateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataUncheckedUpdateInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListUpdateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListUpdateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListUpdateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListUpdateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListUpdateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListUpdateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataCreateManyInput = {
    id?: string
    createdAt: Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListCreateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListCreateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListCreateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListCreateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListCreateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListCreateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt: Date | string
    userId: string
  }

  export type ExcelDataUpdateManyMutationInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListUpdateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListUpdateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListUpdateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListUpdateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListUpdateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListUpdateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataUncheckedUpdateManyInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longLoadedTableData?: XOR<ExcelDataLongLoadedTableDataListUpdateEnvelopeInput, ExcelDataLongLoadedTableDataCreateInput> | ExcelDataLongLoadedTableDataCreateInput[]
    longOpenTableData?: XOR<ExcelDataLongOpenTableDataListUpdateEnvelopeInput, ExcelDataLongOpenTableDataCreateInput> | ExcelDataLongOpenTableDataCreateInput[]
    shortLoadedTableData?: XOR<ExcelDataShortLoadedTableDataListUpdateEnvelopeInput, ExcelDataShortLoadedTableDataCreateInput> | ExcelDataShortLoadedTableDataCreateInput[]
    shortOpenTableData?: XOR<ExcelDataShortOpenTableDataListUpdateEnvelopeInput, ExcelDataShortOpenTableDataCreateInput> | ExcelDataShortOpenTableDataCreateInput[]
    shortClosedTableData?: XOR<ExcelDataShortClosedTableDataListUpdateEnvelopeInput, ExcelDataShortClosedTableDataCreateInput> | ExcelDataShortClosedTableDataCreateInput[]
    longClosedTableData?: XOR<ExcelDataLongClosedTableDataListUpdateEnvelopeInput, ExcelDataLongClosedTableDataCreateInput> | ExcelDataLongClosedTableDataCreateInput[]
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type TradingPairCreateInput = {
    id?: string
    combinedPNL: string
    createdAt: Date | string
    longComponent: XOR<TradingPairLongComponentCreateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | null
    shortComponent: XOR<TradingPairShortComponentCreateEnvelopeInput, TradingPairShortComponentCreateInput>
    status: string
    updatedAt: Date | string
    userId: string
  }

  export type TradingPairUncheckedCreateInput = {
    id?: string
    combinedPNL: string
    createdAt: Date | string
    longComponent: XOR<TradingPairLongComponentCreateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | null
    shortComponent: XOR<TradingPairShortComponentCreateEnvelopeInput, TradingPairShortComponentCreateInput>
    status: string
    updatedAt: Date | string
    userId: string
  }

  export type TradingPairUpdateInput = {
    combinedPNL?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longComponent?: XOR<TradingPairLongComponentUpdateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | InputJsonValue | null
    shortComponent?: XOR<TradingPairShortComponentUpdateEnvelopeInput, TradingPairShortComponentCreateInput>
    status?: StringFieldUpdateOperationsInput | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type TradingPairUncheckedUpdateInput = {
    combinedPNL?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longComponent?: XOR<TradingPairLongComponentUpdateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | InputJsonValue | null
    shortComponent?: XOR<TradingPairShortComponentUpdateEnvelopeInput, TradingPairShortComponentCreateInput>
    status?: StringFieldUpdateOperationsInput | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type TradingPairCreateManyInput = {
    id?: string
    combinedPNL: string
    createdAt: Date | string
    longComponent: XOR<TradingPairLongComponentCreateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | null
    shortComponent: XOR<TradingPairShortComponentCreateEnvelopeInput, TradingPairShortComponentCreateInput>
    status: string
    updatedAt: Date | string
    userId: string
  }

  export type TradingPairUpdateManyMutationInput = {
    combinedPNL?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longComponent?: XOR<TradingPairLongComponentUpdateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | InputJsonValue | null
    shortComponent?: XOR<TradingPairShortComponentUpdateEnvelopeInput, TradingPairShortComponentCreateInput>
    status?: StringFieldUpdateOperationsInput | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type TradingPairUncheckedUpdateManyInput = {
    combinedPNL?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    longComponent?: XOR<TradingPairLongComponentUpdateEnvelopeInput, TradingPairLongComponentCreateInput>
    pairKey?: InputJsonValue | InputJsonValue | null
    shortComponent?: XOR<TradingPairShortComponentUpdateEnvelopeInput, TradingPairShortComponentCreateInput>
    status?: StringFieldUpdateOperationsInput | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type usersCreateInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    schwabToken?: SchwabTokenCreateNestedOneWithoutUserInput
    userSymbols?: UserSymbolsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    schwabToken?: SchwabTokenUncheckedCreateNestedOneWithoutUserInput
    userSymbols?: UserSymbolsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersUpdateInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    schwabToken?: SchwabTokenUpdateOneWithoutUserNestedInput
    userSymbols?: UserSymbolsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    schwabToken?: SchwabTokenUncheckedUpdateOneWithoutUserNestedInput
    userSymbols?: UserSymbolsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type usersCreateManyInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
  }

  export type usersUpdateManyMutationInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type usersUncheckedUpdateManyInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type SchwabTokenCreateInput = {
    id?: string
    accessToken: string
    refreshToken?: string | null
    expiresAt: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    user: usersCreateNestedOneWithoutSchwabTokenInput
  }

  export type SchwabTokenUncheckedCreateInput = {
    id?: string
    userId: string
    accessToken: string
    refreshToken?: string | null
    expiresAt: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SchwabTokenUpdateInput = {
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: usersUpdateOneRequiredWithoutSchwabTokenNestedInput
  }

  export type SchwabTokenUncheckedUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SchwabTokenCreateManyInput = {
    id?: string
    userId: string
    accessToken: string
    refreshToken?: string | null
    expiresAt: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SchwabTokenUpdateManyMutationInput = {
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SchwabTokenUncheckedUpdateManyInput = {
    userId?: StringFieldUpdateOperationsInput | string
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserSymbolsCreateInput = {
    id?: string
    customerId: string
    symbols: string
    createdAt?: Date | string
    updatedAt?: Date | string
    user: usersCreateNestedOneWithoutUserSymbolsInput
  }

  export type UserSymbolsUncheckedCreateInput = {
    id?: string
    userId: string
    customerId: string
    symbols: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserSymbolsUpdateInput = {
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: usersUpdateOneRequiredWithoutUserSymbolsNestedInput
  }

  export type UserSymbolsUncheckedUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserSymbolsCreateManyInput = {
    id?: string
    userId: string
    customerId: string
    symbols: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserSymbolsUpdateManyMutationInput = {
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserSymbolsUncheckedUpdateManyInput = {
    userId?: StringFieldUpdateOperationsInput | string
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type ExcelDataLongLoadedTableDataCompositeListFilter = {
    equals?: ExcelDataLongLoadedTableDataObjectEqualityInput[]
    every?: ExcelDataLongLoadedTableDataWhereInput
    some?: ExcelDataLongLoadedTableDataWhereInput
    none?: ExcelDataLongLoadedTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataLongLoadedTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataLongOpenTableDataCompositeListFilter = {
    equals?: ExcelDataLongOpenTableDataObjectEqualityInput[]
    every?: ExcelDataLongOpenTableDataWhereInput
    some?: ExcelDataLongOpenTableDataWhereInput
    none?: ExcelDataLongOpenTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataLongOpenTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortLoadedTableDataCompositeListFilter = {
    equals?: ExcelDataShortLoadedTableDataObjectEqualityInput[]
    every?: ExcelDataShortLoadedTableDataWhereInput
    some?: ExcelDataShortLoadedTableDataWhereInput
    none?: ExcelDataShortLoadedTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataShortLoadedTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortOpenTableDataCompositeListFilter = {
    equals?: ExcelDataShortOpenTableDataObjectEqualityInput[]
    every?: ExcelDataShortOpenTableDataWhereInput
    some?: ExcelDataShortOpenTableDataWhereInput
    none?: ExcelDataShortOpenTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataShortOpenTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortClosedTableDataCompositeListFilter = {
    equals?: ExcelDataShortClosedTableDataObjectEqualityInput[]
    every?: ExcelDataShortClosedTableDataWhereInput
    some?: ExcelDataShortClosedTableDataWhereInput
    none?: ExcelDataShortClosedTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataShortClosedTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataLongClosedTableDataCompositeListFilter = {
    equals?: ExcelDataLongClosedTableDataObjectEqualityInput[]
    every?: ExcelDataLongClosedTableDataWhereInput
    some?: ExcelDataLongClosedTableDataWhereInput
    none?: ExcelDataLongClosedTableDataWhereInput
    isEmpty?: boolean
    isSet?: boolean
  }

  export type ExcelDataLongClosedTableDataObjectEqualityInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataLongLoadedTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataLongOpenTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataShortLoadedTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataShortOpenTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataShortClosedTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataLongClosedTableDataOrderByCompositeAggregateInput = {
    _count?: SortOrder
  }

  export type ExcelDataCountOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type ExcelDataMaxOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type ExcelDataMinOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type TradingPairLongComponentCompositeFilter = {
    equals?: TradingPairLongComponentObjectEqualityInput
    is?: TradingPairLongComponentWhereInput
    isNot?: TradingPairLongComponentWhereInput
  }

  export type TradingPairLongComponentObjectEqualityInput = {
    dividendUserValue: number
    dollarCost: number
    expectedQuantity: InputJsonValue
    formattedAmt: string
    formattedAsk: string
    formattedBid: string
    formattedChange: string
    formattedCost: string
    formattedDividend: string
    formattedLast: string
    formattedLoadedVolume: string
    formattedSpreadUser: string
    formattedUserDividend: string
    formattedVolume: string
    id: string
    pnl: number
    sectorValue: string
    spreadUserValue: InputJsonValue
    spreadValue: number
    statusValue: string
    ticker: string
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    isSet?: boolean
  }

  export type TradingPairShortComponentCompositeFilter = {
    equals?: TradingPairShortComponentObjectEqualityInput
    is?: TradingPairShortComponentWhereInput
    isNot?: TradingPairShortComponentWhereInput
  }

  export type TradingPairShortComponentObjectEqualityInput = {
    dividendUserValue: number
    dollarCost: number
    expectedQuantity: string
    formattedAmt: string
    formattedAsk: string
    formattedBid: string
    formattedChange: string
    formattedCost: string
    formattedDividend: string
    formattedLast: string
    formattedLoadedVolume: string
    formattedSpreadUser: string
    formattedUserDividend: string
    formattedVolume: string
    id: string
    pnl: number
    sectorValue: string
    spreadUserValue: string
    spreadValue: InputJsonValue
    statusValue: string
    ticker: string
  }

  export type TradingPairLongComponentOrderByInput = {
    dividendUserValue?: SortOrder
    dollarCost?: SortOrder
    expectedQuantity?: SortOrder
    formattedAmt?: SortOrder
    formattedAsk?: SortOrder
    formattedBid?: SortOrder
    formattedChange?: SortOrder
    formattedCost?: SortOrder
    formattedDividend?: SortOrder
    formattedLast?: SortOrder
    formattedLoadedVolume?: SortOrder
    formattedSpreadUser?: SortOrder
    formattedUserDividend?: SortOrder
    formattedVolume?: SortOrder
    id?: SortOrder
    pnl?: SortOrder
    sectorValue?: SortOrder
    spreadUserValue?: SortOrder
    spreadValue?: SortOrder
    statusValue?: SortOrder
    ticker?: SortOrder
  }

  export type TradingPairShortComponentOrderByInput = {
    dividendUserValue?: SortOrder
    dollarCost?: SortOrder
    expectedQuantity?: SortOrder
    formattedAmt?: SortOrder
    formattedAsk?: SortOrder
    formattedBid?: SortOrder
    formattedChange?: SortOrder
    formattedCost?: SortOrder
    formattedDividend?: SortOrder
    formattedLast?: SortOrder
    formattedLoadedVolume?: SortOrder
    formattedSpreadUser?: SortOrder
    formattedUserDividend?: SortOrder
    formattedVolume?: SortOrder
    id?: SortOrder
    pnl?: SortOrder
    sectorValue?: SortOrder
    spreadUserValue?: SortOrder
    spreadValue?: SortOrder
    statusValue?: SortOrder
    ticker?: SortOrder
  }

  export type TradingPairCountOrderByAggregateInput = {
    id?: SortOrder
    combinedPNL?: SortOrder
    createdAt?: SortOrder
    pairKey?: SortOrder
    status?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type TradingPairMaxOrderByAggregateInput = {
    id?: SortOrder
    combinedPNL?: SortOrder
    createdAt?: SortOrder
    status?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }

  export type TradingPairMinOrderByAggregateInput = {
    id?: SortOrder
    combinedPNL?: SortOrder
    createdAt?: SortOrder
    status?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
    isSet?: boolean
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
    isSet?: boolean
  }

  export type SchwabTokenNullableScalarRelationFilter = {
    is?: SchwabTokenWhereInput | null
    isNot?: SchwabTokenWhereInput | null
  }

  export type UserSymbolsNullableScalarRelationFilter = {
    is?: UserSymbolsWhereInput | null
    isNot?: UserSymbolsWhereInput | null
  }

  export type usersCountOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    role?: SortOrder
  }

  export type usersMaxOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    role?: SortOrder
  }

  export type usersMinOrderByAggregateInput = {
    id?: SortOrder
    createdAt?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    role?: SortOrder
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
    isSet?: boolean
  }

  export type UsersScalarRelationFilter = {
    is?: usersWhereInput
    isNot?: usersWhereInput
  }

  export type SchwabTokenCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    accessToken?: SortOrder
    refreshToken?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type SchwabTokenMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    accessToken?: SortOrder
    refreshToken?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type SchwabTokenMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    accessToken?: SortOrder
    refreshToken?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSymbolsCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
    symbols?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSymbolsMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
    symbols?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSymbolsMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
    symbols?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ExcelDataLongLoadedTableDataListCreateEnvelopeInput = {
    set?: ExcelDataLongLoadedTableDataCreateInput | ExcelDataLongLoadedTableDataCreateInput[]
  }

  export type ExcelDataLongLoadedTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataLongOpenTableDataListCreateEnvelopeInput = {
    set?: ExcelDataLongOpenTableDataCreateInput | ExcelDataLongOpenTableDataCreateInput[]
  }

  export type ExcelDataLongOpenTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortLoadedTableDataListCreateEnvelopeInput = {
    set?: ExcelDataShortLoadedTableDataCreateInput | ExcelDataShortLoadedTableDataCreateInput[]
  }

  export type ExcelDataShortLoadedTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortOpenTableDataListCreateEnvelopeInput = {
    set?: ExcelDataShortOpenTableDataCreateInput | ExcelDataShortOpenTableDataCreateInput[]
  }

  export type ExcelDataShortOpenTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataShortClosedTableDataListCreateEnvelopeInput = {
    set?: ExcelDataShortClosedTableDataCreateInput | ExcelDataShortClosedTableDataCreateInput[]
  }

  export type ExcelDataShortClosedTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type ExcelDataLongClosedTableDataListCreateEnvelopeInput = {
    set?: ExcelDataLongClosedTableDataCreateInput | ExcelDataLongClosedTableDataCreateInput[]
  }

  export type ExcelDataLongClosedTableDataCreateInput = {
    id: string
    sector: string
    shares: string
    spread: string
    status: string
    ticker: string
    volume: string
    dividend: string
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type ExcelDataLongLoadedTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataLongLoadedTableDataCreateInput | ExcelDataLongLoadedTableDataCreateInput[]
    push?: ExcelDataLongLoadedTableDataCreateInput | ExcelDataLongLoadedTableDataCreateInput[]
    updateMany?: ExcelDataLongLoadedTableDataUpdateManyInput
    deleteMany?: ExcelDataLongLoadedTableDataDeleteManyInput
  }

  export type ExcelDataLongOpenTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataLongOpenTableDataCreateInput | ExcelDataLongOpenTableDataCreateInput[]
    push?: ExcelDataLongOpenTableDataCreateInput | ExcelDataLongOpenTableDataCreateInput[]
    updateMany?: ExcelDataLongOpenTableDataUpdateManyInput
    deleteMany?: ExcelDataLongOpenTableDataDeleteManyInput
  }

  export type ExcelDataShortLoadedTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataShortLoadedTableDataCreateInput | ExcelDataShortLoadedTableDataCreateInput[]
    push?: ExcelDataShortLoadedTableDataCreateInput | ExcelDataShortLoadedTableDataCreateInput[]
    updateMany?: ExcelDataShortLoadedTableDataUpdateManyInput
    deleteMany?: ExcelDataShortLoadedTableDataDeleteManyInput
  }

  export type ExcelDataShortOpenTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataShortOpenTableDataCreateInput | ExcelDataShortOpenTableDataCreateInput[]
    push?: ExcelDataShortOpenTableDataCreateInput | ExcelDataShortOpenTableDataCreateInput[]
    updateMany?: ExcelDataShortOpenTableDataUpdateManyInput
    deleteMany?: ExcelDataShortOpenTableDataDeleteManyInput
  }

  export type ExcelDataShortClosedTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataShortClosedTableDataCreateInput | ExcelDataShortClosedTableDataCreateInput[]
    push?: ExcelDataShortClosedTableDataCreateInput | ExcelDataShortClosedTableDataCreateInput[]
    updateMany?: ExcelDataShortClosedTableDataUpdateManyInput
    deleteMany?: ExcelDataShortClosedTableDataDeleteManyInput
  }

  export type ExcelDataLongClosedTableDataListUpdateEnvelopeInput = {
    set?: ExcelDataLongClosedTableDataCreateInput | ExcelDataLongClosedTableDataCreateInput[]
    push?: ExcelDataLongClosedTableDataCreateInput | ExcelDataLongClosedTableDataCreateInput[]
    updateMany?: ExcelDataLongClosedTableDataUpdateManyInput
    deleteMany?: ExcelDataLongClosedTableDataDeleteManyInput
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type TradingPairLongComponentCreateEnvelopeInput = {
    set?: TradingPairLongComponentCreateInput
  }

  export type TradingPairLongComponentCreateInput = {
    dividendUserValue: number
    dollarCost: number
    expectedQuantity: InputJsonValue
    formattedAmt: string
    formattedAsk: string
    formattedBid: string
    formattedChange: string
    formattedCost: string
    formattedDividend: string
    formattedLast: string
    formattedLoadedVolume: string
    formattedSpreadUser: string
    formattedUserDividend: string
    formattedVolume: string
    id: string
    pnl: number
    sectorValue: string
    spreadUserValue: InputJsonValue
    spreadValue: number
    statusValue: string
    ticker: string
  }

  export type TradingPairShortComponentCreateEnvelopeInput = {
    set?: TradingPairShortComponentCreateInput
  }

  export type TradingPairShortComponentCreateInput = {
    dividendUserValue: number
    dollarCost: number
    expectedQuantity: string
    formattedAmt: string
    formattedAsk: string
    formattedBid: string
    formattedChange: string
    formattedCost: string
    formattedDividend: string
    formattedLast: string
    formattedLoadedVolume: string
    formattedSpreadUser: string
    formattedUserDividend: string
    formattedVolume: string
    id: string
    pnl: number
    sectorValue: string
    spreadUserValue: string
    spreadValue: InputJsonValue
    statusValue: string
    ticker: string
  }

  export type TradingPairLongComponentUpdateEnvelopeInput = {
    set?: TradingPairLongComponentCreateInput
    update?: TradingPairLongComponentUpdateInput
  }

  export type TradingPairShortComponentUpdateEnvelopeInput = {
    set?: TradingPairShortComponentCreateInput
    update?: TradingPairShortComponentUpdateInput
  }

  export type SchwabTokenCreateNestedOneWithoutUserInput = {
    create?: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
    connectOrCreate?: SchwabTokenCreateOrConnectWithoutUserInput
    connect?: SchwabTokenWhereUniqueInput
  }

  export type UserSymbolsCreateNestedOneWithoutUserInput = {
    create?: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
    connectOrCreate?: UserSymbolsCreateOrConnectWithoutUserInput
    connect?: UserSymbolsWhereUniqueInput
  }

  export type SchwabTokenUncheckedCreateNestedOneWithoutUserInput = {
    create?: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
    connectOrCreate?: SchwabTokenCreateOrConnectWithoutUserInput
    connect?: SchwabTokenWhereUniqueInput
  }

  export type UserSymbolsUncheckedCreateNestedOneWithoutUserInput = {
    create?: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
    connectOrCreate?: UserSymbolsCreateOrConnectWithoutUserInput
    connect?: UserSymbolsWhereUniqueInput
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
    unset?: boolean
  }

  export type SchwabTokenUpdateOneWithoutUserNestedInput = {
    create?: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
    connectOrCreate?: SchwabTokenCreateOrConnectWithoutUserInput
    upsert?: SchwabTokenUpsertWithoutUserInput
    disconnect?: SchwabTokenWhereInput | boolean
    delete?: SchwabTokenWhereInput | boolean
    connect?: SchwabTokenWhereUniqueInput
    update?: XOR<XOR<SchwabTokenUpdateToOneWithWhereWithoutUserInput, SchwabTokenUpdateWithoutUserInput>, SchwabTokenUncheckedUpdateWithoutUserInput>
  }

  export type UserSymbolsUpdateOneWithoutUserNestedInput = {
    create?: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
    connectOrCreate?: UserSymbolsCreateOrConnectWithoutUserInput
    upsert?: UserSymbolsUpsertWithoutUserInput
    disconnect?: UserSymbolsWhereInput | boolean
    delete?: UserSymbolsWhereInput | boolean
    connect?: UserSymbolsWhereUniqueInput
    update?: XOR<XOR<UserSymbolsUpdateToOneWithWhereWithoutUserInput, UserSymbolsUpdateWithoutUserInput>, UserSymbolsUncheckedUpdateWithoutUserInput>
  }

  export type SchwabTokenUncheckedUpdateOneWithoutUserNestedInput = {
    create?: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
    connectOrCreate?: SchwabTokenCreateOrConnectWithoutUserInput
    upsert?: SchwabTokenUpsertWithoutUserInput
    disconnect?: SchwabTokenWhereInput | boolean
    delete?: SchwabTokenWhereInput | boolean
    connect?: SchwabTokenWhereUniqueInput
    update?: XOR<XOR<SchwabTokenUpdateToOneWithWhereWithoutUserInput, SchwabTokenUpdateWithoutUserInput>, SchwabTokenUncheckedUpdateWithoutUserInput>
  }

  export type UserSymbolsUncheckedUpdateOneWithoutUserNestedInput = {
    create?: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
    connectOrCreate?: UserSymbolsCreateOrConnectWithoutUserInput
    upsert?: UserSymbolsUpsertWithoutUserInput
    disconnect?: UserSymbolsWhereInput | boolean
    delete?: UserSymbolsWhereInput | boolean
    connect?: UserSymbolsWhereUniqueInput
    update?: XOR<XOR<UserSymbolsUpdateToOneWithWhereWithoutUserInput, UserSymbolsUpdateWithoutUserInput>, UserSymbolsUncheckedUpdateWithoutUserInput>
  }

  export type usersCreateNestedOneWithoutSchwabTokenInput = {
    create?: XOR<usersCreateWithoutSchwabTokenInput, usersUncheckedCreateWithoutSchwabTokenInput>
    connectOrCreate?: usersCreateOrConnectWithoutSchwabTokenInput
    connect?: usersWhereUniqueInput
  }

  export type usersUpdateOneRequiredWithoutSchwabTokenNestedInput = {
    create?: XOR<usersCreateWithoutSchwabTokenInput, usersUncheckedCreateWithoutSchwabTokenInput>
    connectOrCreate?: usersCreateOrConnectWithoutSchwabTokenInput
    upsert?: usersUpsertWithoutSchwabTokenInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutSchwabTokenInput, usersUpdateWithoutSchwabTokenInput>, usersUncheckedUpdateWithoutSchwabTokenInput>
  }

  export type usersCreateNestedOneWithoutUserSymbolsInput = {
    create?: XOR<usersCreateWithoutUserSymbolsInput, usersUncheckedCreateWithoutUserSymbolsInput>
    connectOrCreate?: usersCreateOrConnectWithoutUserSymbolsInput
    connect?: usersWhereUniqueInput
  }

  export type usersUpdateOneRequiredWithoutUserSymbolsNestedInput = {
    create?: XOR<usersCreateWithoutUserSymbolsInput, usersUncheckedCreateWithoutUserSymbolsInput>
    connectOrCreate?: usersCreateOrConnectWithoutUserSymbolsInput
    upsert?: usersUpsertWithoutUserSymbolsInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutUserSymbolsInput, usersUpdateWithoutUserSymbolsInput>, usersUncheckedUpdateWithoutUserSymbolsInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type ExcelDataLongLoadedTableDataWhereInput = {
    AND?: ExcelDataLongLoadedTableDataWhereInput | ExcelDataLongLoadedTableDataWhereInput[]
    OR?: ExcelDataLongLoadedTableDataWhereInput[]
    NOT?: ExcelDataLongLoadedTableDataWhereInput | ExcelDataLongLoadedTableDataWhereInput[]
    id?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    sector?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    shares?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    spread?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    status?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    ticker?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    volume?: StringFilter<"ExcelDataLongLoadedTableData"> | string
    dividend?: StringFilter<"ExcelDataLongLoadedTableData"> | string
  }

  export type ExcelDataLongOpenTableDataWhereInput = {
    AND?: ExcelDataLongOpenTableDataWhereInput | ExcelDataLongOpenTableDataWhereInput[]
    OR?: ExcelDataLongOpenTableDataWhereInput[]
    NOT?: ExcelDataLongOpenTableDataWhereInput | ExcelDataLongOpenTableDataWhereInput[]
    id?: StringFilter<"ExcelDataLongOpenTableData"> | string
    sector?: StringFilter<"ExcelDataLongOpenTableData"> | string
    shares?: StringFilter<"ExcelDataLongOpenTableData"> | string
    spread?: StringFilter<"ExcelDataLongOpenTableData"> | string
    status?: StringFilter<"ExcelDataLongOpenTableData"> | string
    ticker?: StringFilter<"ExcelDataLongOpenTableData"> | string
    volume?: StringFilter<"ExcelDataLongOpenTableData"> | string
    dividend?: StringFilter<"ExcelDataLongOpenTableData"> | string
  }

  export type ExcelDataShortLoadedTableDataWhereInput = {
    AND?: ExcelDataShortLoadedTableDataWhereInput | ExcelDataShortLoadedTableDataWhereInput[]
    OR?: ExcelDataShortLoadedTableDataWhereInput[]
    NOT?: ExcelDataShortLoadedTableDataWhereInput | ExcelDataShortLoadedTableDataWhereInput[]
    id?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    sector?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    shares?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    spread?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    status?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    ticker?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    volume?: StringFilter<"ExcelDataShortLoadedTableData"> | string
    dividend?: StringFilter<"ExcelDataShortLoadedTableData"> | string
  }

  export type ExcelDataShortOpenTableDataWhereInput = {
    AND?: ExcelDataShortOpenTableDataWhereInput | ExcelDataShortOpenTableDataWhereInput[]
    OR?: ExcelDataShortOpenTableDataWhereInput[]
    NOT?: ExcelDataShortOpenTableDataWhereInput | ExcelDataShortOpenTableDataWhereInput[]
    id?: StringFilter<"ExcelDataShortOpenTableData"> | string
    sector?: StringFilter<"ExcelDataShortOpenTableData"> | string
    shares?: StringFilter<"ExcelDataShortOpenTableData"> | string
    spread?: StringFilter<"ExcelDataShortOpenTableData"> | string
    status?: StringFilter<"ExcelDataShortOpenTableData"> | string
    ticker?: StringFilter<"ExcelDataShortOpenTableData"> | string
    volume?: StringFilter<"ExcelDataShortOpenTableData"> | string
    dividend?: StringFilter<"ExcelDataShortOpenTableData"> | string
  }

  export type ExcelDataShortClosedTableDataWhereInput = {
    AND?: ExcelDataShortClosedTableDataWhereInput | ExcelDataShortClosedTableDataWhereInput[]
    OR?: ExcelDataShortClosedTableDataWhereInput[]
    NOT?: ExcelDataShortClosedTableDataWhereInput | ExcelDataShortClosedTableDataWhereInput[]
    id?: StringFilter<"ExcelDataShortClosedTableData"> | string
    sector?: StringFilter<"ExcelDataShortClosedTableData"> | string
    shares?: StringFilter<"ExcelDataShortClosedTableData"> | string
    spread?: StringFilter<"ExcelDataShortClosedTableData"> | string
    status?: StringFilter<"ExcelDataShortClosedTableData"> | string
    ticker?: StringFilter<"ExcelDataShortClosedTableData"> | string
    volume?: StringFilter<"ExcelDataShortClosedTableData"> | string
    dividend?: StringFilter<"ExcelDataShortClosedTableData"> | string
  }

  export type ExcelDataLongClosedTableDataWhereInput = {
    AND?: ExcelDataLongClosedTableDataWhereInput | ExcelDataLongClosedTableDataWhereInput[]
    OR?: ExcelDataLongClosedTableDataWhereInput[]
    NOT?: ExcelDataLongClosedTableDataWhereInput | ExcelDataLongClosedTableDataWhereInput[]
    id?: StringFilter<"ExcelDataLongClosedTableData"> | string
    sector?: StringFilter<"ExcelDataLongClosedTableData"> | string
    shares?: StringFilter<"ExcelDataLongClosedTableData"> | string
    spread?: StringFilter<"ExcelDataLongClosedTableData"> | string
    status?: StringFilter<"ExcelDataLongClosedTableData"> | string
    ticker?: StringFilter<"ExcelDataLongClosedTableData"> | string
    volume?: StringFilter<"ExcelDataLongClosedTableData"> | string
    dividend?: StringFilter<"ExcelDataLongClosedTableData"> | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type TradingPairLongComponentWhereInput = {
    AND?: TradingPairLongComponentWhereInput | TradingPairLongComponentWhereInput[]
    OR?: TradingPairLongComponentWhereInput[]
    NOT?: TradingPairLongComponentWhereInput | TradingPairLongComponentWhereInput[]
    dividendUserValue?: IntFilter<"TradingPairLongComponent"> | number
    dollarCost?: IntFilter<"TradingPairLongComponent"> | number
    expectedQuantity?: JsonFilter<"TradingPairLongComponent">
    formattedAmt?: StringFilter<"TradingPairLongComponent"> | string
    formattedAsk?: StringFilter<"TradingPairLongComponent"> | string
    formattedBid?: StringFilter<"TradingPairLongComponent"> | string
    formattedChange?: StringFilter<"TradingPairLongComponent"> | string
    formattedCost?: StringFilter<"TradingPairLongComponent"> | string
    formattedDividend?: StringFilter<"TradingPairLongComponent"> | string
    formattedLast?: StringFilter<"TradingPairLongComponent"> | string
    formattedLoadedVolume?: StringFilter<"TradingPairLongComponent"> | string
    formattedSpreadUser?: StringFilter<"TradingPairLongComponent"> | string
    formattedUserDividend?: StringFilter<"TradingPairLongComponent"> | string
    formattedVolume?: StringFilter<"TradingPairLongComponent"> | string
    id?: StringFilter<"TradingPairLongComponent"> | string
    pnl?: IntFilter<"TradingPairLongComponent"> | number
    sectorValue?: StringFilter<"TradingPairLongComponent"> | string
    spreadUserValue?: JsonFilter<"TradingPairLongComponent">
    spreadValue?: IntFilter<"TradingPairLongComponent"> | number
    statusValue?: StringFilter<"TradingPairLongComponent"> | string
    ticker?: StringFilter<"TradingPairLongComponent"> | string
  }

  export type TradingPairShortComponentWhereInput = {
    AND?: TradingPairShortComponentWhereInput | TradingPairShortComponentWhereInput[]
    OR?: TradingPairShortComponentWhereInput[]
    NOT?: TradingPairShortComponentWhereInput | TradingPairShortComponentWhereInput[]
    dividendUserValue?: IntFilter<"TradingPairShortComponent"> | number
    dollarCost?: IntFilter<"TradingPairShortComponent"> | number
    expectedQuantity?: StringFilter<"TradingPairShortComponent"> | string
    formattedAmt?: StringFilter<"TradingPairShortComponent"> | string
    formattedAsk?: StringFilter<"TradingPairShortComponent"> | string
    formattedBid?: StringFilter<"TradingPairShortComponent"> | string
    formattedChange?: StringFilter<"TradingPairShortComponent"> | string
    formattedCost?: StringFilter<"TradingPairShortComponent"> | string
    formattedDividend?: StringFilter<"TradingPairShortComponent"> | string
    formattedLast?: StringFilter<"TradingPairShortComponent"> | string
    formattedLoadedVolume?: StringFilter<"TradingPairShortComponent"> | string
    formattedSpreadUser?: StringFilter<"TradingPairShortComponent"> | string
    formattedUserDividend?: StringFilter<"TradingPairShortComponent"> | string
    formattedVolume?: StringFilter<"TradingPairShortComponent"> | string
    id?: StringFilter<"TradingPairShortComponent"> | string
    pnl?: IntFilter<"TradingPairShortComponent"> | number
    sectorValue?: StringFilter<"TradingPairShortComponent"> | string
    spreadUserValue?: StringFilter<"TradingPairShortComponent"> | string
    spreadValue?: JsonFilter<"TradingPairShortComponent">
    statusValue?: StringFilter<"TradingPairShortComponent"> | string
    ticker?: StringFilter<"TradingPairShortComponent"> | string
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
    isSet?: boolean
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    isSet?: boolean
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
    isSet?: boolean
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
    isSet?: boolean
  }

  export type ExcelDataLongLoadedTableDataUpdateManyInput = {
    where: ExcelDataLongLoadedTableDataWhereInput
    data: ExcelDataLongLoadedTableDataUpdateInput
  }

  export type ExcelDataLongLoadedTableDataDeleteManyInput = {
    where: ExcelDataLongLoadedTableDataWhereInput
  }

  export type ExcelDataLongOpenTableDataUpdateManyInput = {
    where: ExcelDataLongOpenTableDataWhereInput
    data: ExcelDataLongOpenTableDataUpdateInput
  }

  export type ExcelDataLongOpenTableDataDeleteManyInput = {
    where: ExcelDataLongOpenTableDataWhereInput
  }

  export type ExcelDataShortLoadedTableDataUpdateManyInput = {
    where: ExcelDataShortLoadedTableDataWhereInput
    data: ExcelDataShortLoadedTableDataUpdateInput
  }

  export type ExcelDataShortLoadedTableDataDeleteManyInput = {
    where: ExcelDataShortLoadedTableDataWhereInput
  }

  export type ExcelDataShortOpenTableDataUpdateManyInput = {
    where: ExcelDataShortOpenTableDataWhereInput
    data: ExcelDataShortOpenTableDataUpdateInput
  }

  export type ExcelDataShortOpenTableDataDeleteManyInput = {
    where: ExcelDataShortOpenTableDataWhereInput
  }

  export type ExcelDataShortClosedTableDataUpdateManyInput = {
    where: ExcelDataShortClosedTableDataWhereInput
    data: ExcelDataShortClosedTableDataUpdateInput
  }

  export type ExcelDataShortClosedTableDataDeleteManyInput = {
    where: ExcelDataShortClosedTableDataWhereInput
  }

  export type ExcelDataLongClosedTableDataUpdateManyInput = {
    where: ExcelDataLongClosedTableDataWhereInput
    data: ExcelDataLongClosedTableDataUpdateInput
  }

  export type ExcelDataLongClosedTableDataDeleteManyInput = {
    where: ExcelDataLongClosedTableDataWhereInput
  }

  export type TradingPairLongComponentUpdateInput = {
    dividendUserValue?: IntFieldUpdateOperationsInput | number
    dollarCost?: IntFieldUpdateOperationsInput | number
    expectedQuantity?: InputJsonValue | InputJsonValue
    formattedAmt?: StringFieldUpdateOperationsInput | string
    formattedAsk?: StringFieldUpdateOperationsInput | string
    formattedBid?: StringFieldUpdateOperationsInput | string
    formattedChange?: StringFieldUpdateOperationsInput | string
    formattedCost?: StringFieldUpdateOperationsInput | string
    formattedDividend?: StringFieldUpdateOperationsInput | string
    formattedLast?: StringFieldUpdateOperationsInput | string
    formattedLoadedVolume?: StringFieldUpdateOperationsInput | string
    formattedSpreadUser?: StringFieldUpdateOperationsInput | string
    formattedUserDividend?: StringFieldUpdateOperationsInput | string
    formattedVolume?: StringFieldUpdateOperationsInput | string
    id?: StringFieldUpdateOperationsInput | string
    pnl?: IntFieldUpdateOperationsInput | number
    sectorValue?: StringFieldUpdateOperationsInput | string
    spreadUserValue?: InputJsonValue | InputJsonValue
    spreadValue?: IntFieldUpdateOperationsInput | number
    statusValue?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
  }

  export type TradingPairShortComponentUpdateInput = {
    dividendUserValue?: IntFieldUpdateOperationsInput | number
    dollarCost?: IntFieldUpdateOperationsInput | number
    expectedQuantity?: StringFieldUpdateOperationsInput | string
    formattedAmt?: StringFieldUpdateOperationsInput | string
    formattedAsk?: StringFieldUpdateOperationsInput | string
    formattedBid?: StringFieldUpdateOperationsInput | string
    formattedChange?: StringFieldUpdateOperationsInput | string
    formattedCost?: StringFieldUpdateOperationsInput | string
    formattedDividend?: StringFieldUpdateOperationsInput | string
    formattedLast?: StringFieldUpdateOperationsInput | string
    formattedLoadedVolume?: StringFieldUpdateOperationsInput | string
    formattedSpreadUser?: StringFieldUpdateOperationsInput | string
    formattedUserDividend?: StringFieldUpdateOperationsInput | string
    formattedVolume?: StringFieldUpdateOperationsInput | string
    id?: StringFieldUpdateOperationsInput | string
    pnl?: IntFieldUpdateOperationsInput | number
    sectorValue?: StringFieldUpdateOperationsInput | string
    spreadUserValue?: StringFieldUpdateOperationsInput | string
    spreadValue?: InputJsonValue | InputJsonValue
    statusValue?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
  }

  export type SchwabTokenCreateWithoutUserInput = {
    id?: string
    accessToken: string
    refreshToken?: string | null
    expiresAt: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SchwabTokenUncheckedCreateWithoutUserInput = {
    id?: string
    accessToken: string
    refreshToken?: string | null
    expiresAt: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SchwabTokenCreateOrConnectWithoutUserInput = {
    where: SchwabTokenWhereUniqueInput
    create: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
  }

  export type UserSymbolsCreateWithoutUserInput = {
    id?: string
    customerId: string
    symbols: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserSymbolsUncheckedCreateWithoutUserInput = {
    id?: string
    customerId: string
    symbols: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserSymbolsCreateOrConnectWithoutUserInput = {
    where: UserSymbolsWhereUniqueInput
    create: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
  }

  export type SchwabTokenUpsertWithoutUserInput = {
    update: XOR<SchwabTokenUpdateWithoutUserInput, SchwabTokenUncheckedUpdateWithoutUserInput>
    create: XOR<SchwabTokenCreateWithoutUserInput, SchwabTokenUncheckedCreateWithoutUserInput>
    where?: SchwabTokenWhereInput
  }

  export type SchwabTokenUpdateToOneWithWhereWithoutUserInput = {
    where?: SchwabTokenWhereInput
    data: XOR<SchwabTokenUpdateWithoutUserInput, SchwabTokenUncheckedUpdateWithoutUserInput>
  }

  export type SchwabTokenUpdateWithoutUserInput = {
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SchwabTokenUncheckedUpdateWithoutUserInput = {
    accessToken?: StringFieldUpdateOperationsInput | string
    refreshToken?: NullableStringFieldUpdateOperationsInput | string | null
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserSymbolsUpsertWithoutUserInput = {
    update: XOR<UserSymbolsUpdateWithoutUserInput, UserSymbolsUncheckedUpdateWithoutUserInput>
    create: XOR<UserSymbolsCreateWithoutUserInput, UserSymbolsUncheckedCreateWithoutUserInput>
    where?: UserSymbolsWhereInput
  }

  export type UserSymbolsUpdateToOneWithWhereWithoutUserInput = {
    where?: UserSymbolsWhereInput
    data: XOR<UserSymbolsUpdateWithoutUserInput, UserSymbolsUncheckedUpdateWithoutUserInput>
  }

  export type UserSymbolsUpdateWithoutUserInput = {
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserSymbolsUncheckedUpdateWithoutUserInput = {
    customerId?: StringFieldUpdateOperationsInput | string
    symbols?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type usersCreateWithoutSchwabTokenInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    userSymbols?: UserSymbolsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateWithoutSchwabTokenInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    userSymbols?: UserSymbolsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersCreateOrConnectWithoutSchwabTokenInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutSchwabTokenInput, usersUncheckedCreateWithoutSchwabTokenInput>
  }

  export type usersUpsertWithoutSchwabTokenInput = {
    update: XOR<usersUpdateWithoutSchwabTokenInput, usersUncheckedUpdateWithoutSchwabTokenInput>
    create: XOR<usersCreateWithoutSchwabTokenInput, usersUncheckedCreateWithoutSchwabTokenInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutSchwabTokenInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutSchwabTokenInput, usersUncheckedUpdateWithoutSchwabTokenInput>
  }

  export type usersUpdateWithoutSchwabTokenInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    userSymbols?: UserSymbolsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutSchwabTokenInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    userSymbols?: UserSymbolsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type usersCreateWithoutUserSymbolsInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    schwabToken?: SchwabTokenCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateWithoutUserSymbolsInput = {
    id?: string
    createdAt: Date | string
    email: string
    name: string
    password: string
    role?: string | null
    schwabToken?: SchwabTokenUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersCreateOrConnectWithoutUserSymbolsInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutUserSymbolsInput, usersUncheckedCreateWithoutUserSymbolsInput>
  }

  export type usersUpsertWithoutUserSymbolsInput = {
    update: XOR<usersUpdateWithoutUserSymbolsInput, usersUncheckedUpdateWithoutUserSymbolsInput>
    create: XOR<usersCreateWithoutUserSymbolsInput, usersUncheckedCreateWithoutUserSymbolsInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutUserSymbolsInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutUserSymbolsInput, usersUncheckedUpdateWithoutUserSymbolsInput>
  }

  export type usersUpdateWithoutUserSymbolsInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    schwabToken?: SchwabTokenUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutUserSymbolsInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    email?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    role?: NullableStringFieldUpdateOperationsInput | string | null
    schwabToken?: SchwabTokenUncheckedUpdateOneWithoutUserNestedInput
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
  }

  export type ExcelDataLongLoadedTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataLongOpenTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataShortLoadedTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataShortOpenTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataShortClosedTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type ExcelDataLongClosedTableDataUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sector?: StringFieldUpdateOperationsInput | string
    shares?: StringFieldUpdateOperationsInput | string
    spread?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    ticker?: StringFieldUpdateOperationsInput | string
    volume?: StringFieldUpdateOperationsInput | string
    dividend?: StringFieldUpdateOperationsInput | string
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}