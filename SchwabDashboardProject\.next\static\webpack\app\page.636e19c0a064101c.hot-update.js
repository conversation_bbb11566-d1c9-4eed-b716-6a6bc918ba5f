"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ProtectedRoute */ \"(app-pages-browser)/./components/ProtectedRoute.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isSchwabCallback, setIsSchwabCallback] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isLoggedInToSchwab, setIsLoggedInToSchwab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            console.log(\"HomePage session:\", session, \"status:\", status);\n        }\n    }[\"HomePage.useEffect\"], [\n        session,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if we're on the callback URL\n            const url = new URL(window.location.href);\n            const code = url.searchParams.get(\"code\");\n            if (code) {\n                setIsSchwabCallback(true);\n            }\n            // Always check Schwab login status on page load\n            checkSchwabLogin();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const checkSchwabLogin = async ()=>{\n        try {\n            // Chiedi lo stato Schwab al backend (cookie httpOnly)\n            const res = await fetch(\"/api/schwab-status\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            const data = await res.json();\n            setIsLoggedInToSchwab(data.loggedIn);\n            if (data.loggedIn) {\n                localStorage.setItem('schwabLoggedIn', 'true');\n            } else {\n                localStorage.removeItem('schwabLoggedIn');\n            }\n        } catch (error) {\n            console.error(\"Error checking Schwab login status:\", error);\n        }\n    };\n    const handleLogInToSchwab = async ()=>{\n        try {\n            console.log(\"Getting Auth URL...\");\n            const url = new URL(window.location.href);\n            let code = url.searchParams.get(\"code\");\n            if (!code) {\n                // Set a flag to indicate we're attempting to log in\n                localStorage.setItem('schwabLoginAttempt', 'true');\n                window.location.href = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeURL)();\n                return;\n            }\n            // If we have a code, we're logged in\n            localStorage.setItem('schwabLoggedIn', 'true');\n            setIsLoggedInToSchwab(true);\n        } catch (error) {\n            console.error(\"Error getting OAuth Token:\", error);\n        }\n    };\n    const handleLogOutFromSchwab = ()=>{\n        try {\n            // Clear Schwab-related cookies\n            document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            // Clear localStorage flags\n            localStorage.removeItem('schwabLoggedIn');\n            localStorage.removeItem('schwabLoginAttempt');\n            // Update state\n            setIsLoggedInToSchwab(false);\n            // Refresh the page to ensure all state is reset\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error logging out from Schwab:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        localStorage.clear();\n        sessionStorage.clear();\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/generalLogin\"\n        });\n    // window.location.href = \"/generalLogin\";\n    };\n    // Simple and clean navigation cards\n    const navigationCards = [\n        {\n            title: \"Account Summary\",\n            description: \"View account balances and portfolio overview\",\n            href: \"/accountsSummary\"\n        },\n        {\n            title: \"WB Dashboard\",\n            description: \"Monitor and manage trading pairs\",\n            href: \"/Strategies/WB/dashboard\"\n        },\n        {\n            title: \"WB Configuration\",\n            description: \"Setup and configure trading strategies\",\n            href: \"/Strategies/WB/configuration\"\n        },\n        {\n            title: \"Saved Pairs\",\n            description: \"Access your saved trading pairs\",\n            href: \"/savedPairs\"\n        },\n        {\n            title: \"File Handler\",\n            description: \"Import and manage data files\",\n            href: \"/fileHandler\"\n        },\n        {\n            title: \"Account Activity\",\n            description: \"Review transaction history and activity\",\n            href: \"/testingAccountActivity\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto px-6 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: isSchwabCallback ? \"Welcome Back\" : \"Trading Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto\",\n                                    children: isSchwabCallback ? \"Successfully connected to Schwab. Your trading platform is ready.\" : \"Manage your investments and trading strategies from one central location.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"Logged in as \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 32\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full mr-2 \".concat(isLoggedInToSchwab ? 'bg-green-500' : 'bg-red-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: isLoggedInToSchwab ? \"Schwab Connected\" : \"Schwab Disconnected\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLogInToSchwab(),\n                                        className: \"px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: \"Connect Schwab\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogOutFromSchwab,\n                                        className: \"px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLogout(),\n                                        className: \"px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 154,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-1 bg-blue-600 dark:bg-blue-500 rounded-full mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                    children: \"Platform Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: navigationCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                    href: card.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white dark:bg-gray-800 border \".concat(card.borderColor, \" rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(card.color, \" \").concat(card.hoverColor, \" p-5 flex items-center justify-center transition-all duration-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white\",\n                                                    children: card.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-5 flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\",\n                                                        children: card.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                                        children: card.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: \"Access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vggbpo6EXw96Kx48ex04Bhn8Jac=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});