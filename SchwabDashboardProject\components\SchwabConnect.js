"use client";

import { useState, useEffect } from "react";
import { getAuthorizationCodeURL, retrieveAccessToken } from "@/actions/schwabAccess";

export default function SchwabConnect() {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Check if the user is connected to Schwab
  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Check if we have an access token
        const accessToken = await retrieveAccessToken();
        setIsConnected(!!accessToken);
      } catch (error) {
        console.error("Error checking Schwab connection:", error);
        setIsConnected(false);
      }
    };

    checkConnection();
  }, []);

  // <PERSON>le connecting to Schwab
  const handleConnect = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the authorization URL
      const authUrl = await getAuthorizationCodeURL();
      
      if (authUrl) {
        // Open the authorization URL in a new window
        window.location.href = authUrl;
      } else {
        setError("Failed to generate authorization URL");
      }
    } catch (error) {
      console.error("Error connecting to Schwab:", error);
      setError("An error occurred while connecting to Schwab");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      {error && (
        <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      <div className="mb-4">
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="font-medium">
            {isConnected ? "Connected to Schwab" : "Not connected to Schwab"}
          </span>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {isConnected 
            ? "Your Schwab account is connected and you can access your trading data." 
            : "Connect your Schwab account to access your trading data."}
        </p>
      </div>

      <button
        onClick={handleConnect}
        disabled={isLoading || isConnected}
        className={`w-full py-2 px-4 rounded font-medium ${
          isConnected
            ? "bg-gray-200 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400"
            : "bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        }`}
      >
        {isLoading ? "Connecting..." : isConnected ? "Connected" : "Connect to Schwab"}
      </button>
    </div>
  );
}
