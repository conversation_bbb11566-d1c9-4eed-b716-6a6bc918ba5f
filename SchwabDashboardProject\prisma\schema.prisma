generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

type ExcelDataLongLoadedTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type ExcelDataLongOpenTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type ExcelDataShortLoadedTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type ExcelDataShortOpenTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type ExcelDataShortClosedTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type ExcelDataLongClosedTableData {
  id     String
  sector String
  shares String
  spread String
  status String
  ticker String
  volume String
  dividend String
}

type TradingPairLongComponent {
  dividendUserValue     Int
  dollarCost            Int
  /// Multiple data types found: String: 71.4%, Int: 28.6% out of 7 sampled entries
  expectedQuantity      Json
  formattedAmt          String
  formattedAsk          String
  formattedBid          String
  formattedChange       String
  formattedCost         String
  formattedDividend     String
  formattedLast         String
  formattedLoadedVolume String
  formattedSpreadUser   String
  formattedUserDividend String
  formattedVolume       String
  id                    String
  pnl                   Int
  sectorValue           String
  /// Multiple data types found: String: 71.4%, Int: 28.6% out of 7 sampled entries
  spreadUserValue       Json
  spreadValue           Int
  statusValue           String
  ticker                String
}

type TradingPairShortComponent {
  dividendUserValue     Int
  dollarCost            Int
  expectedQuantity      String
  formattedAmt          String
  formattedAsk          String
  formattedBid          String
  formattedChange       String
  formattedCost         String
  formattedDividend     String
  formattedLast         String
  formattedLoadedVolume String
  formattedSpreadUser   String
  formattedUserDividend String
  formattedVolume       String
  id                    String
  pnl                   Int
  sectorValue           String
  spreadUserValue       String
  /// Multiple data types found: Float: 14.3%, Int: 85.7% out of 7 sampled entries
  spreadValue           Json
  statusValue           String
  ticker                String
}

model ExcelData {
  id                   String                          @id @default(auto()) @map("_id") @db.ObjectId
  createdAt            DateTime                        @db.Date
  longLoadedTableData  ExcelDataLongLoadedTableData[]
  longOpenTableData    ExcelDataLongOpenTableData[]
  shortLoadedTableData ExcelDataShortLoadedTableData[]
  shortOpenTableData   ExcelDataShortOpenTableData[]
  shortClosedTableData ExcelDataShortClosedTableData[]
  longClosedTableData  ExcelDataLongClosedTableData[]
  updatedAt            DateTime                        @db.Date
  userId               String
}

model TradingPair {
  id             String                    @id @default(auto()) @map("_id") @db.ObjectId
  combinedPNL    String
  createdAt      DateTime                  @db.Date
  longComponent  TradingPairLongComponent
  /// Could not determine type: the field only had null or empty values in the sample set.
  pairKey        Json?
  shortComponent TradingPairShortComponent
  status         String
  updatedAt      DateTime                  @db.Date
  userId         String
}

model users {
  id          String       @id @default(auto()) @map("_id") @db.ObjectId
  createdAt   DateTime     @db.Date
  email       String       @unique
  name        String
  password    String
  role        String?
  schwabToken SchwabToken?
  userSymbols UserSymbols?
}

model SchwabToken {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  userId       String   @unique @db.ObjectId
  accessToken  String
  refreshToken String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserSymbols {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  userId     String   @db.ObjectId
  customerId String   // Schwab customer ID
  symbols    String   // Comma-separated symbols (e.g., "AAPL,GOOG,NVDA")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
  @@unique([customerId])
}
