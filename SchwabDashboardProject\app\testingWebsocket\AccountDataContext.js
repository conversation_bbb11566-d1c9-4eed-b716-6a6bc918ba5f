"use client";
import { createContext, useContext, useEffect, useState } from "react";
import { io } from "socket.io-client";
// Assumendo di avere funzioni simili per recuperare customerId e correlId
import { retrieveCustomerId, retrieveCorrelId } from "@/actions/schwabAccess";

const AccountDataContext = createContext();

export function AccountDataProvider({ children }) {
  /*const [accountData, setAccountData] = useState([]);
  const [accountFilteredData, setAccountFilteredData] = useState({});

  useEffect(() => {
    // Funzione per attivare la sottoscrizione agli account activity
    async function subscribeAccountActivity() {
      try {
        // Assicurati che il customerId e correlId siano salvati prima di usarli
        const customerId = await retrieveCustomerId();
        await storeCorrelId();
        const correlId = await retrieveCorrelId();
        
        const res = await fetch("https://localhost:3001/api/account-activity", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            SchwabClientCustomerId: customerId, 
            SchwabClientCorrelId: correlId 
          }),
          credentials: "include",
        });
        const data = await res.json();
        console.log("Risposta dalla richiesta account activity:", data);
      } catch (err) {
        console.error("Errore nella sottoscrizione degli account:", err);
      }
    }
    subscribeAccountActivity();

    // Connessione Socket.io per ricevere aggiornamenti in tempo reale sugli account
    const socket = io("https://localhost:3001", { transports: ["websocket"] });
    socket.on("connect", () => {
      console.log("Connesso al server Socket.io (accountData)");
    });

    socket.on("accountData", (data) => {
      console.log("Dati account ricevuti via socket:", data);
      let parsed;
      try {
        parsed = JSON.parse(data);
      } catch (err) {
        console.error("Errore nel parsing di accountData:", err);
        return;
      }
      // Aggiungi i nuovi dati alla lista esistente
      setAccountData((prev) => [...prev, parsed]);

      // Esempio di filtraggio: supponiamo che item["1"] sia l'accountId
      if (parsed.data && Array.isArray(parsed.data)) {
        const filtered = {};
        parsed.data.forEach((item) => {
          const accountId = item["1"];
          filtered[accountId] = item;
        });
        setAccountFilteredData((prev) => ({ ...prev, ...filtered }));
      }
    });

    socket.on("disconnect", () => {
      console.log("Socket.io (accountData) disconnesso");
    });

    return () => {
      socket.disconnect();
    };
  }, []);*/

  return (
    <AccountDataContext.Provider value={{ /*accountData, accountFilteredData*/ }}>
      {children}
    </AccountDataContext.Provider>
  );
}

export function useAccountData() {
  return useContext(AccountDataContext);
}
