"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogFooter, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { usePairArray } from "../../../pairArray/PairArray";
import { useExcelData } from "../configuration/page";
import ProtectedRoute from "../../../../components/ProtectedRoute";

function ShortSortableRow({ id, item }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor = changeValue > 0 ? "text-green-500 dark:text-green-400" : changeValue < 0 ? "text-red-500 dark:text-red-400" : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor = amtValue < 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400";

  // HIGHLIGHT: Check if shares should be highlighted (0 shares)
  const sharesValue = item.statusValue === "WB_OpenPositions" ? item.formattedAmt : item.expectedQuantity;
  const shouldHighlight = parseFloat(sharesValue || "0") === 0;
  const highlightClass = shouldHighlight ? "border-2 border-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 animate-pulse" : "";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="border-b dark:border-gray-700 transition-colors hover:bg-muted/50 dark:hover:bg-gray-700/50"
    >
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedCost || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${amtColor} ${highlightClass}`}>{sharesValue || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500 dark:text-yellow-400">{item.ticker || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedBid || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedAsk || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500 dark:text-yellow-400">{item.formattedLast || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${changeColor}`}>{item.formattedChange || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs dark:text-gray-400">{item.formattedVolume || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs dark:text-gray-400">{item.formattedDividend || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs bg-slate-900 dark:bg-slate-800 text-white">{item.exDateValue || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium bg-slate-900 dark:bg-slate-800 text-white">{item.statusValue === "WB_OpenPositions" ? item.formattedSpread : item.formattedSpreadUser || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.dollarCost || "-"}</td>
    </tr>
  );
}

function LongSortableRow({ id, item }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor = changeValue > 0 ? "text-green-500 dark:text-green-400" : changeValue < 0 ? "text-red-500 dark:text-red-400" : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor = amtValue < 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400";

  // Determine color for PNL
  const pnlValue = parseFloat(item.pnl);
  const pnlColor = pnlValue > 0 ? "text-green-500 dark:text-green-400" : pnlValue < 0 ? "text-red-500 dark:text-red-400" : "";

  // HIGHLIGHT: Check if shares should be highlighted (0 shares)
  const sharesValue = item.statusValue === "WB_OpenPositions" ? item.formattedAmt : item.expectedQuantity;
  const shouldHighlight = parseFloat(sharesValue || "0") === 0;
  const highlightClass = shouldHighlight ? "border-2 border-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 animate-pulse" : "";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="border-b dark:border-gray-700 transition-colors hover:bg-muted/50 dark:hover:bg-gray-700/50"
    >
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.statusValue === "WB_OpenPositions" ? item.formattedSpread : item.formattedSpreadUser || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs bg-slate-900 dark:bg-slate-800 text-white">{item.exDateValue || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedCost || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${amtColor} ${highlightClass}`}>{sharesValue || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500 dark:text-yellow-400">{item.ticker || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedBid || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium dark:text-gray-300">{item.formattedAsk || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500 dark:text-yellow-400">{item.formattedLast || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${changeColor}`}>{item.formattedChange || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs dark:text-gray-400">{item.formattedVolume || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs dark:text-gray-400">{item.formattedDividend || "-"}</td>
    </tr>
  );
}
// Edit Data Dialog Component
function EditDataDialog({
  editDialog,
  setEditDialog,
  shortOpenTableData, shortLoadedTableData, shortClosedTableData,
  longOpenTableData, longLoadedTableData, longClosedTableData,
  setShortOpenTableData, setShortLoadedTableData, setShortClosedTableData,
  setLongOpenTableData, setLongLoadedTableData, setLongClosedTableData
}) {
  const [longFormData, setLongFormData] = useState({});
  const [shortFormData, setShortFormData] = useState({});

  // Update form data when dialog opens
  useEffect(() => {
    if (editDialog.open && editDialog.longData && editDialog.shortData) {
      // Filter out status and id fields
      const filterFields = (data) => {
        const filtered = { ...data };
        delete filtered.status;
        delete filtered.id;
        return filtered;
      };

      setLongFormData(filterFields(editDialog.longData));
      setShortFormData(filterFields(editDialog.shortData));
    }
  }, [editDialog.open, editDialog.longData, editDialog.shortData]);

  const handleLongInputChange = (field, value) => {
    setLongFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleShortInputChange = (field, value) => {
    setShortFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    if (!editDialog.open) return;

    const { section, idx } = editDialog;

    // Update both Long and Short tables
    let longTableData, shortTableData;
    let setLongTableData, setShortTableData;

    switch (section) {
      case 'Open':
        longTableData = longOpenTableData;
        shortTableData = shortOpenTableData;
        setLongTableData = setLongOpenTableData;
        setShortTableData = setShortOpenTableData;
        break;
      case 'Loaded':
        longTableData = longLoadedTableData;
        shortTableData = shortLoadedTableData;
        setLongTableData = setLongLoadedTableData;
        setShortTableData = setShortLoadedTableData;
        break;
      case 'Closed':
        longTableData = longClosedTableData;
        shortTableData = shortClosedTableData;
        setLongTableData = setLongClosedTableData;
        setShortTableData = setShortClosedTableData;
        break;
      default:
        return;
    }

    // Update both Long and Short table data
    const updatedLongTableData = [...longTableData];
    const updatedShortTableData = [...shortTableData];

    // Preserve original status and id, update other fields
    updatedLongTableData[idx] = {
      ...editDialog.longData, // Keep original data including status and id
      ...longFormData // Override with form changes
    };
    updatedShortTableData[idx] = {
      ...editDialog.shortData, // Keep original data including status and id
      ...shortFormData // Override with form changes
    };

    setLongTableData(updatedLongTableData);
    setShortTableData(updatedShortTableData);

    // Close dialog
    setEditDialog({ open: false, section: null, idx: null, id: null, longData: null, shortData: null, tableType: null });
  };

  const handleCancel = () => {
    setEditDialog({ open: false, section: null, idx: null, id: null, longData: null, shortData: null, tableType: null });
  };

  if (!editDialog.open) return null;

  return (
    <Dialog open={editDialog.open} onOpenChange={handleCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Pair Data - {editDialog.section}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6 py-4">
          {/* Long Side */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-green-700 dark:text-green-400 border-b pb-2">
              Long Side
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(longFormData).map(([key, value]) => (
                <div key={key} className="space-y-1">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </label>
                  <input
                    type="text"
                    value={value || ''}
                    onChange={(e) => handleLongInputChange(key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Short Side */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 border-b pb-2">
              Short Side
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(shortFormData).map(([key, value]) => (
                <div key={key} className="space-y-1">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </label>
                  <input
                    type="text"
                    value={value || ''}
                    onChange={(e) => handleShortInputChange(key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
          >
            Save Changes
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function DraggablePairTable() {
  // Dialog state for move action
  const [moveDialog, setMoveDialog] = useState({ open: false, section: null, idx: null, id: null });

  // Dialog state for edit action
  const [editDialog, setEditDialog] = useState({
    open: false,
    section: null,
    idx: null,
    id: null,
    longData: null,
    shortData: null,
    tableType: null // Keep for reference
  });
  const { pairArray, setPairArray, pairStatus, setPairStatus, savePairsToDatabase, isSaving, saveStatus } = usePairArray();
  const {
    shortOpenTableData, shortLoadedTableData, longOpenTableData, longLoadedTableData, shortClosedTableData, longClosedTableData, setShortOpenTableData, setShortLoadedTableData, setShortClosedTableData, setLongOpenTableData, setLongLoadedTableData, setLongClosedTableData, updateLongStatus, updateShortStatus, updateShortClosedStatus, updateLongClosedStatus
  } = useExcelData();
  const [loadedShortItems, setLoadedShortItems] = useState([]);
  const [loadedLongItems, setLoadedLongItems] = useState([]);
  const [openShortItems, setOpenShortItems] = useState([]);
  const [openLongItems, setOpenLongItems] = useState([]);
  const [closedShortItems, setClosedShortItems] = useState([]);
  const [closedLongItems, setClosedLongItems] = useState([]);


  // EDITING: Stato per la sezione di editing dati fantoccio
  const [showEditingSection, setShowEditingSection] = useState(false);
  const [editingPairs, setEditingPairs] = useState([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [persistentOverrides, setPersistentOverrides] = useState({}); // Memorizza le modifiche persistenti


  // Funzione per applicare gli override persistenti ai dati
  const applyPersistentOverrides = (pairs) => {
    return pairs.map(pair => {
      const overrides = persistentOverrides[pair.key];
      if (!overrides) return pair;

      return {
        ...pair,
        shortComponent: {
          ...pair.shortComponent,
          ...overrides.shortComponent
        },
        longComponent: {
          ...pair.longComponent,
          ...overrides.longComponent
        }
      };
    });
  };

  const loadedPairs = pairArray ? applyPersistentOverrides(pairArray.filter(pair => pair.status === "WB_LoadedPairs")) : [];
  const openPairs = pairArray ? applyPersistentOverrides(pairArray.filter(pair => pair.status === "WB_OpenPositions")) : [];
  const closedPairs = pairArray ? applyPersistentOverrides(pairArray.filter(pair => pair.status === "WB_ClosedPositions")) : [];



  useEffect(() => {
    const newLoadedShort = loadedPairs.map(pair => ({
      ...pair.shortComponent,
      pairKey: pair.key
    }));
    const newLoadedLong = loadedPairs.map(pair => ({
      ...pair.longComponent,
      pairKey: pair.key
    }));
    if (JSON.stringify(newLoadedShort) !== JSON.stringify(loadedShortItems)) {
      setLoadedShortItems(newLoadedShort);
    }
    if (JSON.stringify(newLoadedLong) !== JSON.stringify(loadedLongItems)) {
      setLoadedLongItems(newLoadedLong);
    }
  }, [loadedPairs]);

  useEffect(() => {
    const newOpenShort = openPairs.map(pair => ({
      ...pair.shortComponent,
      pairKey: pair.key
    }));
    const newOpenLong = openPairs.map(pair => ({
      ...pair.longComponent,
      pairKey: pair.key
    }));
    if (JSON.stringify(newOpenShort) !== JSON.stringify(openShortItems)) {
      setOpenShortItems(newOpenShort);
    }
    if (JSON.stringify(newOpenLong) !== JSON.stringify(openLongItems)) {
      setOpenLongItems(newOpenLong);
    }
  }, [openPairs, openShortItems, openLongItems]);
  useEffect(() => {
    const newClosedShort = closedPairs.map(pair => ({
      ...pair.shortComponent,
      pairKey: pair.key
    }));
    const newClosedLong = closedPairs.map(pair => ({
      ...pair.longComponent,
      pairKey: pair.key
    }));
    if (JSON.stringify(newClosedShort) !== JSON.stringify(closedShortItems)) {
      setClosedShortItems(newClosedShort);
    }
    if (JSON.stringify(newClosedLong) !== JSON.stringify(closedLongItems)) {
      setClosedLongItems(newClosedLong);
    }
  }, [closedPairs, closedShortItems, closedLongItems]);




  const extractShortFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    id: component.id || "",
    status: component.statusValue || "",
    dividend: component.formattedUserDividend || ""
  });

  const extractLongFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    status: component.statusValue || "",
    id: component.id || "",
    dividend: component.formattedUserDividend || ""
  });

  const handleDragEndLoadedShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedShortItems, oldIndex, newIndex);
    setLoadedShortItems(newOrder);
    setShortLoadedTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndLoadedLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedLongItems, oldIndex, newIndex);
    setLoadedLongItems(newOrder);
    setLongLoadedTableData(newOrder.map(extractLongFields));
  };

  const handleDragEndOpenShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openShortItems.findIndex((_, idx) => idx.toString() === active.id);
    const newIndex = openShortItems.findIndex((_, idx) => idx.toString() === over.id);
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openShortItems, oldIndex, newIndex);
    setOpenShortItems(newOrder);
    setShortOpenTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndOpenLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openLongItems.findIndex((_, idx) => idx.toString() === active.id);
    const newIndex = openLongItems.findIndex((_, idx) => idx.toString() === over.id);
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openLongItems, oldIndex, newIndex);
    setOpenLongItems(newOrder);
    setLongOpenTableData(newOrder.map(extractLongFields));
  };

  const handleDragEndClosedShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(closedShortItems, oldIndex, newIndex);
    setClosedShortItems(newOrder);
    setShortClosedTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndClosedLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(closedLongItems, oldIndex, newIndex);
    setClosedLongItems(newOrder);
    setLongClosedTableData(newOrder.map(extractLongFields));
  };

  // Move logic for all directions
  function handleMovePair(section, longId, idx, destination) {
    let longItem, shortItem;
    if (section === 'Loaded') {
      longItem = longLoadedTableData.find(item => item.id === longId);
      shortItem = shortLoadedTableData[idx];
    } else if (section === 'Open') {
      longItem = longOpenTableData.find(item => item.id === longId);
      shortItem = shortOpenTableData[idx];
    } else if (section === 'Closed') {
      longItem = longClosedTableData[idx];
      shortItem = shortClosedTableData[idx];
    }
    if (!longItem || !shortItem) {
      console.error('Pair not found for move');
      return;
    }
    // Determine new status
    let newStatus;
    if (destination === 'Open') newStatus = 'WB_OpenPositions';
    else if (destination === 'Loaded') newStatus = 'WB_LoadedPairs';
    else if (destination === 'Closed') newStatus = 'WB_ClosedPositions';
    updateLongStatus(longItem.id, newStatus);
    updateShortStatus(shortItem.id, newStatus);
    setMoveDialog({ open: false, section: null, idx: null, id: null });
  }
  // Helper to get valid move destinations
  function getMoveDestinations(section) {
    if (section === 'Loaded') return ['Open', 'Closed'];
    if (section === 'Open') return ['Loaded', 'Closed'];
    if (section === 'Closed') return ['Open', 'Loaded'];
    return [];
  }

  // Function to handle edit action
  const handleEditPair = (pairItem, idx, section, tableType) => {
    // Get both Long and Short Excel data for the same pair
    let longExcelData, shortExcelData;

    switch (section) {
      case 'Open':
        longExcelData = longOpenTableData[idx];
        shortExcelData = shortOpenTableData[idx];
        break;
      case 'Loaded':
        longExcelData = longLoadedTableData[idx];
        shortExcelData = shortLoadedTableData[idx];
        break;
      case 'Closed':
        longExcelData = longClosedTableData[idx];
        shortExcelData = shortClosedTableData[idx];
        break;
      default:
        return;
    }

    if (!longExcelData || !shortExcelData) {
      console.error('No Excel data found for index:', idx);
      return;
    }

    setEditDialog({
      open: true,
      section,
      idx,
      id: pairItem.id, // Keep pair ID for reference
      longData: { ...longExcelData }, // Clone the Long Excel data
      shortData: { ...shortExcelData }, // Clone the Short Excel data
      tableType // Keep for reference but now we edit both sides
    });
  };

  function handleDeletePair(e, longId, idx, section = 'loaded') {
    e.stopPropagation();

    // Determina quale tabella usare in base alla sezione
    let longTableData, shortTableData, longItems, shortItems;
    let setLongTableData, setShortTableData, setLongItems, setShortItems;

    switch (section) {
      case 'loaded':
        longTableData = longLoadedTableData;
        shortTableData = shortLoadedTableData;
        longItems = loadedLongItems;
        shortItems = loadedShortItems;
        setLongTableData = setLongLoadedTableData;
        setShortTableData = setShortLoadedTableData;
        setLongItems = setLoadedLongItems;
        setShortItems = setLoadedShortItems;
        break;
      case 'open':
        longTableData = longOpenTableData;
        shortTableData = shortOpenTableData;
        longItems = openLongItems;
        shortItems = openShortItems;
        setLongTableData = setLongOpenTableData;
        setShortTableData = setShortOpenTableData;
        setLongItems = setOpenLongItems;
        setShortItems = setOpenShortItems;
        break;
      case 'closed':
        longTableData = longClosedTableData; 
        shortTableData = shortClosedTableData; 
        longItems = closedLongItems;
        shortItems = closedShortItems;
        setLongTableData = setLongClosedTableData;
        setShortTableData = setShortClosedTableData;
        setLongItems = setClosedLongItems;
        setShortItems = setClosedShortItems;
        break;
      default:
        console.error(`Unknown section: ${section}`);
        return;
    }

    // Get the long item by ID
    const longItem = longTableData.find(item => item.id === longId);
    if (!longItem) {
      console.error(`Long item with ID ${longId} not found in ${section} section`);
      return;
    }

    // Find the corresponding short item by index
    const shortItem = shortTableData[idx];
    if (!shortItem) {
      console.error(`Short item at index ${idx} not found in ${section} section`);
      return;
    }

    console.log(`Deleting pair from ${section}: Long ID=${longId}, Short ID=${shortItem.id}`);

    // Remove items from Excel data
    setShortTableData(prev => prev.filter(item => item.id !== shortItem.id));
    setLongTableData(prev => prev.filter(item => item.id !== longId));

    // Update the UI
    setShortItems(prev => prev.filter((_, i) => i !== idx));
    setLongItems(prev => prev.filter((_, i) => i !== idx));

    // IMPORTANT: Also update the pairArray to ensure database sync
    const pairKey = longItems[idx]?.pairKey;

    console.log(`Before deletion from ${section} - pairArray length:`, pairArray.length);

    if (pairKey) {
      console.log(`Removing pair with key ${pairKey} from pairArray`);
      setPairArray(prev => {
        const newArray = prev.filter(pair => pair.key !== pairKey);
        console.log(`After deletion from ${section} - pairArray length:`, newArray.length);
        return newArray;
      });
    } else {
      // Fallback to filtering by component IDs if pairKey is not available
      console.log(`Removing pair with longId=${longId} and shortId=${shortItem.id} from pairArray`);
      setPairArray(prev => {
        const newArray = prev.filter(pair =>
          !(pair.longComponent.id === longId && pair.shortComponent.id === shortItem.id)
        );
        console.log(`After deletion from ${section} - pairArray length:`, newArray.length);
        return newArray;
      });
    }
  }

  // EDITING: Funzioni per gestire l'editing dei dati
  const initializeEditingData = () => {
    // Usa i dati già con gli override applicati
    const allPairs = [...loadedPairs, ...openPairs];
    const editingData = allPairs.map(pair => ({
      key: pair.key,
      status: pair.status,
      shortComponent: {
        id: pair.shortComponent.id,
        ticker: pair.shortComponent.ticker || "",
        formattedBid: pair.shortComponent.formattedBid || "",
        formattedAsk: pair.shortComponent.formattedAsk || "",
        formattedLast: pair.shortComponent.formattedLast || "",
        formattedChange: pair.shortComponent.formattedChange || "",
        formattedVolume: pair.shortComponent.formattedVolume || "",
        formattedDividend: pair.shortComponent.formattedDividend || "",
        exDateValue: pair.shortComponent.exDateValue || "",
        spreadValue: pair.shortComponent.spreadValue || "",
        formattedSpreadUser: pair.shortComponent.formattedSpreadUser || "",
        formattedCost: pair.shortComponent.formattedCost || "",
        formattedAmt: pair.shortComponent.formattedAmt || "",
        expectedQuantity: pair.shortComponent.expectedQuantity || "",
        dollarCost: pair.shortComponent.dollarCost || ""
      },
      longComponent: {
        id: pair.longComponent.id,
        ticker: pair.longComponent.ticker || "",
        formattedBid: pair.longComponent.formattedBid || "",
        formattedAsk: pair.longComponent.formattedAsk || "",
        formattedLast: pair.longComponent.formattedLast || "",
        formattedChange: pair.longComponent.formattedChange || "",
        formattedVolume: pair.longComponent.formattedVolume || "",
        formattedDividend: pair.longComponent.formattedDividend || "",
        exDateValue: pair.longComponent.exDateValue || "",
        spreadValue: pair.longComponent.spreadValue || "",
        formattedSpreadUser: pair.longComponent.formattedSpreadUser || "",
        formattedCost: pair.longComponent.formattedCost || "",
        formattedAmt: pair.longComponent.formattedAmt || "",
        expectedQuantity: pair.longComponent.expectedQuantity || "",
        dollarCost: pair.longComponent.dollarCost || ""
      }
    }));
    setEditingPairs(editingData);
    setShowEditingSection(true);
    setHasUnsavedChanges(false);
  };

  const updateEditingValue = (pairIndex, component, field, value) => {
    setEditingPairs(prev => {
      const newPairs = [...prev];
      newPairs[pairIndex][component][field] = value;
      return newPairs;
    });
    setHasUnsavedChanges(true);
  };

  const applyEditingChanges = () => {
    // Salva le modifiche negli override persistenti
    const newOverrides = { ...persistentOverrides };

    editingPairs.forEach(editingPair => {
      const originalPair = pairArray.find(p => p.key === editingPair.key);
      if (originalPair) {
        // Calcola solo i campi che sono stati effettivamente modificati
        const shortOverrides = {};
        const longOverrides = {};

        Object.keys(editingPair.shortComponent).forEach(key => {
          if (editingPair.shortComponent[key] !== (originalPair.shortComponent[key] || "")) {
            shortOverrides[key] = editingPair.shortComponent[key];
          }
        });

        Object.keys(editingPair.longComponent).forEach(key => {
          if (editingPair.longComponent[key] !== (originalPair.longComponent[key] || "")) {
            longOverrides[key] = editingPair.longComponent[key];
          }
        });

        if (Object.keys(shortOverrides).length > 0 || Object.keys(longOverrides).length > 0) {
          newOverrides[editingPair.key] = {
            shortComponent: { ...newOverrides[editingPair.key]?.shortComponent, ...shortOverrides },
            longComponent: { ...newOverrides[editingPair.key]?.longComponent, ...longOverrides }
          };
        }
      }
    });

    setPersistentOverrides(newOverrides);
    setHasUnsavedChanges(false);
    alert("Changes applied successfully! They will persist across drag & drop operations.");
  };

  const cancelEditing = () => {
    setShowEditingSection(false);
    setEditingPairs([]);
    setHasUnsavedChanges(false);
  };

  const resetAllOverrides = () => {
    if (confirm("Are you sure you want to reset all custom data modifications? This will restore original values.")) {
      setPersistentOverrides({});
      alert("All custom modifications have been reset!");
    }
  };

  // AUTO-CLOSE: Funzione per controllare e spostare pair con 0 shares (esclude pair fake)
  const checkAndMoveZeroSharesPairs = () => {
    if (!pairArray || pairArray.length === 0) return;

    const pairsToMove = [];
    const updatedPairArray = pairArray.map(pair => {
      if (pair.status === "WB_OpenPositions") {
        const longShares = parseFloat(pair.longComponent?.formattedAmt || "0");
        const shortShares = parseFloat(pair.shortComponent?.formattedAmt || "0");

        // Controlla se entrambi i ticker sono "FAKE"
        const longTicker = pair.longComponent?.ticker || "";
        const shortTicker = pair.shortComponent?.ticker || "";
        const isFakePair = longTicker.toUpperCase() === "FAKE" && shortTicker.toUpperCase() === "FAKE";

        // Se entrambi i componenti hanno 0 shares E non è una pair finta, sposta a closed
        if (longShares === 0 && shortShares === 0 && !isFakePair) {
          pairsToMove.push(pair.key);
          return {
            ...pair,
            status: "WB_ClosedPositions",
            longComponent: {
              ...pair.longComponent,
              statusValue: "WB_ClosedPositions"
            },
            shortComponent: {
              ...pair.shortComponent,
              statusValue: "WB_ClosedPositions"
            }
          };
        }
      }
      return pair;
    });

    if (pairsToMove.length > 0) {
      setPairArray(updatedPairArray);
      console.log(`Auto-moved ${pairsToMove.length} pair(s) to Closed Positions:`, pairsToMove);

      // Aggiorna anche gli status nei dati Excel per mantenere la sincronizzazione
      pairsToMove.forEach(pairKey => {
        const pair = updatedPairArray.find(p => p.key === pairKey);
        if (pair) {
          // Aggiorna lo status nei dati Excel usando le nuove funzioni per closed
          updateLongClosedStatus(pair.longComponent.id, "WB_ClosedPositions");
          updateShortClosedStatus(pair.shortComponent.id, "WB_ClosedPositions");
        }
      });
    }
  };

  // AUTO-CLOSE: useEffect per monitorare i cambiamenti negli openPairs
  useEffect(() => {
    checkAndMoveZeroSharesPairs();
  }, [openPairs]); // Monitora i cambiamenti negli openPairs

  // HIGHLIGHT: Funzione per determinare se una cella deve essere evidenziata
  const shouldHighlightZeroShares = (value) => {
    const numValue = parseFloat(value || "0");
    return numValue === 0;
  };

  // FAKE PAIR: Funzione per creare una pair finta usando lo stesso sistema di configuration
  const createFakePair = () => {
    // Calcola gli ID esistenti per evitare conflitti
    const existingDataShort = (shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length) || 0;
    const existingDataLong = (longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length) || 0;

    // Genera dati fittizi per SHORT component
    const fakeShortData = {
      ticker: "FAKE",
      shares: "100",
      sector: "Technology",
      spread: "0.50",
      volume: "1000",
      status: "WB_LoadedPairs",
      dividend: "0",
      id: existingDataShort.toString()
    };

    // Genera dati fittizi per LONG component
    const fakeLongData = {
      ticker: "FAKE",
      shares: "100",
      sector: "Technology",
      spread: "0.50",
      volume: "1000",
      status: "WB_LoadedPairs",
      dividend: "0",
      id: existingDataLong.toString()
    };

    // Aggiungi ai dati usando lo stesso sistema di configuration
    setShortLoadedTableData((prev) => [...prev, fakeShortData]);
    setLongLoadedTableData((prev) => [...prev, fakeLongData]);

    console.log("✅ Fake pair created successfully!");
  };

  return (
    <ProtectedRoute>
      <div className="w-full px-2 py-6 dark:bg-gray-900">
        <h1 className="text-2xl font-bold mb-6 dark:text-gray-100">Trading Pairs</h1>
        <div className="flex flex-wrap gap-2 mb-6">
          <button
            className={`${isSaving ? 'bg-gray-500 dark:bg-gray-600' : 'bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800'} text-white font-bold py-1 px-2 rounded transition-colors`}
            onClick={savePairsToDatabase}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save to Database'}
          </button>
          <button
            className="bg-purple-500 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-800 text-white font-bold py-1 px-2 rounded transition-colors"
            onClick={initializeEditingData}
            disabled={showEditingSection}
          >
             Edit Pair Data
          </button>
          <button
            className="bg-orange-500 hover:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-800 text-white font-bold py-1 px-2 rounded transition-colors"
            onClick={createFakePair}
            title="Create a fake pair with dummy data for testing"
          >
             Add Fake Pair
          </button>
          {Object.keys(persistentOverrides).length > 0 && (
            <button
              className="bg-orange-500 hover:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-800 text-white font-bold py-1 px-2 rounded transition-colors"
              onClick={resetAllOverrides}
            >
              Reset Custom Data
            </button>
          )}
          {Object.keys(persistentOverrides).length > 0 && (
            <div className="py-1 px-2 rounded bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm">
              {Object.keys(persistentOverrides).length} pair(s) with custom data
            </div>
          )}
          <div className="py-1 px-2 rounded bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm">
            🤖 Auto-Close: ON (0 shares → Closed)
          </div>
          {saveStatus && (
            <div className={`py-1 px-2 rounded ${saveStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'}`}>
              {saveStatus}
            </div>
          )}
        </div>

        {/* EDITING Section */}
        {showEditingSection && (
          <div className="mb-8 p-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-purple-800 dark:text-purple-200">🛠️ Edit Pair Data</h2>
              <div className="flex gap-2">
                <button
                  className={`${hasUnsavedChanges ? 'bg-green-500 hover:bg-green-700' : 'bg-gray-400'} text-white font-bold py-1 px-3 rounded transition-colors`}
                  onClick={applyEditingChanges}
                  disabled={!hasUnsavedChanges}
                >
                  Apply Changes
                </button>
                <button
                  className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded transition-colors"
                  onClick={cancelEditing}
                >
                  Cancel
                </button>
              </div>
            </div>

            {hasUnsavedChanges && (
              <div className="mb-4 p-2 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded text-yellow-800 dark:text-yellow-200 text-sm">
                ⚠️ You have unsaved changes. Click "Apply Changes" to save them.
              </div>
            )}

            <div className="space-y-6">
              {editingPairs.map((pair, pairIndex) => (
                <div key={pair.key} className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium mb-3 text-gray-800 dark:text-gray-200">
                    Pair {pairIndex + 1} - {pair.status === "WB_OpenPositions" ? "OPEN" : "LOADED"}
                  </h3>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* SHORT Component */}
                    <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded border border-red-200 dark:border-red-800">
                      <h4 className="font-medium text-red-800 dark:text-red-200 mb-3">SHORT Component</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ticker</label>
                          <input
                            type="text"
                            value={pair.shortComponent.ticker}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'ticker', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Bid</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedBid}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedBid', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ask</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedAsk}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedAsk', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Last</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedLast}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedLast', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Change</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedChange}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedChange', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Volume</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedVolume}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedVolume', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Dividend</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedDividend}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedDividend', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ex Date</label>
                          <input
                            type="text"
                            value={pair.shortComponent.exDateValue}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'exDateValue', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Spread</label>
                          <input
                            type="text"
                            value={pair.shortComponent.spreadValue}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'spreadValue', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Shares</label>
                          <input
                            type="text"
                            value={pair.shortComponent.formattedAmt}
                            onChange={(e) => updateEditingValue(pairIndex, 'shortComponent', 'formattedAmt', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                      </div>
                    </div>

                    {/* LONG Component */}
                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded border border-green-200 dark:border-green-800">
                      <h4 className="font-medium text-green-800 dark:text-green-200 mb-3">LONG Component</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ticker</label>
                          <input
                            type="text"
                            value={pair.longComponent.ticker}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'ticker', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Bid</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedBid}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedBid', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ask</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedAsk}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedAsk', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Last</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedLast}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedLast', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Change</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedChange}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedChange', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Volume</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedVolume}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedVolume', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Dividend</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedDividend}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedDividend', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Ex Date</label>
                          <input
                            type="text"
                            value={pair.longComponent.exDateValue}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'exDateValue', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Spread</label>
                          <input
                            type="text"
                            value={pair.longComponent.spreadValue}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'spreadValue', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Shares</label>
                          <input
                            type="text"
                            value={pair.longComponent.formattedAmt}
                            onChange={(e) => updateEditingValue(pairIndex, 'longComponent', 'formattedAmt', e.target.value)}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Open Pairs Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200">Open Pairs</h2>
          <div className="flex flex-nowrap gap-0">
            {/* Open SHORT Table */}
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-red-700 dark:text-red-400">Open SHORT</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndOpenShort}>
                <SortableContext items={openShortItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#f8d7da" }} className="dark:bg-red-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Div</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium bg-blue-400 bg-opacity-80">Orders</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {openShortItems.map((item, idx) => (
                          <ShortSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            {/* Open LONG Table */}
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-green-700 dark:text-green-400">Open LONG</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndOpenLong}>
                <SortableContext items={openLongItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#d4edda" }} className="dark:bg-green-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Div</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {openLongItems.map((item, idx) => (
                          <LongSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            {/* Actions table for Open Pairs */}
            <div className="w-28" style={{ minWidth: '7rem', width: '7rem' }}>
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">Actions</h3>
              <div className="bg-white dark:bg-gray-800 overflow-hidden">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: "#e2e3e5" }} className="dark:bg-gray-700">
                      <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center dark:text-black font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {openLongItems.map((item, idx) => (
                      <tr key={idx} className="border-b dark:border-gray-700 transition-colors hover:bg-muted/50 dark:hover:bg-gray-700/50">
                        <td className="border dark:border-gray-700 px-0 py-0 h-8 text-sm w-28" style={{ minWidth: '7rem', width: '7rem' }}>
                          <div className="flex justify-center space-x-2 py-0.5 w-full">
                            <button
                              className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => handleEditPair(item, idx, 'Open', 'long')}
                              title="Edit Data"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              className="bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => setMoveDialog({ open: true, section: 'Open', idx, id: item.id })}
                              title="Move Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                              </svg>
                            </button>
                            <button
                              className="bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={(e) => handleDeletePair(e, item.id, idx, 'open')}
                              title="Delete Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Pair Summary table: only in Open Pairs section, next to Open LONG */}
            <div className="flex-auto min-w-0 max-w-[500px] flex flex-col justify-stretch">
              <h3 className="text-lg font-medium mb-2 text-blue-700 dark:text-blue-300">Pair Summary</h3>
              <div className="bg-white dark:bg-gray-800 overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: '#4875a5ff' }} className="dark:bg-blue-900 dark:bg-opacity-80">
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Combined PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short $Cost</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long $Cost</th>
                    </tr>
                  </thead>
                  <tbody className="dark:text-gray-300">
                    {pairArray && pairArray.length > 0 ? (
                      pairArray.filter(pair => pair.status === "WB_OpenPositions").map((pair, idx) => {
                        const short = pair.shortComponent || {};
                        const long = pair.longComponent || {};
                        return (
                          <tr key={pair.key || idx} className="border-b dark:border-gray-700 h-8 align-middle">
                            <td className="px-2 py-1 text-xs text-right text-red-500 dark:text-red-400">{short.formattedPnl || short.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-green-500 dark:text-green-400">{long.formattedPnl || long.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right font-bold">{pair.combinedPNL || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{short.dollarCost || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{long.dollarCost || '-'}</td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr><td colSpan={7} className="text-center py-2 text-xs text-gray-400">No pairs available</td></tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        {/* end of open pairs */}


{/* Loaded Pairs Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200">Loaded Pairs</h2>
          <div className="flex flex-nowrap gap-0">
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-red-700 dark:text-red-400">Loaded SHORT</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndLoadedShort}>
                <SortableContext items={loadedShortItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#f8d7da" }} className="dark:bg-red-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-1 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Div</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium bg-blue-400 bg-opacity-80">Partials</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {loadedShortItems.map((item, idx) => (
                          <ShortSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-green-700 dark:text-green-400">Loaded LONG</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndLoadedLong}>
                <SortableContext items={loadedLongItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#d4edda" }} className="dark:bg-green-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Div</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {loadedLongItems.map((item, idx) => (
                          <LongSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            <div className="w-28" style={{ minWidth: '7rem', width: '7rem' }}>
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">Actions</h3>
              <div className="bg-white dark:bg-gray-800 overflow-hidden">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: "#e2e3e5" }} className="dark:bg-gray-700">
                      <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center dark:text-black font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loadedLongItems.map((item, idx) => (
                      <tr key={idx} className="border-b dark:border-gray-700 transition-colors hover:bg-muted/50 dark:hover:bg-gray-700/50">
                        <td className="border dark:border-gray-700 px-0 py-0 h-8 text-sm w-28" style={{ minWidth: '7rem', width: '7rem' }}>
                          <div className="flex justify-center space-x-2 py-0.5 w-full">
                            <button
                              className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => handleEditPair(item, idx, 'Loaded', 'long')}
                              title="Edit Data"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              className="bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => setMoveDialog({ open: true, section: 'Loaded', idx, id: item.id })}
                              title="Move Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                              </svg>
                            </button>
                            <button
                              className="bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={(e) => handleDeletePair(e, item.id, idx, 'loaded')}
                              title="Delete Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Pair Summary table for Loaded Pairs */}
            <div className="flex-auto min-w-0 max-w-[500px] flex flex-col justify-stretch">
              <h3 className="text-lg font-medium mb-2 text-blue-700 dark:text-blue-300">Pair Summary</h3>
              <div className="bg-white dark:bg-gray-800 overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: '#4875a5ff' }} className="dark:bg-blue-900 dark:bg-opacity-80">
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Combined PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short $Cost</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long $Cost</th>
                    </tr>
                  </thead>
                  <tbody className="dark:text-gray-300">
                    {pairArray && pairArray.length > 0 ? (
                      pairArray.filter(pair => pair.status === "WB_LoadedPairs").map((pair, idx) => {
                        const short = pair.shortComponent || {};
                        const long = pair.longComponent || {};
                        return (
                          <tr key={pair.key || idx} className="border-b dark:border-gray-700 h-8 align-middle">
                            <td className="px-2 py-1 text-xs text-right text-red-500 dark:text-red-400">{short.formattedPnl || short.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-green-500 dark:text-green-400">{long.formattedPnl || long.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right font-bold">{pair.combinedPNL || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{short.dollarCost || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{long.dollarCost || '-'}</td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr><td colSpan={7} className="text-center py-2 text-xs text-gray-400">No pairs available</td></tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        {/* end of loaded pairs */}


{/* Closed Pairs Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200">Closed Pairs</h2>
          <div className="flex flex-nowrap gap-0">
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-red-700 dark:text-red-400">Closed SHORT</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndClosedShort}>
                <SortableContext items={closedShortItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#f8d7da" }} className="dark:bg-red-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-1 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Div</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium bg-blue-400 bg-opacity-80">Partials</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {closedShortItems.map((item, idx) => (
                          <ShortSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            <div className="flex-auto min-w-0">
              <h3 className="text-lg font-medium mb-2 text-green-700 dark:text-green-400">Closed LONG</h3>
              <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndClosedLong}>
                <SortableContext items={closedLongItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                  <div className="bg-white dark:bg-gray-800 overflow-hidden">
                    <table className="w-full caption-bottom text-sm table-fixed">
                      <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                        <tr style={{ background: "#d4edda" }} className="dark:bg-green-800 dark:bg-opacity-90">
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">S</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Xd</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Cost</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Shares</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Sym</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Bid</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Ask</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Last</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Chg</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Vol/1k</th>
                          <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium">Div</th>
                        </tr>
                      </thead>
                      <tbody className="dark:text-gray-300">
                        {closedLongItems.map((item, idx) => (
                          <LongSortableRow key={idx} id={idx} item={item} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </SortableContext>
              </DndContext>
            </div>
            <div className="w-28" style={{ minWidth: '7rem', width: '7rem' }}>
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">Actions</h3>
              <div className="bg-white dark:bg-gray-800 overflow-hidden">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: "#e2e3e5" }} className="dark:bg-gray-700">
                      <th className="border dark:border-gray-700 px-0 py-0 h-10 text-sm text-center dark:text-black font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {closedLongItems.map((item, idx) => (
                      <tr key={idx} className="border-b dark:border-gray-700 transition-colors hover:bg-muted/50 dark:hover:bg-gray-700/50">
                        <td className="border dark:border-gray-700 px-0 py-0 h-8 text-sm">
                          <div className="flex justify-center space-x-1 py-0.5">
                            <button
                              className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => handleEditPair(item, idx, 'Closed', 'long')}
                              title="Edit Data"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              className="bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={() => setMoveDialog({ open: true, section: 'Closed', idx, id: item.id })}
                              title="Move Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                              </svg>
                            </button>
                            <button
                              className="bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors"
                              onClick={(e) => handleDeletePair(e, item.id, idx, 'closed')}
                              title="Delete Pair"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Pair Summary table for Closed Pairs */}
            <div className="flex-auto min-w-0 max-w-[500px] ml-6 flex flex-col justify-stretch">
              <h3 className="text-lg font-medium mb-2 text-blue-700 dark:text-blue-300">Pair Summary</h3>
              <div className="bg-white dark:bg-gray-800 overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch">
                <table className="w-full caption-bottom text-sm table-fixed">
                  <thead className="[&_tr]:border-b dark:[&_tr]:border-gray-700">
                    <tr style={{ background: '#4875a5ff' }} className="dark:bg-blue-900 dark:bg-opacity-80">
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long Div</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Combined PNL</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Short $Cost</th>
                      <th className="border dark:border-gray-700 px-2 py-1 text-xs text-center">Long $Cost</th>
                    </tr>
                  </thead>
                  <tbody className="dark:text-gray-300">
                    {pairArray && pairArray.length > 0 ? (
                      pairArray.filter(pair => pair.status === "WB_ClosedPositions").map((pair, idx) => {
                        const short = pair.shortComponent || {};
                        const long = pair.longComponent || {};
                        return (
                          <tr key={pair.key || idx} className="border-b dark:border-gray-700 h-8 align-middle">
                            <td className="px-2 py-1 text-xs text-right text-red-500 dark:text-red-400">{short.formattedPnl || short.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-blue-700 dark:text-blue-300">0</td>
                            <td className="px-2 py-1 text-xs text-right text-green-500 dark:text-green-400">{long.formattedPnl || long.pnl || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right font-bold">{pair.combinedPNL || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{short.dollarCost || '-'}</td>
                            <td className="px-2 py-1 text-xs text-right">{long.dollarCost || '-'}</td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr><td colSpan={7} className="text-center py-2 text-xs text-gray-400">No pairs available</td></tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        {/* end of closed pairs */}

      {/* Edit Data Dialog */}
      <EditDataDialog
        editDialog={editDialog}
        setEditDialog={setEditDialog}
        shortOpenTableData={shortOpenTableData}
        shortLoadedTableData={shortLoadedTableData}
        shortClosedTableData={shortClosedTableData}
        longOpenTableData={longOpenTableData}
        longLoadedTableData={longLoadedTableData}
        longClosedTableData={longClosedTableData}
        setShortOpenTableData={setShortOpenTableData}
        setShortLoadedTableData={setShortLoadedTableData}
        setShortClosedTableData={setShortClosedTableData}
        setLongOpenTableData={setLongOpenTableData}
        setLongLoadedTableData={setLongLoadedTableData}
        setLongClosedTableData={setLongClosedTableData}
      />

      {/* Move Pair Dialog - render once at the root, not inside table rows */}
      <Dialog open={moveDialog.open} onOpenChange={open => setMoveDialog(v => ({ ...v, open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move Pair</DialogTitle>
          </DialogHeader>
          <div className="mb-4">Choose destination for this pair:</div>
          <div className="flex flex-col gap-2">
            {moveDialog.open && getMoveDestinations(moveDialog.section).map(dest => (
              <button
                key={dest}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded"
                onClick={() => handleMovePair(moveDialog.section, moveDialog.id, moveDialog.idx, dest)}
              >
                Move to {dest}
              </button>
            ))}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <button className="mt-2 w-full border border-gray-300 rounded py-2">Cancel</button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}