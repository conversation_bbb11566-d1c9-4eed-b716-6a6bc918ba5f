import {
  AdjustmentsHorizontalIcon,
  ArrowTrendingUpIcon,
  BoltIcon,
  CursorArrowRaysIcon,
  PencilIcon,
  UserGroupIcon,
  UserIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";
import NavLink from "./NavLink";

const variants = {
  close: {
    x: -300,
    opacity: 0,
  },
  open: {
    x: 0,
    opacity: 100,
  },
};

const ProjectNavigation = ({ selectedProject, isOpen, setSelectedProject }) => {
  return (
    <motion.nav
      variants={variants}
      initial="close"
      animate="open"
      exit="close"
      transition={{
        duration: 0.25,
        ease: "easeInOut",
      }}
      className={`h-full flex flex-col z-10 gap-8 w-64 absolute bg-neutral-900 dark:bg-neutral-950 ml-0 ${
        isOpen ? "left-64" : "left-20"
      } border-r border-neutral-800 dark:border-neutral-700 p-5`}
    >
      <div className="flex flex-row w-full justify-between place-items-center">
        <h1 className="tracking-wide text-neutral-100 dark:text-neutral-50 text-lg">
          {selectedProject}
        </h1>
        <button onClick={() => setSelectedProject(null)}>
          <XMarkIcon className="w-8 stroke-neutral-400 dark:stroke-neutral-300" />
        </button>
      </div>
      {/* <input
        placeholder="Search"
        type="text"
        className="px-3 py-2 tracking-wide rounded-lg bg-neutral-600/40 text-neutral-100"
      /> */}
      <div className="flex flex-col gap-3">
        <NavLink
          name="Dashboard"
          showName={true}
          href={`Strategies/${selectedProject}/dashboard`}
        >
          <ArrowTrendingUpIcon className="stroke-[0.75] stroke-inherit min-w-8 w-8" />
        </NavLink>
        <NavLink
          name="Configuration"
          showName={true}
          href={`Strategies/${selectedProject}/configuration`}
        >
          <AdjustmentsHorizontalIcon className="stroke-[0.75] stroke-inherit min-w-8 w-8" />
        </NavLink>
      </div>
    </motion.nav>
  );
};

export default ProjectNavigation;
