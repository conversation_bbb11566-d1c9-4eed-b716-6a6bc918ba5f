"use client";
import TooltipButton from "@/components/TooltipButton";
import { createContext, useContext, useState, useEffect } from "react";
import { retrieveAccessToken, retrieveCustomerId, retrieveCorrelId } from "@/actions/schwabAccess";


const shortKeys = ["ticker", "shares", "sector", "spread", "volume", "status", "dividend"];
const longKeys = ["ticker", "shares", "sector", "spread", "volume", "status", "dividend"];

const ExcelDataContext = createContext({
  shortOpenTableData: [],
  shortLoadedTableData: [],
  longOpenTableData: [],
  longLoadedTableData: [],
  shortClosedTableData: [],
  longClosedTableData: [],
  setShortOpenTableData: () => {},
  setShortLoadedTableData: () => {},
  setLongOpenTableData: () => {},
  setLongLoadedTableData: () => {},
  setShortClosedTableData: () => {},
  setLongClosedTableData: () => {},
  updateLongStatus: () => {},
  updateShortStatus: () => {},
});

export function ExcelDataProvider({ children }) {
  const [shortOpenTableData, setShortOpenTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortOpenTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [shortLoadedTableData, setShortLoadedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortLoadedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longOpenTableData, setLongOpenTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longOpenTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longLoadedTableData, setLongLoadedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longLoadedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });
  const [shortClosedTableData, setShortClosedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortClosedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longClosedTableData, setLongClosedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longClosedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  // Nuovo stato per i dati di mercato (dividend e ex-date)
  const [marketDataCache, setMarketDataCache] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("schwabMarketDataCache");
      return data ? JSON.parse(data) : {};
    }
    return {};
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("shortOpenTableData", JSON.stringify(shortOpenTableData));
      localStorage.setItem("shortLoadedTableData", JSON.stringify(shortLoadedTableData));
      localStorage.setItem("longOpenTableData", JSON.stringify(longOpenTableData));
      localStorage.setItem("longLoadedTableData", JSON.stringify(longLoadedTableData));
      localStorage.setItem("shortClosedTableData", JSON.stringify(shortClosedTableData));
      localStorage.setItem("longClosedTableData", JSON.stringify(longClosedTableData));
      localStorage.setItem("schwabMarketDataCache", JSON.stringify(marketDataCache));
    }
  }, [shortOpenTableData, shortLoadedTableData,shortClosedTableData, longOpenTableData, longLoadedTableData, longClosedTableData, marketDataCache]);

  // Move a long row from any table to any other table, like the old system but for all tables
  const updateLongStatus = (rowId, newStatus) => {
    // Try to find the row in all tables
    let row = longLoadedTableData.find(r => r.id === rowId)
      || longOpenTableData.find(r => r.id === rowId)
      || longClosedTableData.find(r => r.id === rowId);
    if (!row) return;
    const updated = { ...row, status: newStatus };
    // Remove from all tables
    setLongLoadedTableData(prev => prev.filter(r => r.id !== rowId));
    setLongOpenTableData(prev => prev.filter(r => r.id !== rowId));
    setLongClosedTableData(prev => prev.filter(r => r.id !== rowId));
    // Add to the right table
    if (newStatus === "WB_LoadedPairs") {
      setLongLoadedTableData(prev => [...prev, updated]);
    } else if (newStatus === "WB_OpenPositions") {
      setLongOpenTableData(prev => [...prev, updated]);
    } else if (newStatus === "WB_ClosedPositions") {
      setLongClosedTableData(prev => [...prev, updated]);
    }
  };

  // Move a short row from any table to any other table, like the old system but for all tables
  const updateShortStatus = (rowId, newStatus) => {
    let row = shortLoadedTableData.find(r => r.id === rowId)
      || shortOpenTableData.find(r => r.id === rowId)
      || shortClosedTableData.find(r => r.id === rowId);
    if (!row) return;
    const updated = { ...row, status: newStatus };
    setShortLoadedTableData(prev => prev.filter(r => r.id !== rowId));
    setShortOpenTableData(prev => prev.filter(r => r.id !== rowId));
    setShortClosedTableData(prev => prev.filter(r => r.id !== rowId));
    if (newStatus === "WB_LoadedPairs") {
      setShortLoadedTableData(prev => [...prev, updated]);
    } else if (newStatus === "WB_OpenPositions") {
      setShortOpenTableData(prev => [...prev, updated]);
    } else if (newStatus === "WB_ClosedPositions") {
      setShortClosedTableData(prev => [...prev, updated]);
    }
  };
  const updateShortClosedStatus = (rowId, newStatus) => {
    setShortOpenTableData(prev => {
      const row = prev.find(r => r.id === rowId);
      if (!row) return prev;
      const updatedRow = { ...row, status: newStatus };
      setShortClosedTableData(closedPrev => {
        if (closedPrev.some(r => r.id === rowId)) {
          return closedPrev;
        }
        return [...closedPrev, updatedRow];
      });
      return prev.filter(r => r.id !== rowId);
    });
  };
  const updateLongClosedStatus = (rowId, newStatus) => {
    setLongOpenTableData(prev => {
      const row = prev.find(r => r.id === rowId);
      if (!row) return prev;
      const updatedRow = { ...row, status: newStatus };
      setLongClosedTableData(closedPrev => {
        if (closedPrev.some(r => r.id === rowId)) {
          return closedPrev;
        }
        return [...closedPrev, updatedRow];
      });
      return prev.filter(r => r.id !== rowId);
    });
  };

  // Funzioni per gestire i dati di mercato (dividend e ex-date)
  const updateMarketData = (ticker, data) => {
    setMarketDataCache(prev => ({
      ...prev,
      [ticker]: {
        ...prev[ticker],
        ...data,
        lastUpdated: new Date().toISOString()
      }
    }));
  };

  const getMarketData = (ticker) => {
    return marketDataCache[ticker] || null;
  };

  const clearMarketDataCache = () => {
    setMarketDataCache({});
    if (typeof window !== "undefined") {
      localStorage.removeItem("schwabMarketDataCache");
    }
  };

  return (
    <ExcelDataContext.Provider value={{
      shortOpenTableData,
      shortLoadedTableData,
      longOpenTableData,
      longLoadedTableData,
      shortClosedTableData,
      longClosedTableData,
      setShortOpenTableData,
      setShortLoadedTableData,
      setLongOpenTableData,
      setLongLoadedTableData,
      setShortClosedTableData,
      setLongClosedTableData,
      updateLongStatus,
      updateShortStatus,
      updateShortClosedStatus,
      updateLongClosedStatus,
      // Nuove funzioni per i dati di mercato
      marketDataCache,
      updateMarketData,
      getMarketData,
      clearMarketDataCache
    }}>
      {children}
    </ExcelDataContext.Provider>
  );
}

export function useExcelData() {
  return useContext(ExcelDataContext);
}

function ExcelInput() {
  const [pasteData, setPasteData] = useState("");
  const [updateStatus, setUpdateStatus] = useState("");
  const [redirectAfterSubmit, setRedirectAfterSubmit] = useState(true);
  const [savedSymbols, setSavedSymbols] = useState([]);
  const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData, shortClosedTableData, longClosedTableData } = useExcelData();

  // Load saved symbols on component mount
  useEffect(() => {
    const loadSavedSymbols = async () => {
      try {
        const response = await fetch('/api/get-saved-symbols');
        if (response.ok) {
          const data = await response.json();
          setSavedSymbols(data.symbolsArray || []);
          console.log('Loaded saved symbols:', data.symbolsArray);
        }
      } catch (error) {
        console.error('Error loading saved symbols:', error);
      }
    };

    loadSavedSymbols();
  }, []);

  const handleChange = (e) => {
    setPasteData(e.target.value);
  };

  const handleSubmit = async () => {
    const rows = pasteData.split(/\r?\n/).filter((row) => row.trim() !== "");
    const parsedData = rows.map((row) => row.split("\t"))
    const existingDataShort = (shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length) || 0;
    const existingDataLong = (longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length) || 0;
    const shortData = parsedData.map((row, index) => {
      const obj = {};
      shortKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else if (key === "dividend") {
          obj[key] = "0";
        } else {
          obj[key] = row[idx] || "";
        }
      });
      return { ...obj, id: (existingDataShort + index).toString() };
    });

    const longData = parsedData.map((row, index) => {
      const obj = {};
      longKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else if (key === "dividend") {
          obj[key] = "0";
        } else {
          obj[key] = row[idx + 5] || "";
        }
      });
      return { ...obj, id: (existingDataLong + index).toString() };
    });

    setShortLoadedTableData((prev) => [...prev, ...shortData]);
    setLongLoadedTableData((prev) => [...prev, ...longData]);
    setPasteData("");

    // Display status message
    setUpdateStatus("Data processed successfully. Pairs will be saved to database when created.");

    // Redirect to dashboard if option is enabled
    if (redirectAfterSubmit) {
      // Wait a moment to ensure data is saved to localStorage
      setTimeout(() => {
        window.location.href = "/Strategies/WB/dashboard";
      }, 500);
    }
  };

  // Function to update stock symbols on the server
  const updateStockSymbols = async () => {
    try {
      setUpdateStatus("Update in progress...");

      // Collect all ticker symbols from both short and long data
      const shortTickers = [...shortLoadedTableData, ...shortOpenTableData, ...shortClosedTableData]
        .map(item => item.ticker)
        .filter(ticker => ticker && ticker.trim() !== "");

      const longTickers = [...longLoadedTableData, ...longOpenTableData, ...longClosedTableData]
        .map(item => item.ticker)
        .filter(ticker => ticker && ticker.trim() !== "");

      // Combine and remove duplicates
      const allTickers = [...new Set([...shortTickers, ...longTickers])];

      if (allTickers.length === 0) {
        setUpdateStatus("No symbols found to update");
        return;
      }

      console.log("Symbols to send to server:", allTickers);

      // First, test if the server is responding
      try {
        const testUrl = "https://localhost:3001/test";
        setUpdateStatus(`Verifying server connection: ${testUrl}...`);

        const testResponse = await fetch(testUrl);
        if (!testResponse.ok) {
          setUpdateStatus(`Error: The server is not responding correctly. Code: ${testResponse.status}`);
          return;
        }

        const testData = await testResponse.json();
        console.log("Test server response:", testData);
        setUpdateStatus(`Server connected. Current symbols: ${testData.currentSymbols}. Sending new symbols...`);
      } catch (testError) {
        console.error("Error in connection test:", testError);
        setUpdateStatus(`Connection error: ${testError.message}. Make sure the server is running on http://localhost:3001`);
        return;
      }

      // Send to server
      const url = "https://localhost:3001/update-stock-symbols";

      try {
        const accessToken = await retrieveAccessToken();
        const customerId = await retrieveCustomerId();
        const correlId = await retrieveCorrelId();

        // Get user session for email
        const session = await fetch('/api/auth/session');
        const sessionData = await session.json();
        const userEmail = sessionData?.user?.email;

        if (!userEmail) {
          console.warn('No user email found in session, symbols will not be associated with user account');
        }

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            symbols: allTickers,
            token: accessToken,
            clientCustomerId: customerId,
            clientCorrelId: correlId,
            userEmail: userEmail
          }),
          credentials: "include",
        });

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const textResponse = await response.text();
          console.error("Server returned non-JSON response:", textResponse);
          setUpdateStatus(`Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001`);
          return;
        }

        const data = await response.json();

        if (response.ok) {
          setUpdateStatus(`Symbols updated successfully: ${data.symbols}`);

          // Update the saved symbols display
          setSavedSymbols(data.symbols.split(','));
        } else {
          setUpdateStatus(`Error: ${data.error || 'Unknown error'}`);
        }
      } catch (fetchError) {
        console.error("Error in fetch request:", fetchError);
        setUpdateStatus(`Connection error: ${fetchError.message}. Make sure the server is running on http://localhost:3001`);
      }
    } catch (error) {
      console.error("Error updating symbols:", error);
      setUpdateStatus(`Error: ${error.message || 'Unknown error'}`);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6">
        <h2 className="text-xl font-semibold text-white">Paste Data from Excel</h2>
      </div>
      <div className="p-6">
        <div className="mb-4">
          <textarea
            onChange={handleChange}
            value={pasteData}
            placeholder="Paste Excel data here (tabular format)"
            rows="10"
            className="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
          />
        </div>

        <div className="flex flex-wrap gap-3 mb-6">
          <TooltipButton
            onClick={handleSubmit}
            className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-md transition-colors"
            tooltipText="Process the pasted Excel data and update the tables"
          >
            Process Data
          </TooltipButton>

          {/* Saved Symbols Display */}
          {savedSymbols.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                Currently Saved Symbols ({savedSymbols.length}):
              </h4>
              <div className="text-xs text-blue-700 dark:text-blue-300 break-words">
                {savedSymbols.join(', ')}
              </div>
            </div>
          )}

          <TooltipButton
            onClick={updateStockSymbols}
            className="bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-medium py-2 px-4 rounded-md transition-colors"
            tooltipText="Update stock symbols on the server for real-time data"
          >
            Update Symbols on Server
          </TooltipButton>
        </div>

        <div className="mb-4 flex items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700">
          <input
            type="checkbox"
            id="redirectToggle"
            checked={redirectAfterSubmit}
            onChange={() => setRedirectAfterSubmit(!redirectAfterSubmit)}
            className="mr-2 h-4 w-4 text-blue-600 dark:text-blue-500 focus:ring-blue-500 dark:focus:ring-blue-600 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="redirectToggle" className="text-sm text-gray-700 dark:text-gray-300">
            Redirect to dashboard after processing data
          </label>
        </div>

        {updateStatus && (
          <div className={`mt-4 p-4 rounded-md ${updateStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800'}`}>
            {updateStatus}
          </div>
        )}
      </div>
    </div>
  );
}

function ClearButton() {
  const {
    setShortOpenTableData,
    setShortLoadedTableData,
    setLongOpenTableData,
    setLongLoadedTableData,
    setShortClosedTableData,
    setLongClosedTableData,
    clearMarketDataCache,
  } = useExcelData();
  const [clearStatus, setClearStatus] = useState("");

  const clearData = () => {
    setShortOpenTableData([]);
    setShortLoadedTableData([]);
    setLongOpenTableData([]);
    setLongLoadedTableData([]);
    setShortClosedTableData([]);
    setLongClosedTableData([]);
    clearMarketDataCache(); // Pulisce anche i dati di mercato
    if (typeof window !== "undefined") {
      localStorage.removeItem("shortOpenTableData");
      localStorage.removeItem("shortLoadedTableData");
      localStorage.removeItem("longOpenTableData");
      localStorage.removeItem("longLoadedTableData");
      localStorage.removeItem("shortClosedTableData");
      localStorage.removeItem("longClosedTableData");
      localStorage.removeItem("schwabMarketDataCache");
    }
    setClearStatus("All data has been cleared (including market data)");
    setTimeout(() => setClearStatus(""), 3000);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6">
      <div className="bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-3 px-6">
        <h2 className="text-xl font-semibold text-white">Data Management</h2>
      </div>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Clear all Excel data from memory and local storage. This action cannot be undone.
            </p>
            <TooltipButton
              onClick={clearData}
              className="bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white font-medium py-2 px-4 rounded-md transition-colors"
              tooltipText="Clear all Excel data from memory and local storage"
            >
              Clear All Data
            </TooltipButton>
          </div>
          <div className="hidden md:block">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-200 dark:text-red-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </div>
        </div>
        {clearStatus && (
          <div className="mt-4 p-4 rounded-md bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800">
            {clearStatus}
          </div>
        )}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 py-8">
      {/* Header section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 shadow-md dark:shadow-indigo-950 mb-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold">WB Configuration</h1>
          <p className="text-blue-100 mt-2">Configure your WB trading strategy settings</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4">
        <ExcelInput />
        <ClearButton />
      </div>
    </div>
  );
}
