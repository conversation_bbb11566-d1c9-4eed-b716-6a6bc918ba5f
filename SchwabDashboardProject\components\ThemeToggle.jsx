import { SunIcon, MoonIcon } from "@heroicons/react/24/solid";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function ThemeToggle({ iconOnly, floating = false }) {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const baseClasses = "bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold rounded flex items-center transition-colors";
  const floatingClasses = floating ? "fixed bottom-4 right-4 z-50 shadow-lg py-2 px-3 rounded-full" : "py-1 px-2";
  const buttonClasses = `${baseClasses} ${floatingClasses}`;

  return (
    <TooltipProvider delayDuration={20}>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className={buttonClasses}
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            aria-label={theme === "dark" ? "Switch to light mode" : "Switch to dark mode"}
          >
            {iconOnly ? (
              theme === "dark" ? (
                <SunIcon className="w-20" />
              ) : (
                <MoonIcon className="w-20" />
              )
            ) : (
              <>
                {theme === "dark" ? (
                  <SunIcon className="h-5 w-5 mr-2" />
                ) : (
                  <MoonIcon className="h-5 w-5 mr-2" />
                )}
                {theme === "dark" ? "Light" : "Dark"} Mode
              </>
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent side={floating ? "left" : "right"}>
          <p>Switch to {theme === "dark" ? "light" : "dark"} mode</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
