"use client";
import ProtectedRoute from "../components/ProtectedRoute";
import { useSession, signOut as logoutUser } from "next-auth/react";
import { getAuthorizationCodeURL } from "../actions/schwabAccess";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function HomePage() {
  const { data: session, status } = useSession();
  const [isSchwabCallback, setIsSchwabCallback] = useState(false);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  useEffect(() => {
    console.log("HomePage session:", session, "status:", status);
  }, [session, status]);

  useEffect(() => {
    // Check if we're on the callback URL
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    if (code) {
      setIsSchwabCallback(true);
    }

    // Always check Schwab login status on page load
    checkSchwabLogin();
  }, []);

  const checkSchwabLogin = async () => {
    try {
      // Chiedi lo stato Schwab al backend (cookie httpOnly)
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      if (data.loggedIn) {
        localStorage.setItem('schwabLoggedIn', 'true');
      } else {
        localStorage.removeItem('schwabLoggedIn');
      }
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
    }
  };

  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        // Set a flag to indicate we're attempting to log in
        localStorage.setItem('schwabLoginAttempt', 'true');
        window.location.href = await getAuthorizationCodeURL();
        return;
      }

      // If we have a code, we're logged in
      localStorage.setItem('schwabLoggedIn', 'true');
      setIsLoggedInToSchwab(true);
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  const handleLogOutFromSchwab = () => {
    try {
      // Clear Schwab-related cookies
      document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;

      // Clear localStorage flags
      localStorage.removeItem('schwabLoggedIn');
      localStorage.removeItem('schwabLoginAttempt');

      // Update state
      setIsLoggedInToSchwab(false);

      // Refresh the page to ensure all state is reset
      window.location.reload();
    } catch (error) {
      console.error("Error logging out from Schwab:", error);
    }
  };

  const handleLogout = async () => {
    localStorage.clear();
    sessionStorage.clear();
    await logoutUser({ callbackUrl: "/generalLogin" });
    // window.location.href = "/generalLogin";
  };

  // Navigation cards data - More professional color scheme and icons
  const navigationCards = [
    {
      title: "Account Summary",
      description: "View your account summary and balances",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      href: "/accountsSummary",
      color: "bg-slate-700",
      hoverColor: "hover:bg-slate-800",
      borderColor: "border-slate-600"
    },
    {
      title: "WB Dashboard",
      description: "Manage your WB trading pairs",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
        </svg>
      ),
      href: "/Strategies/WB/dashboard",
      color: "bg-blue-700",
      hoverColor: "hover:bg-blue-800",
      borderColor: "border-blue-600"
    },
    {
      title: "WB Configuration",
      description: "Configure your WB trading strategy",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      href: "/Strategies/WB/configuration",
      color: "bg-gray-700",
      hoverColor: "hover:bg-gray-800",
      borderColor: "border-gray-600"
    },
    {
      title: "Saved Pairs",
      description: "View your saved trading pairs",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
        </svg>
      ),
      href: "/savedPairs",
      color: "bg-indigo-700",
      hoverColor: "hover:bg-indigo-800",
      borderColor: "border-indigo-600"
    },
    {
      title: "File Handler",
      description: "Manage your data files",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
        </svg>
      ),
      href: "/fileHandler",
      color: "bg-slate-700",
      hoverColor: "hover:bg-slate-800",
      borderColor: "border-slate-600"
    },
    {
      title: "Account Activity",
      description: "View your account activity",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      href: "/testingAccountActivity",
      color: "bg-blue-700",
      hoverColor: "hover:bg-blue-800",
      borderColor: "border-blue-600"
    }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Professional Hero Section */}
        <div className="bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 dark:from-slate-900 dark:via-slate-800 dark:to-black text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="flex flex-col lg:flex-row items-center justify-between">
              <div className="mb-8 lg:mb-0 lg:mr-12 flex-1">
                <div className="mb-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mb-4">
                    Professional Trading Platform
                  </span>
                </div>
                <h1 className="text-4xl lg:text-5xl font-bold tracking-tight mb-6 leading-tight">
                  {isSchwabCallback ? (
                    <>
                      <span className="text-green-400">Connected</span> Successfully
                    </>
                  ) : (
                    <>
                      Investment <span className="text-blue-400">Management</span><br />
                      <span className="text-gray-300">Platform</span>
                    </>
                  )}
                </h1>
                <p className="text-xl text-gray-300 max-w-2xl mb-8 leading-relaxed">
                  {isSchwabCallback
                    ? "Your Schwab connection is active. Access real-time market data and manage your investment strategies with confidence."
                    : "Streamline your investment workflow with advanced analytics, real-time data integration, and comprehensive portfolio management tools."}
                </p>
                {session && !isLoggedInToSchwab && (
                  <button
                    className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    onClick={() => handleLogInToSchwab()}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    Connect to Schwab
                  </button>
                )}
              </div>
              <div className="flex-shrink-0 lg:flex-1 lg:max-w-md">
                {/* Professional illustration */}
                <div className="relative">
                  <div className="w-64 h-64 lg:w-80 lg:h-80 mx-auto bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-2xl backdrop-blur-sm border border-white/10 flex items-center justify-center">
                    <div className="text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-24 w-24 mx-auto mb-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                      </svg>
                      <div className="text-sm text-gray-300 font-medium">Real-time Analytics</div>
                    </div>
                  </div>
                  {/* Floating elements */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-green-400/30">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-blue-400/30">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Professional User Welcome Section */}
        {session && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="bg-white dark:bg-gray-800 shadow-lg dark:shadow-gray-900/50 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div className="flex items-center mb-4 sm:mb-0">
                  <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-3 mr-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Welcome back, {session.user?.name || session.user?.email?.split('@')[0]}
                    </h2>
                    <div className="flex items-center mt-1">
                      <div className={`w-2 h-2 rounded-full mr-2 ${isLoggedInToSchwab ? 'bg-green-500' : 'bg-amber-500'}`}></div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {isLoggedInToSchwab ? "Schwab Connected • Real-time data active" : "Schwab Disconnected • Limited data access"}
                      </p>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => handleLogout()}
                  className="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 text-sm font-medium border border-gray-300 dark:border-gray-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Professional System Status Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white dark:bg-gray-800 shadow-lg dark:shadow-gray-900/50 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-800/80 flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div className="flex items-center mb-3 sm:mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <h2 className="text-base font-semibold text-gray-900 dark:text-gray-100">System Status</h2>
              </div>
              {isLoggedInToSchwab && (
                <button
                  onClick={handleLogOutFromSchwab}
                  className="inline-flex items-center px-3 py-1.5 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 text-sm font-medium border border-gray-300 dark:border-gray-600 shadow-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Disconnect Schwab
                </button>
              )}
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center mb-3">
                    <div className={`w-3 h-3 rounded-full ${isLoggedInToSchwab ? 'bg-green-500' : 'bg-red-500'} mr-2.5`}></div>
                    <span className="font-medium text-gray-900 dark:text-gray-100">Schwab Connection</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 ml-5.5">
                    {isLoggedInToSchwab ? 'Active • Real-time data' : 'Inactive • No market data'}
                  </p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center mb-3">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2.5"></div>
                    <span className="font-medium text-gray-900 dark:text-gray-100">Dashboard Services</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 ml-5.5">
                    Online • All systems operational
                  </p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center mb-3">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2.5"></div>
                    <span className="font-medium text-gray-900 dark:text-gray-100">Data Processing</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 ml-5.5">
                    Active • Analytics engine running
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Navigation Cards */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
          <div className="flex items-center mb-6">
            <div className="w-10 h-1 bg-blue-600 dark:bg-blue-500 rounded-full mr-3"></div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Platform Navigation</h2>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {navigationCards.map((card, index) => (
              <Link href={card.href} key={index}>
                <div className={`group bg-white dark:bg-gray-800 border ${card.borderColor} rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col`}>
                  <div className={`${card.color} ${card.hoverColor} p-5 flex items-center justify-center transition-all duration-300`}>
                    <div className="text-white">
                      {card.icon}
                    </div>
                  </div>
                  <div className="p-5 flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {card.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      {card.description}
                    </p>
                    <div className="flex items-center text-sm text-blue-600 dark:text-blue-400 font-medium">
                      <span className="mr-2">Access</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
  }
