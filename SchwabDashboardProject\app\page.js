"use client";
import ProtectedRoute from "../components/ProtectedRoute";
import { useSession, signOut as logoutUser } from "next-auth/react";
import { getAuthorizationCodeURL } from "../actions/schwabAccess";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function HomePage() {
  const { data: session, status } = useSession();
  const [isSchwabCallback, setIsSchwabCallback] = useState(false);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  useEffect(() => {
    console.log("HomePage session:", session, "status:", status);
  }, [session, status]);

  useEffect(() => {
    // Check if we're on the callback URL
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    if (code) {
      setIsSchwabCallback(true);
    }

    // Always check Schwab login status on page load
    checkSchwabLogin();
  }, []);

  const checkSchwabLogin = async () => {
    try {
      // Chiedi lo stato Schwab al backend (cookie httpOnly)
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      if (data.loggedIn) {
        localStorage.setItem('schwabLoggedIn', 'true');
      } else {
        localStorage.removeItem('schwabLoggedIn');
      }
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
    }
  };

  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        // Set a flag to indicate we're attempting to log in
        localStorage.setItem('schwabLoginAttempt', 'true');
        window.location.href = await getAuthorizationCodeURL();
        return;
      }

      // If we have a code, we're logged in
      localStorage.setItem('schwabLoggedIn', 'true');
      setIsLoggedInToSchwab(true);
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  const handleLogOutFromSchwab = () => {
    try {
      // Clear Schwab-related cookies
      document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;

      // Clear localStorage flags
      localStorage.removeItem('schwabLoggedIn');
      localStorage.removeItem('schwabLoginAttempt');

      // Update state
      setIsLoggedInToSchwab(false);

      // Refresh the page to ensure all state is reset
      window.location.reload();
    } catch (error) {
      console.error("Error logging out from Schwab:", error);
    }
  };

  const handleLogout = async () => {
    localStorage.clear();
    sessionStorage.clear();
    await logoutUser({ callbackUrl: "/generalLogin" });
    // window.location.href = "/generalLogin";
  };

  // Rich navigation cards with icons and colors
  const navigationCards = [
    {
      title: "Account Summary",
      description: "View your account summary and balances",
      icon: "📊",
      href: "/accountsSummary",
      bgColor: "bg-gradient-to-br from-blue-500 to-blue-600",
      shadowColor: "shadow-blue-500/20",
      hoverEffect: "hover:shadow-blue-500/40 hover:-translate-y-1"
    },
    {
      title: "WB Dashboard",
      description: "Manage your WB trading pairs",
      icon: "📈",
      href: "/Strategies/WB/dashboard",
      bgColor: "bg-gradient-to-br from-emerald-500 to-emerald-600",
      shadowColor: "shadow-emerald-500/20",
      hoverEffect: "hover:shadow-emerald-500/40 hover:-translate-y-1"
    },
    {
      title: "WB Configuration",
      description: "Configure your WB trading strategy",
      icon: "⚙️",
      href: "/Strategies/WB/configuration",
      bgColor: "bg-gradient-to-br from-purple-500 to-purple-600",
      shadowColor: "shadow-purple-500/20",
      hoverEffect: "hover:shadow-purple-500/40 hover:-translate-y-1"
    },
    {
      title: "Saved Pairs",
      description: "View your saved trading pairs",
      icon: "🔖",
      href: "/savedPairs",
      bgColor: "bg-gradient-to-br from-amber-500 to-amber-600",
      shadowColor: "shadow-amber-500/20",
      hoverEffect: "hover:shadow-amber-500/40 hover:-translate-y-1"
    },
    {
      title: "File Handler",
      description: "Manage your data files",
      icon: "📁",
      href: "/fileHandler",
      bgColor: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      shadowColor: "shadow-indigo-500/20",
      hoverEffect: "hover:shadow-indigo-500/40 hover:-translate-y-1"
    },
    {
      title: "Account Activity",
      description: "View your account activity",
      icon: "📝",
      href: "/testingAccountActivity",
      bgColor: "bg-gradient-to-br from-rose-500 to-rose-600",
      shadowColor: "shadow-rose-500/20",
      hoverEffect: "hover:shadow-rose-500/40 hover:-translate-y-1"
    }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20">
        {/* Rich Hero Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-900">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-black/10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="text-center">
              <div className="mb-8">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm border border-white/30">
                  ✨ Professional Trading Platform
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                {isSchwabCallback ? (
                  <>
                    🎉 <span className="text-green-300">Connected</span> Successfully!
                  </>
                ) : (
                  <>
                    Your Trading <br />
                    <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                      Command Center
                    </span>
                  </>
                )}
              </h1>

              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto mb-10 leading-relaxed">
                {isSchwabCallback
                  ? "🚀 Your Schwab connection is live! Access real-time market data and manage your investment strategies with confidence."
                  : "🎯 Streamline your investment workflow with advanced analytics, real-time data integration, and comprehensive portfolio management tools."}
              </p>

              {session && !isLoggedInToSchwab && (
                <button
                  className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold rounded-xl shadow-2xl hover:shadow-white/25 transition-all duration-300 transform hover:scale-105 hover:bg-blue-50"
                  onClick={() => handleLogInToSchwab()}
                >
                  <span className="mr-3 text-2xl">🔗</span>
                  Connect to Schwab
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full backdrop-blur-sm animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-16 h-16 bg-yellow-300/20 rounded-full backdrop-blur-sm animate-bounce"></div>
          <div className="absolute top-1/2 right-20 w-12 h-12 bg-green-300/20 rounded-full backdrop-blur-sm animate-ping"></div>
        </div>

        {/* Rich User Welcome Section */}
        {session && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                  <div className="flex items-center mb-4 sm:mb-0">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mr-4 backdrop-blur-sm">
                      <span className="text-2xl">👤</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">
                        Welcome back, {session.user?.name || session.user?.email?.split('@')[0]}! 👋
                      </h2>
                      <div className="flex items-center mt-2">
                        <div className={`w-3 h-3 rounded-full mr-2 ${isLoggedInToSchwab ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
                        <span className="text-blue-100 text-sm">
                          {isLoggedInToSchwab ? "🟢 Schwab Connected • Real-time data active" : "🔴 Schwab Disconnected • Limited access"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {session && !isLoggedInToSchwab && (
                      <button
                        onClick={() => handleLogInToSchwab()}
                        className="px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        🔗 Connect Schwab
                      </button>
                    )}
                    {isLoggedInToSchwab && (
                      <button
                        onClick={handleLogOutFromSchwab}
                        className="px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10"
                      >
                        Disconnect
                      </button>
                    )}
                    <button
                      onClick={() => handleLogout()}
                      className="px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10"
                    >
                      🚪 Logout
                    </button>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="p-6 bg-gray-50 dark:bg-gray-800/50">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">📊</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Dashboard Services</div>
                    <div className="text-xs text-green-600 dark:text-green-400 font-medium">✅ Online</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">⚡</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Data Processing</div>
                    <div className="text-xs text-green-600 dark:text-green-400 font-medium">✅ Active</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">🔄</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Market Data</div>
                    <div className={`text-xs font-medium ${isLoggedInToSchwab ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {isLoggedInToSchwab ? '✅ Live' : '❌ Offline'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Rich Navigation Cards */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              🚀 Platform Features
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Access all your trading tools and analytics from one powerful dashboard
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {navigationCards.map((card, index) => (
              <Link href={card.href} key={index}>
                <div className={`group relative overflow-hidden rounded-2xl ${card.bgColor} ${card.shadowColor} shadow-2xl ${card.hoverEffect} transition-all duration-300 cursor-pointer`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 bg-black/10">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                  </div>

                  {/* Content */}
                  <div className="relative p-8 text-white">
                    <div className="flex items-center justify-between mb-6">
                      <div className="text-4xl">{card.icon}</div>
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold mb-3 group-hover:text-yellow-200 transition-colors duration-300">
                      {card.title}
                    </h3>

                    <p className="text-white/90 text-sm leading-relaxed mb-6">
                      {card.description}
                    </p>

                    <div className="flex items-center text-sm font-medium">
                      <span className="mr-2">Access Now</span>
                      <div className="w-6 h-0.5 bg-white/60 group-hover:w-8 transition-all duration-300"></div>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Footer Section */}
        <div className="bg-gradient-to-r from-gray-900 to-blue-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-6">Ready to Optimize Your Trading?</h2>
              <p className="text-blue-200 max-w-2xl mx-auto mb-8">
                Access real-time market data, advanced analytics, and powerful trading tools all in one place.
              </p>

              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/Strategies/WB/dashboard">
                  <div className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                    Go to Dashboard
                  </div>
                </Link>
                <Link href="/Strategies/WB/configuration">
                  <div className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors backdrop-blur-sm">
                    Configure Strategy
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
  }
