"use client";
import ProtectedRoute from "../components/ProtectedRoute";
import { useSession, signOut as logoutUser } from "next-auth/react";
import { getAuthorizationCodeURL } from "../actions/schwabAccess";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function HomePage() {
  const { data: session, status } = useSession();
  const [isSchwabCallback, setIsSchwabCallback] = useState(false);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  useEffect(() => {
    console.log("HomePage session:", session, "status:", status);
  }, [session, status]);

  useEffect(() => {
    // Check if we're on the callback URL
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    if (code) {
      setIsSchwabCallback(true);
    }

    // Always check Schwab login status on page load
    checkSchwabLogin();
  }, []);

  const checkSchwabLogin = async () => {
    try {
      // Chiedi lo stato Schwab al backend (cookie httpOnly)
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      if (data.loggedIn) {
        localStorage.setItem('schwabLoggedIn', 'true');
      } else {
        localStorage.removeItem('schwabLoggedIn');
      }
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
    }
  };

  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        // Set a flag to indicate we're attempting to log in
        localStorage.setItem('schwabLoginAttempt', 'true');
        window.location.href = await getAuthorizationCodeURL();
        return;
      }

      // If we have a code, we're logged in
      localStorage.setItem('schwabLoggedIn', 'true');
      setIsLoggedInToSchwab(true);
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  const handleLogOutFromSchwab = () => {
    try {
      // Clear Schwab-related cookies
      document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;

      // Clear localStorage flags
      localStorage.removeItem('schwabLoggedIn');
      localStorage.removeItem('schwabLoginAttempt');

      // Update state
      setIsLoggedInToSchwab(false);

      // Refresh the page to ensure all state is reset
      window.location.reload();
    } catch (error) {
      console.error("Error logging out from Schwab:", error);
    }
  };

  const handleLogout = async () => {
    localStorage.clear();
    sessionStorage.clear();
    await logoutUser({ callbackUrl: "/generalLogin" });
    // window.location.href = "/generalLogin";
  };

  // Simple and clean navigation cards
  const navigationCards = [
    {
      title: "Account Summary",
      description: "View account balances and portfolio overview",
      href: "/accountsSummary"
    },
    {
      title: "WB Dashboard",
      description: "Monitor and manage trading pairs",
      href: "/Strategies/WB/dashboard"
    },
    {
      title: "WB Configuration",
      description: "Setup and configure trading strategies",
      href: "/Strategies/WB/configuration"
    },
    {
      title: "Saved Pairs",
      description: "Access your saved trading pairs",
      href: "/savedPairs"
    },
    {
      title: "File Handler",
      description: "Import and manage data files",
      href: "/fileHandler"
    },
    {
      title: "Account Activity",
      description: "Review transaction history and activity",
      href: "/testingAccountActivity"
    }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white dark:bg-gray-900">
        {/* Clean Header */}
        <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <div className="max-w-6xl mx-auto px-6 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-semibold text-gray-900 dark:text-white mb-2">
                {isSchwabCallback ? "Welcome Back" : "Trading Dashboard"}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                {isSchwabCallback
                  ? "Successfully connected to Schwab. Your trading platform is ready."
                  : "Manage your investments and trading strategies from one central location."}
              </p>
            </div>
          </div>
        </div>

        {/* User Info */}
        {session && (
          <div className="max-w-6xl mx-auto px-6 py-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Logged in as <span className="font-medium text-gray-900 dark:text-white">{session.user?.name || session.user?.email}</span>
                </p>
                <div className="flex items-center mt-1">
                  <div className={`w-2 h-2 rounded-full mr-2 ${isLoggedInToSchwab ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {isLoggedInToSchwab ? "Schwab Connected" : "Schwab Disconnected"}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {session && !isLoggedInToSchwab && (
                  <button
                    onClick={() => handleLogInToSchwab()}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Connect Schwab
                  </button>
                )}
                {isLoggedInToSchwab && (
                  <button
                    onClick={handleLogOutFromSchwab}
                    className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    Disconnect
                  </button>
                )}
                <button
                  onClick={() => handleLogout()}
                  className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        )}



        {/* Simple Navigation */}
        <div className="max-w-6xl mx-auto px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {navigationCards.map((card, index) => (
              <Link href={card.href} key={index}>
                <div className="group p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-md">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {card.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {card.description}
                  </p>
                  <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                    <span>Open</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
  }
