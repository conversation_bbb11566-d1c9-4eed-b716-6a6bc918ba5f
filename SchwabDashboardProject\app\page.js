"use client";
import ProtectedRoute from "../components/ProtectedRoute";
import { useSession, signOut as logoutUser } from "next-auth/react";
import { getAuthorizationCodeURL } from "../actions/schwabAccess";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function HomePage() {
  const { data: session, status } = useSession();
  const [isSchwabCallback, setIsSchwabCallback] = useState(false);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  useEffect(() => {
    console.log("HomePage session:", session, "status:", status);
  }, [session, status]);

  useEffect(() => {
    // Check if we're on the callback URL
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    if (code) {
      setIsSchwabCallback(true);
    }

    // Always check Schwab login status on page load
    checkSchwabLogin();
  }, []);

  const checkSchwabLogin = async () => {
    try {
      // Chiedi lo stato Schwab al backend (cookie httpOnly)
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      if (data.loggedIn) {
        localStorage.setItem('schwabLoggedIn', 'true');
      } else {
        localStorage.removeItem('schwabLoggedIn');
      }
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
    }
  };

  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        // Set a flag to indicate we're attempting to log in
        localStorage.setItem('schwabLoginAttempt', 'true');
        window.location.href = await getAuthorizationCodeURL();
        return;
      }

      // If we have a code, we're logged in
      localStorage.setItem('schwabLoggedIn', 'true');
      setIsLoggedInToSchwab(true);
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  const handleLogOutFromSchwab = () => {
    try {
      // Clear Schwab-related cookies
      document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;
      document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;

      // Clear localStorage flags
      localStorage.removeItem('schwabLoggedIn');
      localStorage.removeItem('schwabLoginAttempt');

      // Update state
      setIsLoggedInToSchwab(false);

      // Refresh the page to ensure all state is reset
      window.location.reload();
    } catch (error) {
      console.error("Error logging out from Schwab:", error);
    }
  };

  const handleLogout = async () => {
    localStorage.clear();
    sessionStorage.clear();
    await logoutUser({ callbackUrl: "/generalLogin" });
    // window.location.href = "/generalLogin";
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: "Account Summary",
      description: "View your account summary and balances",
      icon: "📊",
      href: "/accountsSummary",
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600"
    },
    {
      title: "WB Dashboard",
      description: "Manage your WB trading pairs",
      icon: "📈",
      href: "/Strategies/WB/dashboard",
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600"
    },
    {
      title: "WB Configuration",
      description: "Configure your WB trading strategy",
      icon: "⚙️",
      href: "/Strategies/WB/configuration",
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600"
    },
    {
      title: "Saved Pairs",
      description: "View your saved trading pairs",
      icon: "🔖",
      href: "/savedPairs",
      color: "bg-amber-500",
      hoverColor: "hover:bg-amber-600"
    },
    {
      title: "File Handler",
      description: "Manage your data files",
      icon: "📁",
      href: "/fileHandler",
      color: "bg-indigo-500",
      hoverColor: "hover:bg-indigo-600"
    },
    {
      title: "Account Activity",
      description: "View your account activity",
      icon: "📝",
      href: "/testingAccountActivity",
      color: "bg-rose-500",
      hoverColor: "hover:bg-rose-600"
    }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero section - Reduced height */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0 md:mr-8">
                <h1 className="text-3xl font-extrabold tracking-tight mb-2">
                  {isSchwabCallback ? "Welcome Back!" : "Trading Dashboard"}
                </h1>
                <p className="text-base max-w-2xl">
                  {isSchwabCallback
                    ? "You've successfully connected with Schwab. Start managing your investments now."
                    : "Your all-in-one platform for managing investments and trading strategies."}
                </p>
                {session && !isLoggedInToSchwab && (
                  <button
                    className="mt-3 px-4 py-2 bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 font-medium rounded-lg shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition duration-300 flex items-center text-sm"
                    onClick={() => handleLogInToSchwab()}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V3zm1 0v12h12V3H4z" clipRule="evenodd" />
                      <path fillRule="evenodd" d="M6 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                    </svg>
                    Connect with Schwab
                  </button>
                )}
              </div>
              <div className="flex-shrink-0">
                {/* Smaller logo/illustration */}
                <div className="w-32 h-32 bg-white dark:bg-gray-800 bg-opacity-20 dark:bg-opacity-20 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* User welcome section - More compact */}
        {session && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="bg-white dark:bg-gray-800 shadow-sm dark:shadow-gray-900 rounded-lg p-4 flex justify-between items-center">
              <div className="flex items-center">
                <div className="bg-blue-100 dark:bg-blue-900 rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-base font-medium text-gray-900 dark:text-gray-100">Welcome, {session.user?.name || session.user?.email}</h2>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{isLoggedInToSchwab ? "Connected to Schwab" : "Not connected to Schwab"}</p>
                </div>
              </div>
              <button
                onClick={() => handleLogout()}
                className="px-3 py-1.5 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-md hover:bg-red-200 dark:hover:bg-red-900/50 transition duration-300 flex items-center text-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 12.586V8z" clipRule="evenodd" />
                </svg>
                Logout
              </button>
            </div>
          </div>
        )}

        {/* Status section - Moved above navigation */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-white dark:bg-gray-800 shadow-sm dark:shadow-gray-900 rounded-lg p-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
              <h2 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-0">System Status</h2>
              {isLoggedInToSchwab && (
                <button
                  onClick={handleLogOutFromSchwab}
                  className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300 flex items-center text-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 12.586V8z" clipRule="evenodd" />
                  </svg>
                  Disconnect from Schwab
                </button>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <div className="flex items-center">
                <div className={`w-2.5 h-2.5 rounded-full ${isLoggedInToSchwab ? 'bg-green-500' : 'bg-red-500'} mr-2`}></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">Schwab Connection: {isLoggedInToSchwab ? 'Active' : 'Inactive'}</span>
              </div>
              <div className="flex items-center">
                <div className="w-2.5 h-2.5 rounded-full bg-green-500 mr-2"></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">Dashboard Services: Online</span>
              </div>
              <div className="flex items-center">
                <div className="w-2.5 h-2.5 rounded-full bg-green-500 mr-2"></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">Data Processing: Active</span>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation cards - More compact */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Quick Navigation</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {navigationCards.map((card, index) => (
              <Link href={card.href} key={index}>
                <div className={`${card.color} ${card.hoverColor} text-white rounded-lg shadow-md dark:shadow-gray-900 p-4 transition duration-300 transform hover:scale-105 hover:shadow-lg cursor-pointer h-full flex flex-col`}>
                  <div className="text-3xl mb-2">{card.icon}</div>
                  <h3 className="text-lg font-bold mb-1">{card.title}</h3>
                  <p className="text-sm text-white text-opacity-90 flex-grow">{card.description}</p>
                  <div className="mt-2 flex justify-end" >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
  }
