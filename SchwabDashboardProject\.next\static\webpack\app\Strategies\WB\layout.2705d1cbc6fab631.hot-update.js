"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/layout",{

/***/ "(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js":
/*!***************************************************!*\
  !*** ./app/testingWebsocket/MarketDataContext.js ***!
  \***************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataProvider: () => (/* binding */ MarketDataProvider),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Strategies/WB/configuration/page */ \"(app-pages-browser)/./app/Strategies/WB/configuration/page.js\");\n/* __next_internal_client_entry_do_not_use__ MarketDataProvider,useMarketData auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst MarketDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction MarketDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [accountData, setAccountData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accountFilteredData, setAccountFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Accesso al sistema di cache per i dati di mercato\n    const { updateMarketData, getMarketData } = (0,_app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData)() || {};\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarketDataProvider.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"https://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ]\n            });\n            socket.on(\"connect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Connesso al server Socket.io\");\n                    handleNewToken();\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on('authenticated', {\n                \"MarketDataProvider.useEffect\": (response)=>{\n                    if (response.success) {\n                        console.log('Socket authenticated successfully');\n                    } else {\n                        console.error('Socket authentication failed:', response.error);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            async function handleNewToken() {\n                try {\n                    // Assicurati che il customerId e correlId siano salvati prima di usarli\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCustomerId)();\n                    await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.storeCorrelId)();\n                    const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                    const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                    const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                    if (!customerId || !correlId) {\n                        console.error(\"CustomerId o correlId non disponibili, devi prima richiederli!\");\n                        return;\n                    }\n                    // Send authentication data to WebSocket\n                    socket.emit('authenticate', {\n                        customerId: customerId,\n                        correlId: correlId,\n                        accessToken: accessToken\n                    });\n                    const res = await fetch(\"https://localhost:3001/init-schwab\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            token: accessToken,\n                            clientCorrelId: correlId,\n                            clientCustomerId: customerId\n                        }),\n                        credentials: \"include\"\n                    });\n                    await res.json();\n                } catch (err) {\n                    console.error(\"Errore nell'aggiornare il token:\", err);\n                }\n            }\n            socket.on(\"marketData\", {\n                \"MarketDataProvider.useEffect\": (data)=>{\n                    let parsedData;\n                    try {\n                        parsedData = JSON.parse(data);\n                    } catch (error) {\n                        console.error(\"Errore nel parsing dei dati:\", error);\n                        return;\n                    }\n                    setMarketData({\n                        \"MarketDataProvider.useEffect\": (prev)=>[\n                                ...prev,\n                                parsedData\n                            ]\n                    }[\"MarketDataProvider.useEffect\"]);\n                    if (parsedData.data && Array.isArray(parsedData.data)) {\n                        let newFilteredData = {}; // Oggetto temporaneo per salvare i dati\n                        parsedData.data.forEach({\n                            \"MarketDataProvider.useEffect\": (item)=>{\n                                if (item.content && Array.isArray(item.content)) {\n                                    item.content.forEach({\n                                        \"MarketDataProvider.useEffect\": (stock)=>{\n                                            const ticker = stock.key;\n                                            const value1 = stock[\"1\"];\n                                            const value2 = stock[\"2\"];\n                                            const value3 = stock[\"3\"];\n                                            const value5 = stock[\"18\"];\n                                            const value6 = stock[\"8\"];\n                                            const value7 = stock[\"22\"];\n                                            const value8 = stock[\"26\"];\n                                            if (value1 !== undefined && value2 !== undefined && value3 !== undefined) {\n                                                // Crea un nuovo oggetto per questo ticker con i valori principali\n                                                const tickerData = {\n                                                    bid_prc: value1,\n                                                    ask_prc: value2,\n                                                    last_prc: value3,\n                                                    timestamp: item.timestamp\n                                                };\n                                                // Aggiungi change e volume solo se sono definiti\n                                                if (value5 !== undefined) {\n                                                    tickerData.change = value5;\n                                                }\n                                                if (value6 !== undefined) {\n                                                    tickerData.volume = value6;\n                                                }\n                                                // Aggiungi dividend solo se è definito\n                                                if (value7 !== undefined) {\n                                                    tickerData.dividend = value7;\n                                                }\n                                                if (value8 !== undefined) {\n                                                    tickerData.ex_div_date = value8;\n                                                }\n                                                // Salva dividend e ex-date nel localStorage se sono definiti\n                                                if ((value7 !== undefined || value8 !== undefined) && updateMarketData) {\n                                                    const marketDataToSave = {};\n                                                    if (value7 !== undefined) marketDataToSave.dividend = value7;\n                                                    if (value8 !== undefined) marketDataToSave.ex_div_date = value8;\n                                                    updateMarketData(ticker, marketDataToSave);\n                                                    console.log(\"Saved market data for \".concat(ticker, \":\"), marketDataToSave);\n                                                }\n                                                // Salva l'oggetto completo\n                                                newFilteredData[ticker] = tickerData;\n                                            }\n                                        }\n                                    }[\"MarketDataProvider.useEffect\"]);\n                                }\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                        // Aggiorna lo stato preservando i valori precedenti che non sono stati aggiornati\n                        setFilteredData({\n                            \"MarketDataProvider.useEffect\": (prev)=>{\n                                const updatedData = {\n                                    ...prev\n                                };\n                                // Per ogni ticker nei nuovi dati\n                                Object.keys(newFilteredData).forEach({\n                                    \"MarketDataProvider.useEffect\": (ticker)=>{\n                                        // Recupera i dati salvati dal localStorage se disponibili\n                                        const savedMarketData = getMarketData ? getMarketData(ticker) : null;\n                                        // Se il ticker esiste già nello stato precedente\n                                        if (updatedData[ticker]) {\n                                            // Crea un nuovo oggetto per questo ticker\n                                            updatedData[ticker] = {\n                                                ...updatedData[ticker],\n                                                ...newFilteredData[ticker] // Sovrascrivi con i nuovi valori\n                                            };\n                                            // Se dividend è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].dividend === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.dividend) !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                } else if (updatedData[ticker].dividend !== undefined) {\n                                                    updatedData[ticker].dividend = prev[ticker].dividend;\n                                                }\n                                            }\n                                            // Se ex_div_date è undefined nei nuovi dati, prova a recuperarlo dal localStorage o dai dati precedenti\n                                            if (newFilteredData[ticker].ex_div_date === undefined) {\n                                                if ((savedMarketData === null || savedMarketData === void 0 ? void 0 : savedMarketData.ex_div_date) !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                } else if (updatedData[ticker].ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = prev[ticker].ex_div_date;\n                                                }\n                                            }\n                                        } else {\n                                            // Se è un nuovo ticker, aggiungi i nuovi dati e integra con quelli salvati\n                                            updatedData[ticker] = {\n                                                ...newFilteredData[ticker]\n                                            };\n                                            // Aggiungi i dati salvati se non sono presenti nei nuovi dati\n                                            if (savedMarketData) {\n                                                if (updatedData[ticker].dividend === undefined && savedMarketData.dividend !== undefined) {\n                                                    updatedData[ticker].dividend = savedMarketData.dividend;\n                                                }\n                                                if (updatedData[ticker].ex_div_date === undefined && savedMarketData.ex_div_date !== undefined) {\n                                                    updatedData[ticker].ex_div_date = savedMarketData.ex_div_date;\n                                                }\n                                            }\n                                        }\n                                    }\n                                }[\"MarketDataProvider.useEffect\"]);\n                                return updatedData;\n                            }\n                        }[\"MarketDataProvider.useEffect\"]);\n                    }\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"MarketDataProvider.useEffect\": ()=>{\n                    console.log(\"Disconnesso dal server Socket.io\");\n                }\n            }[\"MarketDataProvider.useEffect\"]);\n            return ({\n                \"MarketDataProvider.useEffect\": ()=>{\n                    socket.disconnect();\n                    console.log(\"Socket disconnesso (cleanup del MarketDataProvider)\");\n                }\n            })[\"MarketDataProvider.useEffect\"];\n        }\n    }[\"MarketDataProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MarketDataContext.Provider, {\n        value: {\n            marketData,\n            filteredData\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\testingWebsocket\\\\MarketDataContext.js\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketDataProvider, \"1guidLdNFU8u7NOo5fKj5tpL9h4=\", false, function() {\n    return [\n        _app_Strategies_WB_configuration_page__WEBPACK_IMPORTED_MODULE_4__.useExcelData\n    ];\n});\n_c = MarketDataProvider;\nfunction useMarketData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MarketDataContext);\n}\n_s1(useMarketData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"MarketDataProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/testingWebsocket/MarketDataContext.js\n"));

/***/ })

});