/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/oauth/schwab/page"],{

/***/ "(app-pages-browser)/./actions/schwabAccess.js":
/*!*********************************!*\
  !*** ./actions/schwabAccess.js ***!
  \*********************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getAccounts: () => (/* binding */ getAccounts),\n/* harmony export */   getAuthorizationCodeCallbackHandler: () => (/* binding */ getAuthorizationCodeCallbackHandler),\n/* harmony export */   getAuthorizationCodeURL: () => (/* binding */ getAuthorizationCodeURL),\n/* harmony export */   getMarketData: () => (/* binding */ getMarketData),\n/* harmony export */   makeApiCall: () => (/* binding */ makeApiCall),\n/* harmony export */   refreshToken: () => (/* binding */ refreshToken),\n/* harmony export */   retrieveAccessToken: () => (/* binding */ retrieveAccessToken),\n/* harmony export */   retrieveAuthorizationCode: () => (/* binding */ retrieveAuthorizationCode),\n/* harmony export */   retrieveCorrelId: () => (/* binding */ retrieveCorrelId),\n/* harmony export */   retrieveCustomerId: () => (/* binding */ retrieveCustomerId),\n/* harmony export */   retrieveRefreshToken: () => (/* binding */ retrieveRefreshToken),\n/* harmony export */   storeAccessToken: () => (/* binding */ storeAccessToken),\n/* harmony export */   storeAccessTokenFirstLogin: () => (/* binding */ storeAccessTokenFirstLogin),\n/* harmony export */   storeAuthorizationCode: () => (/* binding */ storeAuthorizationCode),\n/* harmony export */   storeCorrelId: () => (/* binding */ storeCorrelId),\n/* harmony export */   storeCustomerId: () => (/* binding */ storeCustomerId),\n/* harmony export */   storeRefreshToken: () => (/* binding */ storeRefreshToken)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00123b7f590fca2fea846ce8bb090c8b4135e9de5d\":\"getAccessToken\",\"00610d6e30a540305699b3726edfa456794a935d4e\":\"retrieveAuthorizationCode\",\"00972f29e10d6cd18dd1265b60067912bb6a7a1c2b\":\"getAuthorizationCodeURL\",\"00a9e585e1dc35eb977066b761d49f1a23fdc16c93\":\"storeCustomerId\",\"00bf6df4de4448c4552c6f62dbf4ba66009dcad250\":\"storeCorrelId\",\"00eda339ddc9f1e862059f7d997de8e23129bed7ef\":\"refreshToken\",\"4019d9b6d929f38fb47a1d4a69423a6d858847b0f3\":\"storeRefreshToken\",\"404f3e3ad78ac2de2901424a14b5c7fe022ce0b303\":\"storeAccessTokenFirstLogin\",\"4075df18647696b0765b2b5d66cadca4e16796a875\":\"retrieveRefreshToken\",\"409be9f1ad10c10b018021cd0e5c43e04f6a52ec55\":\"storeAccessToken\",\"40c21480db71c85b8af6ed1b0041f5c14845668e80\":\"retrieveAccessToken\",\"40d7abcf2a47f382acdd16645f2339ee46da14ba01\":\"retrieveCorrelId\",\"40f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb\":\"retrieveCustomerId\",\"40fb93412cb8910c389b9bef1a01cf17e6bb15379b\":\"storeAuthorizationCode\",\"60325ece34f85a6ed2aff537378b9ed92812b4c97b\":\"getAuthorizationCodeCallbackHandler\",\"78feca9946baeb980c93461938e15da67fb1f1530b\":\"makeApiCall\",\"7f276eddd716d85ac6647acaf92f150beea2a1298d\":\"getMarketData\",\"7f8d47bfa3f1bd78bb4909dc7270562779ecb727b3\":\"getAccounts\"} */ \nvar getAuthorizationCodeURL = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00972f29e10d6cd18dd1265b60067912bb6a7a1c2b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAuthorizationCodeURL\");\nvar getAuthorizationCodeCallbackHandler = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60325ece34f85a6ed2aff537378b9ed92812b4c97b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAuthorizationCodeCallbackHandler\");\nvar getAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00123b7f590fca2fea846ce8bb090c8b4135e9de5d\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAccessToken\");\nvar refreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00eda339ddc9f1e862059f7d997de8e23129bed7ef\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"refreshToken\");\nvar storeAuthorizationCode = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40fb93412cb8910c389b9bef1a01cf17e6bb15379b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAuthorizationCode\");\nvar retrieveAuthorizationCode = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00610d6e30a540305699b3726edfa456794a935d4e\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveAuthorizationCode\");\nvar storeAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"409be9f1ad10c10b018021cd0e5c43e04f6a52ec55\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAccessToken\");\nvar storeAccessTokenFirstLogin = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"404f3e3ad78ac2de2901424a14b5c7fe022ce0b303\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAccessTokenFirstLogin\");\nvar retrieveAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40c21480db71c85b8af6ed1b0041f5c14845668e80\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveAccessToken\");\nvar storeRefreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4019d9b6d929f38fb47a1d4a69423a6d858847b0f3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeRefreshToken\");\nvar retrieveRefreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4075df18647696b0765b2b5d66cadca4e16796a875\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveRefreshToken\");\nvar storeCorrelId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00bf6df4de4448c4552c6f62dbf4ba66009dcad250\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeCorrelId\");\nvar retrieveCorrelId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40d7abcf2a47f382acdd16645f2339ee46da14ba01\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveCorrelId\");\nvar storeCustomerId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00a9e585e1dc35eb977066b761d49f1a23fdc16c93\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeCustomerId\");\nvar retrieveCustomerId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40f8a5fe5cee4c4b6ec4027d0d7bfde1ce627521fb\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveCustomerId\");\nvar makeApiCall = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"78feca9946baeb980c93461938e15da67fb1f1530b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"makeApiCall\");\nvar getAccounts = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f8d47bfa3f1bd78bb4909dc7270562779ecb727b3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAccounts\");\nvar getMarketData = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f276eddd716d85ac6647acaf92f150beea2a1298d\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getMarketData\");\n // export {\n //   getAccounts,\n //   getMarketData,\n //   getAuthorizationCodeURL,\n //   getAuthorizationCodeCallbackHandler,\n //   getAccessToken,\n //   storeAuthorizationCode,\n //   retrieveAuthorizationCode,\n //   storeAccessToken,\n //   retrieveAccessToken,\n //   storeRefreshToken,\n //   retrieveRefreshToken,\n //   refreshToken,\n // };\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./actions/schwabAccess.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/oauth/schwab/page.js":
/*!**********************************!*\
  !*** ./app/oauth/schwab/page.js ***!
  \**********************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchwabCallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SchwabCallback() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasProcessed, setHasProcessed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchwabCallback.useEffect\": ()=>{\n            // Only run this effect once and prevent reprocessing\n            if (isProcessing || hasProcessed) return;\n            async function processCallback() {\n                setIsProcessing(true);\n                try {\n                    console.log(\"OAuth callback page loaded\");\n                    // Check if we have a code in the URL\n                    const url = new URL(window.location.href);\n                    const code = url.searchParams.get(\"code\");\n                    if (!code) {\n                        console.error(\"Missing code parameter\");\n                        setError(\"Missing authorization code\");\n                        setHasProcessed(true);\n                        return;\n                    }\n                    console.log(\"Processing OAuth callback with code\");\n                    // For now, let's bypass the state parameter check since we're having issues with it\n                    // We'll just use the code to get the access token\n                    const result = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeCallbackHandler)(code, \"bypass_state_check\");\n                    if (result.success) {\n                        console.log(\"OAuth callback processed successfully\");\n                        setSuccess(true);\n                        // Redirect to home page after a short delay\n                        setTimeout({\n                            \"SchwabCallback.useEffect.processCallback\": ()=>{\n                                router.push(\"/\");\n                            }\n                        }[\"SchwabCallback.useEffect.processCallback\"], 2000);\n                    } else {\n                        console.error(\"OAuth callback processing failed:\", result.error);\n                        setError(result.error || \"Failed to connect to Schwab\");\n                    }\n                } catch (error) {\n                    console.error(\"Error in OAuth callback processing:\", error);\n                    setError(\"An error occurred while processing the callback\");\n                } finally{\n                    setIsProcessing(false);\n                    setHasProcessed(true);\n                }\n            }\n            // Execute the callback processing\n            processCallback();\n        }\n    }[\"SchwabCallback.useEffect\"], [\n        isProcessing,\n        router,\n        hasProcessed\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-center mb-6 text-gray-900 dark:text-gray-100\",\n                    children: success ? \"Connected to Schwab\" : \"Connecting to Schwab\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/\"),\n                                className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800\",\n                                children: \"Return to Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this) : success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium\",\n                            children: \"Success!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Your Schwab account has been connected successfully.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2\",\n                            children: \"Redirecting to Home page...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 text-center\",\n                            children: \"Please wait while we connect your Schwab account...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(SchwabCallback, \"m4zci8YO66dlkfz2tyujZMPPq3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SchwabCallback;\nvar _c;\n$RefreshReg$(_c, \"SchwabCallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/oauth/schwab/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFbGxlblxcT25lRHJpdmVcXERlc2t0b3BcXERhc2hib2FyZFxcU2Nod2FiRGFzaGJvYXJkUHJvamVjdFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/oauth/schwab/page.js */ \"(app-pages-browser)/./app/oauth/schwab/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRWxsZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEYXNoYm9hcmQlNUMlNUNTY2h3YWJEYXNoYm9hcmRQcm9qZWN0JTVDJTVDYXBwJTVDJTVDb2F1dGglNUMlNUNzY2h3YWIlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxFbGxlblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERhc2hib2FyZFxcXFxTY2h3YWJEYXNoYm9hcmRQcm9qZWN0XFxcXGFwcFxcXFxvYXV0aFxcXFxzY2h3YWJcXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    callServer: function() {\n        return _appcallserver.callServer;\n    },\n    createServerReference: function() {\n        return createServerReference;\n    },\n    findSourceMapURL: function() {\n        return _appfindsourcemapurl.findSourceMapURL;\n    }\n});\nconst _appcallserver = __webpack_require__(/*! next/dist/client/app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! next/dist/client/app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst createServerReference = ( false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\")).createServerReference;\n\n//# sourceMappingURL=action-client-wrapper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWxsZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxEYXNoYm9hcmRcXFNjaHdhYkRhc2hib2FyZFByb2plY3RcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);