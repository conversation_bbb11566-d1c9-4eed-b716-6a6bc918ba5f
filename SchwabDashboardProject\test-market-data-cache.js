// Test script per verificare il sistema di cache dei dati di mercato
// Esegui questo script nella console del browser per testare il sistema

console.log("🧪 Testing Market Data Cache System...");

// Simula dati di test
const testData = {
  'AAPL': { dividend: 0.25, ex_div_date: '2024-02-09' },
  'MSFT': { dividend: 0.75, ex_div_date: '2024-02-15' },
  'GOOGL': { dividend: 0.0, ex_div_date: '2024-03-01' }
};

// Test 1: Verifica che localStorage sia vuoto inizialmente
console.log("📋 Test 1: Checking initial localStorage state...");
const initialData = localStorage.getItem('schwabMarketDataCache');
console.log("Initial data:", initialData ? JSON.parse(initialData) : "Empty");

// Test 2: Simula il salvataggio di dati
console.log("\n💾 Test 2: Simulating data save...");
localStorage.setItem('schwabMarketDataCache', JSON.stringify(testData));
console.log("Test data saved:", testData);

// Test 3: Verifica il recupero dei dati
console.log("\n📖 Test 3: Verifying data retrieval...");
const retrievedData = localStorage.getItem('schwabMarketDataCache');
const parsedData = retrievedData ? JSON.parse(retrievedData) : null;
console.log("Retrieved data:", parsedData);

// Test 4: Verifica che i dati siano corretti
console.log("\n✅ Test 4: Data validation...");
let testsPassed = 0;
let totalTests = 0;

Object.keys(testData).forEach(ticker => {
  totalTests++;
  if (parsedData && parsedData[ticker] && 
      parsedData[ticker].dividend === testData[ticker].dividend &&
      parsedData[ticker].ex_div_date === testData[ticker].ex_div_date) {
    console.log(`✅ ${ticker}: PASS`);
    testsPassed++;
  } else {
    console.log(`❌ ${ticker}: FAIL`);
  }
});

console.log(`\n📊 Test Results: ${testsPassed}/${totalTests} tests passed`);

// Test 5: Simula aggiornamento di un singolo ticker
console.log("\n🔄 Test 5: Simulating single ticker update...");
const currentData = JSON.parse(localStorage.getItem('schwabMarketDataCache') || '{}');
currentData['AAPL'] = {
  ...currentData['AAPL'],
  dividend: 0.30, // Nuovo valore
  lastUpdated: new Date().toISOString()
};
localStorage.setItem('schwabMarketDataCache', JSON.stringify(currentData));
console.log("Updated AAPL data:", currentData['AAPL']);

// Test 6: Verifica la struttura dei dati
console.log("\n🏗️ Test 6: Data structure validation...");
const finalData = JSON.parse(localStorage.getItem('schwabMarketDataCache'));
const hasValidStructure = Object.keys(finalData).every(ticker => {
  const data = finalData[ticker];
  return typeof data === 'object' && 
         (data.dividend !== undefined || data.ex_div_date !== undefined);
});

console.log("Data structure valid:", hasValidStructure ? "✅ YES" : "❌ NO");

// Test 7: Pulizia
console.log("\n🧹 Test 7: Cleanup test...");
localStorage.removeItem('schwabMarketDataCache');
const cleanedData = localStorage.getItem('schwabMarketDataCache');
console.log("Data after cleanup:", cleanedData ? "❌ Still exists" : "✅ Cleaned");

console.log("\n🎉 Market Data Cache System Test Complete!");
console.log("📝 To test in the actual app:");
console.log("1. Go to WB Dashboard");
console.log("2. Connect to Schwab and wait for market data");
console.log("3. Look for the blue 'Market Data Cache' panel");
console.log("4. Refresh the page and check if data persists");
