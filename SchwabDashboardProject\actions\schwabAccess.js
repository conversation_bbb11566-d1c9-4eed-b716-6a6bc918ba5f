"use server";

import axios from "axios";
import { cookies } from "next/headers";
import { getUserPreference } from "./schwabTraderAPIactions";


const clientId = process.env.NEXT_PUBLIC_SCHWAB_APP_KEY;
const clientSecret = process.env.NEXT_PUBLIC_SCHWAB_SECRET;
const callbackUrl = process.env.NEXT_PUBLIC_SCHWAB_CALLBACKURL;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

export async function getAuthorizationCodeURL() {
  try {
    // Generate a random state parameter for CSRF protection
    // We'll skip this for now since we're having issues with it
    // const state = crypto.randomBytes(32).toString('hex');

    // // Store the state in a cookie for verification when the user returns
    // const cookiesObj = await cookies();
    // cookiesObj.set("schwab_oauth_state", state, {
    //   httpOnly: true,
    //   secure: process.env.NODE_ENV === "production",
    //   sameSite: "lax",
    //   maxAge: 60 * 10, // 10 minutes
    //   path: "/",
    // });

    // Construct the authorization URL without the state parameter for now
    const authorizationCodeUrl = `https://api.schwabapi.com/v1/oauth/authorize?client_id=${clientId}&redirect_uri=${callbackUrl}`;

    console.log("Generated authorization URL");
    return authorizationCodeUrl;
  } catch (error) {
    console.error("Error generating authorization URL:", error);
    throw error;
  }
}

export async function getAuthorizationCodeCallbackHandler(code, state) {
  try {
    console.log("Processing OAuth callback with code");

    if (!code) {
      console.error("No authorization code provided");
      return { success: false, error: "No authorization code provided" };
    }

    // Only verify state if it's not the bypass value
    if (state !== "bypass_state_check") {
      if (!state) {
        console.error("No state parameter provided");
        return { success: false, error: "No state parameter provided" };
      }

      // Verify the state parameter to prevent CSRF attacks
      const cookiesObj = await cookies();
      const storedState = cookiesObj.get("schwab_oauth_state")?.value;

      if (!storedState || state !== storedState) {
        console.error("Invalid state parameter");
        return { success: false, error: "Invalid state parameter" };
      }

      // Clear the state cookie
      cookiesObj.delete("schwab_oauth_state");
    } else {
      console.log("Bypassing state parameter check");
    }

    // Store the authorization code for later use
    const storeResult = await storeAuthorizationCode(code);

    if (!storeResult) {
      console.error("Failed to store authorization code");
      return { success: false, error: "Failed to store authorization code" };
    }

    console.log("Authorization code stored successfully");

    // Get the access token
    const tokenResult = await getAccessToken();

    if (!tokenResult) {
      console.error("Failed to get access token");
      return { success: false, error: "Failed to get access token" };
    }

    console.log("Access token retrieved successfully");

    return {
      success: true,
      message: "Authorization successful",
      hasAccessToken: !!tokenResult.access_token,
      hasRefreshToken: !!tokenResult.refresh_token
    };
  } catch (error) {
    console.error("Error in authorization code callback:", error);
    return { success: false, error: error.message || "Unknown error" };
  }
}

export async function getAccessToken() {
  try {
    console.log("Getting access token...");
    const authorizationCode = await retrieveAuthorizationCode();

    if (!authorizationCode) {
      console.error("No authorization code found in cookies");
      // Try to refresh the token instead of throwing an error
      const refreshedToken = await refreshToken();
      if (refreshedToken) {
        console.log("Successfully refreshed token instead of getting a new one");
        return refreshedToken;
      }
      // If we can't refresh, return null instead of throwing
      console.log("Could not refresh token either, returning null");
      return null;
    }

    // Check for client credentials
    if (!clientId || !clientSecret) {
      console.error("Missing client credentials");
      return null;
    }

    // Create the authorization header
    const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");

    // Ensure the code is properly encoded
    const encodedCode = encodeURIComponent(authorizationCode);
    const encodedCallbackUrl = encodeURIComponent(callbackUrl);

    // Set up the request
    const accessTokenUrl = "https://api.schwabapi.com/v1/oauth/token";
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": `Basic ${authHeader}`
    };

    // Create the request body
    const body = `grant_type=authorization_code&code=${encodedCode}&redirect_uri=${encodedCallbackUrl}`;

    console.log("Making token request to:", accessTokenUrl);
    console.log("Using authorization code:", authorizationCode.substring(0, 10) + "...");

    // Make the request
    const response = await axios.post(accessTokenUrl, body, { headers });

    // Process the response
    if (response.status === 200) {
      console.log("Token response status:", response.status);

      const accessTokenResponse = response.data;
      console.log("Access token retrieved successfully");

      // Store the tokens
      if (accessTokenResponse.access_token) {
        await storeAccessToken(accessTokenResponse);
        console.log("Access token stored successfully");
      }

      if (accessTokenResponse.refresh_token) {
        await storeRefreshToken(accessTokenResponse.refresh_token);
        console.log("Refresh token stored successfully");
      }

      return accessTokenResponse;
    } else {
      console.error("Unexpected response status:", response.status);
      return null;
    }
  } catch (error) {
    console.error("Error in getAccessToken:", error.response?.status);
    console.error("Error details:", error.response?.data);
    // Return null instead of throwing
    return null;
  }
}

// async function getAuthorizationCode() {
// console.log("Received request with method:", req.method);
// if (req.method === "GET") {
//   const authorizationCode = req.query.code;
//   console.log("Received authorization code: " + authorizationCode);
//   if (authorizationCode) {
//     await storeAuthorizationCode(authorizationCode);
//     res.redirect("/dashboard");
//   } else {
//     res.status(400).send("No authorization code provided");
//   }
// } else {
//   res.status(405).send("Method not allowed");
// }
// }

// async function login() {
//   try {
//     const accessToken = await getAccessToken();
//     // Store the access token for later use
//     await storeAccessToken(accessTokenResponse.access_token); //valid for 30 minutes
//     await storeRefreshToken(accessTokenResponse.refresh_token); //valid for 7 days
//     return { accessToken };
//   } catch (error) {
//     console.error(error);
//     throw error;
//   }
// }

export async function refreshToken() {
  try {
    console.log("Refreshing access token...");

    const refreshToken = await retrieveRefreshToken(true);
    if (!refreshToken) {
      console.log("No refresh token found. User might need to log in again.");
      return null;
    }

    const accessTokenUrl = "https://api.schwabapi.com/v1/oauth/token";

    // Create the Authorization header with Basic Auth
    const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": `Basic ${authHeader}`, // Basic Authentication
    };

    const body = `grant_type=refresh_token&refresh_token=${refreshToken}`;

    const response = await fetch(accessTokenUrl, {
      method: "POST",
      headers,
      body,
    });

    if (!response.ok) {
      const errorResponse = await response.json();
      console.error(`Error refreshing token: ${response.status} - ${errorResponse.error_description || JSON.stringify(errorResponse)}`);
      return null;
    }

    const newAccessTokenResponse = await response.json();
    console.log("New access token retrieved:", newAccessTokenResponse.access_token);

    // Store the new access token
    await storeAccessToken(newAccessTokenResponse);

    // Store the new refresh token (only if present)
    if (newAccessTokenResponse.refresh_token) {
      await storeRefreshToken(newAccessTokenResponse.refresh_token);
    }

    return newAccessTokenResponse;
  } catch (error) {
    console.error("Error in refreshToken:", error.message);
    return null;
  }
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Helper functions to store and retrieve authorization code and access token
export async function storeAuthorizationCode(code) {
  try {
    if (!code) {
      console.error("No authorization code provided");
      return false;
    }

    // Clean the code if it contains URL-encoded characters
    const cleanCode = code.replace(/%40/g, '@');

    console.log(`Storing authorization code: ${cleanCode.substring(0, 10)}...`);

    const cookiesObj = await cookies();

    // Set the cookie with a longer expiration (2 hours) to ensure we have time to use it
    cookiesObj.set("authorization_code", cleanCode, {
      maxAge: 7200,  // 2 hours in seconds
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });

    // Verify the code was stored
    const storedCode = cookiesObj.get("authorization_code")?.value;

    if (storedCode) {
      console.log(`Authorization code stored successfully: ${storedCode.substring(0, 10)}...`);
      return true;
    } else {
      console.error("Failed to store authorization code in cookies");
      return false;
    }
  } catch (error) {
    console.error("Error storing authorization code:", error);
    return false;
  }
}

export async function retrieveAuthorizationCode() {
  try {
    const cookiesObj = await cookies();
    const authcode = cookiesObj.get("authorization_code")?.value;

    if (!authcode) {
      console.log("No authorization code found in cookies");
      return null;
    }

    console.log(`Retrieved authorization code from cookies: ${authcode.substring(0, 10)}...`);
    return authcode;
  } catch (error) {
    console.error("Error in retrieveAuthorizationCode:", error);
    return null;
  }
}

export async function storeAccessToken(token) {
  try {
    if (!token || !token.access_token) {
      console.error("No access token provided or invalid token format");
      return false;
    }

    console.log("Storing access token:", token.access_token.substring(0, 10) + "...");
    const cookiesObj = await cookies();
    cookiesObj.set("access_token", token.access_token, {
      maxAge: 1800,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });
    return true;
  } catch (error) {
    console.error("Error storing access token:", error);
    return false;
  }
}
///////////////////////////////////////////////////////////////////////////////////////
//this is a duplicate code, until we find the solution for the accessToken problem in the AccessToken function
export async function storeAccessTokenFirstLogin(token) {
  try {
    if (!token) {
      console.error("No access token provided");
      return false;
    }

    const cookiesObj = await cookies();
    cookiesObj.set("access_token", token, {
      maxAge: 1800,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });
    return true;
  } catch (error) {
    console.error("Error storing first login access token:", error);
    return false;
  }
}
///////////////////////////////////////////////////////////////////////////////////////

export async function retrieveAccessToken(skipRefresh = false) {
  try {
    const cookiesObj = await cookies();
    let accessToken = cookiesObj.get("access_token")?.value;

    // If we have an access token or we're skipping refresh, return it (or null)
    if (accessToken || skipRefresh) {
      return accessToken || null;
    }

    // Only attempt to refresh if we don't have a token and we're not skipping refresh
    console.log("No access token found, attempting to refresh");
    try {
      // Access token is not found, try to refresh
      const refreshResult = await refreshToken();
      if (refreshResult) {
        // Try to get the access token again after refresh
        const newCookiesObj = await cookies();
        accessToken = newCookiesObj.get("access_token")?.value;

        if (accessToken) {
          console.log("Successfully refreshed and retrieved access token");
        } else {
          console.log("Refresh succeeded but no access token found");
        }
      } else {
        console.log("Token refresh failed");
      }
    } catch (error) {
      console.error("Error refreshing token:", error.message);
    }

    return accessToken || null;
  } catch (error) {
    console.error("Error in retrieveAccessToken:", error);
    return null;
  }
}

export async function storeRefreshToken(token) {
  try {
    if (!token) {
      console.error("No refresh token provided");
      return false;
    }

    const cookiesObj = await cookies();
    cookiesObj.set("refresh_token", token, {
      maxAge: 604800,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });
    return true;
  } catch (error) {
    console.error("Error storing refresh token:", error);
    return false;
  }
}

export async function retrieveRefreshToken(suppressLog = false) {
  try {
    const cookiesObj = await cookies();
    const refreshTokenValue = cookiesObj.get("refresh_token")?.value;
    if (!refreshTokenValue && !suppressLog) {
      console.log("No refresh token found");
    }
    return refreshTokenValue || null;
  } catch (error) {
    console.error("Error in retrieveRefreshToken:", error);
    return null;
  }
}

export async function storeCorrelId() {
  try {
    const userPreference = await getUserPreference();
    const schwabClientCorrelId = userPreference.streamerInfo?.[0]?.schwabClientCorrelId;

    if (!schwabClientCorrelId) {
      console.error("No correlation ID found in user preferences");
      return false;
    }

    const cookiesObj = await cookies();
    cookiesObj.set("client_correlId", schwabClientCorrelId, {
      maxAge: 86400,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });
    return true;
  } catch (error) {
    console.error("Error storing correlation ID:", error);
    return false;
  }
}

export async function retrieveCorrelId(suppressLog = false) {
  try {
    const cookiesObj = await cookies();
    const correlId = cookiesObj.get("client_correlId")?.value;
    if (!correlId && !suppressLog) {
      console.log("No client correlId found");
    }
    return correlId || null;
  } catch (error) {
    console.error("Error in retrieveCorrelId:", error);
    return null;
  }
}

export async function storeCustomerId() {
  try {
    const userPreference = await getUserPreference();
    const schwabClientCustomerId = userPreference.streamerInfo?.[0]?.schwabClientCustomerId;

    if (!schwabClientCustomerId) {
      console.error("No customer ID found in user preferences");
      return false;
    }

    const cookiesObj = await cookies();
    cookiesObj.set("client_customerId", schwabClientCustomerId, {
      maxAge: 86400,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'strict'
    });
    return true;
  } catch (error) {
    console.error("Error storing customer ID:", error);
    return false;
  }
}

export async function retrieveCustomerId(suppressLog = false) {
  try {
    const cookiesObj = await cookies();
    const customerId = cookiesObj.get("client_customerId")?.value;
    if (!customerId && !suppressLog) {
      console.log("No client customerId found");
    }
    return customerId || null;
  } catch (error) {
    console.error("Error in retrieveCustomerId:", error);
    return null;
  }
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//EXAMPLE API CALL WITH TOKEN - Generic helper function
export async function makeApiCall(endpoint, method = "GET", body = null, skipRefresh = false) {
  try {
    const accessToken = await retrieveAccessToken(skipRefresh); // Retrieve the stored access token

    if (!accessToken) {
      console.log("No access token available for API call");
      return { error: "Authentication required" };
    }

    const headers = {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    };

    const options = {
      method,
      headers
    };

    if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(endpoint, options);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API call failed: ${response.status} ${response.statusText}`, errorText);
      return {
        error: "API call failed",
        status: response.status,
        statusText: response.statusText,
        details: errorText
      };
    }

    return await response.json();
  } catch (error) {
    console.error("Error in makeApiCall:", error);
    return { error: error.message || "Unknown error" };
  }
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

export const getAccounts = async (skipRefresh = false) => {
  try {
    let accessToken = await retrieveAccessToken(skipRefresh);
    console.log("Using access token for accounts API call:", accessToken ? "Yes" : "No");

    if (!accessToken) {
      return {
        error: "Not connected to Schwab",
        message: "Please connect your Schwab account first",
        needsConnection: true
      };
    }

    const response = await axios.get(
      "https://api.schwabapi.com/trader/v1/accounts",
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching accounts:", error);

    // Check if it's an authentication error
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      return {
        error: "Authentication failed",
        message: "Your Schwab connection has expired. Please reconnect.",
        needsConnection: true
      };
    }

    return {
      error: "Failed to fetch accounts",
      message: error.message || "An unknown error occurred",
      needsConnection: false
    };
  }
};

export const getMarketData = async (symbol, skipRefresh = false) => {
  try {
    let accessToken = await retrieveAccessToken(skipRefresh);
    console.log("Using access token for market data API call:", accessToken ? "Yes" : "No");

    if (!accessToken) {
      return {
        error: "Not connected to Schwab",
        message: "Please connect your Schwab account first",
        needsConnection: true
      };
    }

    const response = await axios.get(
      `https://api.schwabapi.com/v1/markets/data/${symbol}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching market data:", error);

    // Check if it's an authentication error
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      return {
        error: "Authentication failed",
        message: "Your Schwab connection has expired. Please reconnect.",
        needsConnection: true
      };
    }

    return {
      error: "Failed to fetch market data",
      message: error.message || "An unknown error occurred",
      needsConnection: false
    };
  }
};

// export {
//   getAccounts,
//   getMarketData,
//   getAuthorizationCodeURL,
//   getAuthorizationCodeCallbackHandler,
//   getAccessToken,
//   storeAuthorizationCode,
//   retrieveAuthorizationCode,
//   storeAccessToken,
//   retrieveAccessToken,
//   storeRefreshToken,
//   retrieveRefreshToken,
//   refreshToken,
// };
