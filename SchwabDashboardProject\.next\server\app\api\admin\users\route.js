/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/users/route";
exports.ids = ["app/api/admin/users/route"];
exports.modules = {

/***/ "(rsc)/./app/api/admin/users/route.js":
/*!**************************************!*\
  !*** ./app/api/admin/users/route.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./lib/auth-options.js\");\n/* harmony import */ var _lib_prisma_dal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma-dal */ \"(rsc)/./lib/prisma-dal.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"408d3d4bfe166baf17e2003eacd52b63a21edd7bc5\":\"GET\"} */ \n\n\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_5__.PrismaClient();\nasync function GET(req) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    if (!session || !session.user.role || session.user.role.toLowerCase() !== \"admin\") {\n        return new Response(JSON.stringify({\n            error: \"Unauthorized\"\n        }), {\n            status: 401\n        });\n    }\n    const users = await prisma.users.findMany({\n        select: {\n            id: true,\n            name: true,\n            email: true,\n            role: true\n        },\n        orderBy: {\n            email: \"asc\"\n        }\n    });\n    // Normalize all roles to lowercase for UI consistency, but keep original for DB updates\n    const usersWithRole = users.map((u)=>({\n            ...u,\n            role: (u.role || 'user').toLowerCase()\n        }));\n    return new Response(JSON.stringify({\n        users: usersWithRole\n    }), {\n        status: 200\n    });\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_6__.ensureServerEntryExports)([\n    GET\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(GET, \"408d3d4bfe166baf17e2003eacd52b63a21edd7bc5\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/users/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth-options.js":
/*!*****************************!*\
  !*** ./lib/auth-options.js ***!
  \*****************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _rateLimit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rateLimit.js */ \"(rsc)/./lib/rateLimit.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma-dal.js */ \"(rsc)/./lib/prisma-dal.js\");\n\n\n\n\nconst CredentialsProvider = next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"] || next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__;\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET,\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    providers: [\n        CredentialsProvider({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\",\n                    required: true\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    required: true\n                }\n            },\n            async authorize (credentials, req) {\n                // Rate limit by IP address (or fallback to email if no IP)\n                let ip = null;\n                if (req && req.headers) {\n                    ip = req.headers[\"x-forwarded-for\"]?.split(\",\")[0]?.trim() || req.headers[\"x-real-ip\"] || req.socket?.remoteAddress;\n                }\n                // fallback to email if no IP (not ideal, but better than nothing)\n                const rateLimitKey = ip || (credentials?.email ? `email:${credentials.email}` : \"unknown\");\n                if ((0,_rateLimit_js__WEBPACK_IMPORTED_MODULE_0__.rateLimit)(rateLimitKey, 5, 60 * 1000)) {\n                    throw new Error(\"Too many login attempts. Please try again in a minute.\");\n                }\n                try {\n                    // Check if credentials are provided\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"Email and password are required\");\n                    }\n                    // Find the user using Prisma\n                    const user = await (0,_prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__.verifyUserCredentials)(credentials.email);\n                    if (!user) {\n                        console.log(\"User not found\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"User found:\", user.email);\n                    // Verify password\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        console.log(\"Invalid password\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"Password verified successfully\");\n                    // Return the user object\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name || user.email.split('@')[0],\n                        role: user.role || 'user'\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw error;\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/generalLogin\",\n        signOut: \"/generalLogin\",\n        error: \"/generalLogin\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user data to the token when signing in\n            if (user) {\n                token.id = user.id;\n                // Explicitly check for null/undefined and set to 'user' if so\n                token.role = user.role === undefined || user.role === null ? 'user' : user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Reject session if token is missing or expired\n            if (!token || !token.id || token.exp && Date.now() / 1000 > token.exp) {\n                return null;\n            }\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role === undefined || token.role === null ? 'user' : token.role;\n            }\n            return session;\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-options.js\n");

/***/ }),

/***/ "(rsc)/./lib/prisma-dal.js":
/*!***************************!*\
  !*** ./lib/prisma-dal.js ***!
  \***************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExcelData: () => (/* binding */ getExcelData),\n/* harmony export */   getTradingPairs: () => (/* binding */ getTradingPairs),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   saveExcelData: () => (/* binding */ saveExcelData),\n/* harmony export */   saveTradingPairs: () => (/* binding */ saveTradingPairs),\n/* harmony export */   verifyUserCredentials: () => (/* binding */ verifyUserCredentials)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\r\n * Prisma Data Access Layer (DAL)\r\n *\r\n * This file provides a layer of abstraction for database operations using Prisma.\r\n * It replaces the direct MongoDB operations in lib/mongodb.js.\r\n */ \n// Create a new PrismaClient instance\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n/**\r\n * User-related operations\r\n */ // Get a user by email\nasync function getUserByEmail(email) {\n    try {\n        console.log('Getting user by email:', email);\n        // Log available models\n        const availableModels = Object.keys(prisma).filter((key)=>!key.startsWith('_') && !key.startsWith('$') && typeof prisma[key] === 'object');\n        console.log('Available models:', availableModels);\n        // Determine which model to use for users\n        let userModel;\n        if (availableModels.includes('users')) {\n            userModel = prisma.users;\n        } else {\n            console.error('No user model found in Prisma client');\n            return null;\n        }\n        // Use the determined model\n        const user = await userModel.findUnique({\n            where: {\n                email\n            }\n        });\n        console.log('User found:', user ? 'Yes' : 'No');\n        if (user) {\n            // With Prisma, the id is already a string\n            // Just make sure we have a consistent property name\n            user.id = user.id || user._id;\n            // Log the user object for debugging\n            console.log('User object:', {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            });\n        }\n        return user;\n    } catch (error) {\n        console.error('Error getting user by email:', error);\n        return null;\n    }\n}\n// Verify user credentials\nasync function verifyUserCredentials(email) {\n    try {\n        const user = await getUserByEmail(email);\n        return user;\n    } catch (error) {\n        console.error('Error verifying user credentials:', error);\n        return null;\n    }\n}\n/**\r\n * Trading Pair operations\r\n */ // Save trading pairs\nasync function saveTradingPairs(pairs, userId) {\n    try {\n        console.log('saveTradingPairs called with pairs:', pairs.length);\n        console.log('saveTradingPairs called with userId:', userId);\n        // Delete all existing pairs for this user\n        await prisma.tradingPair.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Log a sample pair for debugging\n        if (pairs.length > 0) {\n            console.log('Sample pair to save:', {\n                pairKey: pairs[0].pairKey,\n                status: pairs[0].status,\n                combinedPNL: pairs[0].combinedPNL\n            });\n        }\n        // Create all new pairs - we need to handle each pair individually\n        // since createMany doesn't work well with JSON fields in MongoDB\n        let insertedCount = 0;\n        for (const pair of pairs){\n            try {\n                // For pairKey, ensure it's a string or null\n                const pairKeyValue = pair.pairKey ? String(pair.pairKey) : null;\n                // Transform the shortComponent to match the expected schema\n                const shortComponent = {\n                    dividendUserValue: parseInt(pair.shortComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.shortComponent?.dollarCost || 0),\n                    expectedQuantity: pair.shortComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.shortComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.shortComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.shortComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.shortComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.shortComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.shortComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.shortComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.shortComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.shortComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.shortComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.shortComponent?.formattedVolume || \"0\",\n                    id: pair.shortComponent?.id || \"0\",\n                    pnl: parseInt(pair.shortComponent?.pnl || 0),\n                    sectorValue: pair.shortComponent?.sectorValue || \"\",\n                    spreadUserValue: pair.shortComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.shortComponent?.spreadValue || 0,\n                    statusValue: pair.shortComponent?.statusValue || \"\",\n                    ticker: pair.shortComponent?.ticker || \"\"\n                };\n                // Transform the longComponent to match the expected schema\n                const longComponent = {\n                    dividendUserValue: parseInt(pair.longComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.longComponent?.dollarCost || 0),\n                    // expectedQuantity is a Json type in the schema\n                    expectedQuantity: pair.longComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.longComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.longComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.longComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.longComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.longComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.longComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.longComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.longComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.longComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.longComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.longComponent?.formattedVolume || \"0\",\n                    id: pair.longComponent?.id || \"0\",\n                    pnl: parseInt(pair.longComponent?.pnl || 0),\n                    sectorValue: pair.longComponent?.sectorValue || \"\",\n                    // spreadUserValue is a Json type in the schema\n                    spreadUserValue: pair.longComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.longComponent?.spreadValue || 0,\n                    statusValue: pair.longComponent?.statusValue || \"\",\n                    ticker: pair.longComponent?.ticker || \"\"\n                };\n                await prisma.tradingPair.create({\n                    data: {\n                        pairKey: pairKeyValue,\n                        status: pair.status || \"\",\n                        shortComponent: shortComponent,\n                        longComponent: longComponent,\n                        combinedPNL: pair.combinedPNL || \"0\",\n                        userId,\n                        createdAt: now,\n                        updatedAt: now\n                    }\n                });\n                insertedCount++;\n            } catch (error) {\n                console.error(`Error inserting pair:`, error);\n            // Continue with the next pair\n            }\n        }\n        const createdPairs = {\n            count: insertedCount\n        };\n        return {\n            success: true,\n            insertedCount: createdPairs.count,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving trading pairs:', error);\n        throw error;\n    }\n}\n// Get trading pairs\nasync function getTradingPairs(userId, status = null) {\n    try {\n        const whereClause = {\n            userId\n        };\n        if (status) {\n            whereClause.status = status;\n        }\n        const pairs = await prisma.tradingPair.findMany({\n            where: whereClause,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return pairs;\n    } catch (error) {\n        console.error('Error getting trading pairs:', error);\n        return [];\n    }\n}\n/**\r\n * Excel Data operations\r\n */ // Save Excel data\nasync function saveExcelData(excelData, userId) {\n    try {\n        // Delete all existing Excel data for this user\n        await prisma.excelData.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Create new Excel data\n        const createdExcelData = await prisma.excelData.create({\n            data: {\n                shortOpenTableData: excelData.shortOpenTableData || [],\n                shortLoadedTableData: excelData.shortLoadedTableData || [],\n                longOpenTableData: excelData.longOpenTableData || [],\n                longLoadedTableData: excelData.longLoadedTableData || [],\n                shortClosedTableData: excelData.shortClosedTableData || [],\n                longClosedTableData: excelData.longClosedTableData || [],\n                userId,\n                createdAt: now,\n                updatedAt: now\n            }\n        });\n        return {\n            success: true,\n            insertedId: createdExcelData.id,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving Excel data:', error);\n        throw error;\n    }\n}\n// Get Excel data\nasync function getExcelData(userId) {\n    try {\n        const excelData = await prisma.excelData.findFirst({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return excelData;\n    } catch (error) {\n        console.error('Error getting Excel data:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma-dal.js\n");

/***/ }),

/***/ "(rsc)/./lib/rateLimit.js":
/*!**************************!*\
  !*** ./lib/rateLimit.js ***!
  \**************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit)\n/* harmony export */ });\n// lib/rateLimit.js\n// Simple in-memory rate limiter for development/testing (not for production scale)\nconst store = new Map();\nfunction rateLimit(key, limit = 5, windowMs = 60 * 1000) {\n    const now = Date.now();\n    let entry = store.get(key);\n    if (!entry || now - entry.last > windowMs) {\n        entry = {\n            count: 1,\n            last: now\n        };\n    } else {\n        entry.count += 1;\n    }\n    store.set(key, entry);\n    return entry.count > limit;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcmF0ZUxpbWl0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtQkFBbUI7QUFDbkIsbUZBQW1GO0FBQ25GLE1BQU1BLFFBQVEsSUFBSUM7QUFFWCxTQUFTQyxVQUFVQyxHQUFHLEVBQUVDLFFBQVEsQ0FBQyxFQUFFQyxXQUFXLEtBQUssSUFBSTtJQUM1RCxNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO0lBQ3BCLElBQUlFLFFBQVFSLE1BQU1TLEdBQUcsQ0FBQ047SUFDdEIsSUFBSSxDQUFDSyxTQUFTRixNQUFNRSxNQUFNRSxJQUFJLEdBQUdMLFVBQVU7UUFDekNHLFFBQVE7WUFBRUcsT0FBTztZQUFHRCxNQUFNSjtRQUFJO0lBQ2hDLE9BQU87UUFDTEUsTUFBTUcsS0FBSyxJQUFJO0lBQ2pCO0lBQ0FYLE1BQU1ZLEdBQUcsQ0FBQ1QsS0FBS0s7SUFDZixPQUFPQSxNQUFNRyxLQUFLLEdBQUdQO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVsbGVuXFxPbmVEcml2ZVxcRGVza3RvcFxcRGFzaGJvYXJkXFxTY2h3YWJEYXNoYm9hcmRQcm9qZWN0XFxsaWJcXHJhdGVMaW1pdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBsaWIvcmF0ZUxpbWl0LmpzXHJcbi8vIFNpbXBsZSBpbi1tZW1vcnkgcmF0ZSBsaW1pdGVyIGZvciBkZXZlbG9wbWVudC90ZXN0aW5nIChub3QgZm9yIHByb2R1Y3Rpb24gc2NhbGUpXHJcbmNvbnN0IHN0b3JlID0gbmV3IE1hcCgpO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHJhdGVMaW1pdChrZXksIGxpbWl0ID0gNSwgd2luZG93TXMgPSA2MCAqIDEwMDApIHtcclxuICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gIGxldCBlbnRyeSA9IHN0b3JlLmdldChrZXkpO1xyXG4gIGlmICghZW50cnkgfHwgbm93IC0gZW50cnkubGFzdCA+IHdpbmRvd01zKSB7XHJcbiAgICBlbnRyeSA9IHsgY291bnQ6IDEsIGxhc3Q6IG5vdyB9O1xyXG4gIH0gZWxzZSB7XHJcbiAgICBlbnRyeS5jb3VudCArPSAxO1xyXG4gIH1cclxuICBzdG9yZS5zZXQoa2V5LCBlbnRyeSk7XHJcbiAgcmV0dXJuIGVudHJ5LmNvdW50ID4gbGltaXQ7XHJcbn1cclxuIl0sIm5hbWVzIjpbInN0b3JlIiwiTWFwIiwicmF0ZUxpbWl0Iiwia2V5IiwibGltaXQiLCJ3aW5kb3dNcyIsIm5vdyIsIkRhdGUiLCJlbnRyeSIsImdldCIsImxhc3QiLCJjb3VudCIsInNldCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/rateLimit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_admin_users_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/users/route.js */ \"(rsc)/./app/api/admin/users/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/users/route\",\n        pathname: \"/api/admin/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\api\\\\admin\\\\users\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_admin_users_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cadmin%5C%5Cusers%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%22408d3d4bfe166baf17e2003eacd52b63a21edd7bc5%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cadmin%5C%5Cusers%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%22408d3d4bfe166baf17e2003eacd52b63a21edd7bc5%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"408d3d4bfe166baf17e2003eacd52b63a21edd7bc5\": () => (/* reexport safe */ C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_admin_users_route_js__WEBPACK_IMPORTED_MODULE_0__.GET)\n/* harmony export */ });\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabDashboardProject_app_api_admin_users_route_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/api/admin/users/route.js */ \"(rsc)/./app/api/admin/users/route.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRWxsZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEYXNoYm9hcmQlNUMlNUNTY2h3YWJEYXNoYm9hcmRQcm9qZWN0JTVDJTVDYXBwJTVDJTVDYXBpJTVDJTVDYWRtaW4lNUMlNUN1c2VycyU1QyU1Q3JvdXRlLmpzJTIyJTJDJTVCJTdCJTIyaWQlMjIlM0ElMjI0MDhkM2Q0YmZlMTY2YmFmMTdlMjAwM2VhY2Q1MmI2M2EyMWVkZDdiYzUlMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJHRVQlMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz0hIiwibWFwcGluZ3MiOiI7Ozs7OztBQUM4SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgR0VUIGFzIFwiNDA4ZDNkNGJmZTE2NmJhZjE3ZTIwMDNlYWNkNTJiNjNhMjFlZGQ3YmM1XCIgfSBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcRWxsZW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEYXNoYm9hcmRcXFxcU2Nod2FiRGFzaGJvYXJkUHJvamVjdFxcXFxhcHBcXFxcYXBpXFxcXGFkbWluXFxcXHVzZXJzXFxcXHJvdXRlLmpzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabDashboardProject%5C%5Capp%5C%5Capi%5C%5Cadmin%5C%5Cusers%5C%5Croute.js%22%2C%5B%7B%22id%22%3A%22408d3d4bfe166baf17e2003eacd52b63a21edd7bc5%22%2C%22exportedName%22%3A%22GET%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabDashboardProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();