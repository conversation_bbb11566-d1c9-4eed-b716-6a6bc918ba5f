import { rateLimit } from "./rateLimit.js";
import credentials from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { verifyUserCredentials } from "./prisma-dal.js";

const CredentialsProvider = credentials.default || credentials;

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", required: true },
        password: { label: "Password", type: "password", required: true },
      },
      async authorize(credentials, req) {
        // Rate limit by IP address (or fallback to email if no IP)
        let ip = null;
        if (req && req.headers) {
          ip = req.headers["x-forwarded-for"]?.split(",")[0]?.trim() || req.headers["x-real-ip"] || req.socket?.remoteAddress;
        }
        // fallback to email if no IP (not ideal, but better than nothing)
        const rateLimitKey = ip || (credentials?.email ? `email:${credentials.email}` : "unknown");
        if (rateLimit(rateLimitKey, 5, 60 * 1000)) {
          throw new Error("Too many login attempts. Please try again in a minute.");
        }
        try {
          // Check if credentials are provided
          if (!credentials?.email || !credentials?.password) {
            throw new Error("Email and password are required");
          }

          // Find the user using Prisma
          const user = await verifyUserCredentials(credentials.email);

          if (!user) {
            console.log("User not found");
            throw new Error("Invalid email or password");
          }

          console.log("User found:", user.email);

          // Verify password
          const isValidPassword = await bcrypt.compare(credentials.password, user.password);
          if (!isValidPassword) {
            console.log("Invalid password");
            throw new Error("Invalid email or password");
          }

          console.log("Password verified successfully");

          // Return the user object
          return {
            id: user.id,
            email: user.email,
            name: user.name || user.email.split('@')[0],
            role: user.role || 'user'
          };
        } catch (error) {
          console.error("Authentication error:", error);
          throw error;
        }
      },
    }),
  ],
  pages: {
    signIn: "/generalLogin",
    signOut: "/generalLogin",
    error: "/generalLogin", // Redirect to login page on error
  },
  callbacks: {
    async jwt({ token, user }) {
      // Add user data to the token when signing in
      if (user) {
        token.id = user.id;
        // Explicitly check for null/undefined and set to 'user' if so
        token.role = (user.role === undefined || user.role === null) ? 'user' : user.role;
      }
      return token;
    },
    async session({ session, token }) {
      // Reject session if token is missing or expired
      if (!token || !token.id || (token.exp && Date.now() / 1000 > token.exp)) {
        return null;
      }
      if (session.user) {
        session.user.id = token.id;
        session.user.role = (token.role === undefined || token.role === null) ? 'user' : token.role;
      }
      return session;
    }
  },
  debug: process.env.NODE_ENV === "development",
};
