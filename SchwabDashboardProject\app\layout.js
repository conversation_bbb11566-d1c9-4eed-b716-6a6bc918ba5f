
import "./globals.css";
import { Inter } from "next/font/google";
import MyClientWrapper from "./MyClientWrapper";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Trading Dashboard",
  description: "Schwab Trading Platform",
};

export default function RootLayout({ children }) {
  return (
    <html suppressHydrationWarning lang="en">
      <body className={`${inter.className} antialiased`}>
        <MyClientWrapper>{children}</MyClientWrapper>
      </body>
    </html>
  );
}
