"use client";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "../../../../components/ui/accordion";

import { OpenPositionsDataTable } from "./OpenPositionsDataTable";
import { LoadedPairsOrdersInDataTable } from "./LoadedPairsOrdersInDataTable";
import { ClosedPositionsDataTable } from "./ClosedPositionsDataTable";

export function AccordionsSection() {
  return (
    <div className="my-2">
      <Accordion type="multiple">
        <AccordionItem
          value="accordion-1"
          className=" border-2 border-amber-900 rounded"
        >
          <AccordionTrigger className="bg-amber-900  rounded items-center justify-center h-5">
            Open Positions
          </AccordionTrigger>
          <AccordionContent className="mx-1 my-1">
            <OpenPositionsDataTable />
          </AccordionContent>
        </AccordionItem>

        <div className="my-2"></div>

        <AccordionItem
          value="accordion-2"
          className=" border-2 border-amber-800 rounded"
        >
          <AccordionTrigger className="bg-amber-800  rounded items-center justify-center h-5">
            Loaded Pairs / Orders In
          </AccordionTrigger>
          <AccordionContent className="mx-1 my-1">
            <LoadedPairsOrdersInDataTable />
          </AccordionContent>
        </AccordionItem>

        <div className="my-2"></div>

        <AccordionItem
          value="accordion-3"
          className=" border-2 border-amber-700 rounded"
        >
          <AccordionTrigger className="bg-amber-700  rounded items-center justify-center h-5">
            Closed Positions
          </AccordionTrigger>
          <AccordionContent className="mx-1 my-1">
            <ClosedPositionsDataTable />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
