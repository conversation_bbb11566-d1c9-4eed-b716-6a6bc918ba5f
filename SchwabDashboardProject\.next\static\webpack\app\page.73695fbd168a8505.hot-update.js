"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ProtectedRoute */ \"(app-pages-browser)/./components/ProtectedRoute.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    var _session_user, _session_user_email, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isSchwabCallback, setIsSchwabCallback] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isLoggedInToSchwab, setIsLoggedInToSchwab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            console.log(\"HomePage session:\", session, \"status:\", status);\n        }\n    }[\"HomePage.useEffect\"], [\n        session,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if we're on the callback URL\n            const url = new URL(window.location.href);\n            const code = url.searchParams.get(\"code\");\n            if (code) {\n                setIsSchwabCallback(true);\n            }\n            // Always check Schwab login status on page load\n            checkSchwabLogin();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const checkSchwabLogin = async ()=>{\n        try {\n            // Chiedi lo stato Schwab al backend (cookie httpOnly)\n            const res = await fetch(\"/api/schwab-status\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            const data = await res.json();\n            setIsLoggedInToSchwab(data.loggedIn);\n            if (data.loggedIn) {\n                localStorage.setItem('schwabLoggedIn', 'true');\n            } else {\n                localStorage.removeItem('schwabLoggedIn');\n            }\n        } catch (error) {\n            console.error(\"Error checking Schwab login status:\", error);\n        }\n    };\n    const handleLogInToSchwab = async ()=>{\n        try {\n            console.log(\"Getting Auth URL...\");\n            const url = new URL(window.location.href);\n            let code = url.searchParams.get(\"code\");\n            if (!code) {\n                // Set a flag to indicate we're attempting to log in\n                localStorage.setItem('schwabLoginAttempt', 'true');\n                window.location.href = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeURL)();\n                return;\n            }\n            // If we have a code, we're logged in\n            localStorage.setItem('schwabLoggedIn', 'true');\n            setIsLoggedInToSchwab(true);\n        } catch (error) {\n            console.error(\"Error getting OAuth Token:\", error);\n        }\n    };\n    const handleLogOutFromSchwab = ()=>{\n        try {\n            // Clear Schwab-related cookies\n            document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            // Clear localStorage flags\n            localStorage.removeItem('schwabLoggedIn');\n            localStorage.removeItem('schwabLoginAttempt');\n            // Update state\n            setIsLoggedInToSchwab(false);\n            // Refresh the page to ensure all state is reset\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error logging out from Schwab:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        localStorage.clear();\n        sessionStorage.clear();\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/generalLogin\"\n        });\n    // window.location.href = \"/generalLogin\";\n    };\n    // Rich navigation cards with icons and colors\n    const navigationCards = [\n        {\n            title: \"Account Summary\",\n            description: \"View your account summary and balances\",\n            icon: \"📊\",\n            href: \"/accountsSummary\",\n            bgColor: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n            shadowColor: \"shadow-blue-500/20\",\n            hoverEffect: \"hover:shadow-blue-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Dashboard\",\n            description: \"Manage your WB trading pairs\",\n            icon: \"📈\",\n            href: \"/Strategies/WB/dashboard\",\n            bgColor: \"bg-gradient-to-br from-emerald-500 to-emerald-600\",\n            shadowColor: \"shadow-emerald-500/20\",\n            hoverEffect: \"hover:shadow-emerald-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Configuration\",\n            description: \"Configure your WB trading strategy\",\n            icon: \"⚙️\",\n            href: \"/Strategies/WB/configuration\",\n            bgColor: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n            shadowColor: \"shadow-purple-500/20\",\n            hoverEffect: \"hover:shadow-purple-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Saved Pairs\",\n            description: \"View your saved trading pairs\",\n            icon: \"🔖\",\n            href: \"/savedPairs\",\n            bgColor: \"bg-gradient-to-br from-amber-500 to-amber-600\",\n            shadowColor: \"shadow-amber-500/20\",\n            hoverEffect: \"hover:shadow-amber-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"File Handler\",\n            description: \"Manage your data files\",\n            icon: \"📁\",\n            href: \"/fileHandler\",\n            bgColor: \"bg-gradient-to-br from-indigo-500 to-indigo-600\",\n            shadowColor: \"shadow-indigo-500/20\",\n            hoverEffect: \"hover:shadow-indigo-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Account Activity\",\n            description: \"View your account activity\",\n            icon: \"📝\",\n            href: \"/testingAccountActivity\",\n            bgColor: \"bg-gradient-to-br from-rose-500 to-rose-600\",\n            shadowColor: \"shadow-rose-500/20\",\n            hoverEffect: \"hover:shadow-rose-500/40 hover:-translate-y-1\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm border border-white/30\",\n                                            children: \"✨ Professional Trading Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n                                        children: isSchwabCallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"\\uD83C\\uDF89 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300\",\n                                                    children: \"Connected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" Successfully!\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Your Trading \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 34\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                                    children: \"Command Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto mb-10 leading-relaxed\",\n                                        children: isSchwabCallback ? \"🚀 Your Schwab connection is live! Access real-time market data and manage your investment strategies with confidence.\" : \"🎯 Streamline your investment workflow with advanced analytics, real-time data integration, and comprehensive portfolio management tools.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold rounded-xl shadow-2xl hover:shadow-white/25 transition-all duration-300 transform hover:scale-105 hover:bg-blue-50\",\n                                        onClick: ()=>handleLogInToSchwab(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-2xl\",\n                                                children: \"\\uD83D\\uDD17\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Connect to Schwab\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-5 w-5 ml-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full backdrop-blur-sm animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-20 right-10 w-16 h-16 bg-yellow-300/20 rounded-full backdrop-blur-sm animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-20 w-12 h-12 bg-green-300/20 rounded-full backdrop-blur-sm animate-ping\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4 sm:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mr-4 backdrop-blur-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDC64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: [\n                                                                \"Welcome back, \",\n                                                                ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.split('@')[0]),\n                                                                \"! \\uD83D\\uDC4B\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2 \".concat(isLoggedInToSchwab ? 'bg-green-400' : 'bg-red-400', \" animate-pulse\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-100 text-sm\",\n                                                                    children: isLoggedInToSchwab ? \"🟢 Schwab Connected • Real-time data active\" : \"🔴 Schwab Disconnected • Limited access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogInToSchwab(),\n                                                    className: \"px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                                    children: \"\\uD83D\\uDD17 Connect Schwab\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogOutFromSchwab,\n                                                    className: \"px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10\",\n                                                    children: \"Disconnect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogout(),\n                                                    className: \"px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10\",\n                                                    children: \"\\uD83D\\uDEAA Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-gray-50 dark:bg-gray-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                                    children: \"\\uD83D\\uDCCA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Dashboard Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"✅ Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                                    children: \"⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Data Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"✅ Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 275,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Market Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-medium \".concat(isLoggedInToSchwab ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'),\n                                                    children: isLoggedInToSchwab ? '✅ Live' : '❌ Offline'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"\\uD83D\\uDE80 Platform Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto\",\n                                    children: \"Access all your trading tools and analytics from one powerful dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: navigationCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                    href: card.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative overflow-hidden rounded-2xl \".concat(card.bgColor, \" \").concat(card.shadowColor, \" shadow-2xl \").concat(card.hoverEffect, \" transition-all duration-300 cursor-pointer\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl\",\n                                                                children: card.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-6 w-6\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold mb-3 group-hover:text-yellow-200 transition-colors duration-300\",\n                                                        children: card.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm leading-relaxed mb-6\",\n                                                        children: card.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: \"Access Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-0.5 bg-white/60 group-hover:w-8 transition-all duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"Ready to Optimize Your Trading?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Access real-time market data, advanced analytics, and powerful trading tools all in one place.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/dashboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors\",\n                                                children: \"Go to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/configuration\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors backdrop-blur-sm\",\n                                                children: \"Configure Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vggbpo6EXw96Kx48ex04Bhn8Jac=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});