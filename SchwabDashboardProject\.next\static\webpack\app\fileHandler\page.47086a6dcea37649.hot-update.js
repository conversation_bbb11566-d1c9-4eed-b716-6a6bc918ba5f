"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fileHandler/page",{

/***/ "(app-pages-browser)/./app/Strategies/WB/configuration/page.js":
/*!*************************************************!*\
  !*** ./app/Strategies/WB/configuration/page.js ***!
  \*************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExcelDataProvider: () => (/* binding */ ExcelDataProvider),\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   useExcelData: () => (/* binding */ useExcelData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TooltipButton */ \"(app-pages-browser)/./components/TooltipButton.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ ExcelDataProvider,useExcelData,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst shortKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst longKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst ExcelDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    shortOpenTableData: [],\n    shortLoadedTableData: [],\n    longOpenTableData: [],\n    longLoadedTableData: [],\n    shortClosedTableData: [],\n    longClosedTableData: [],\n    setShortOpenTableData: ()=>{},\n    setShortLoadedTableData: ()=>{},\n    setLongOpenTableData: ()=>{},\n    setLongLoadedTableData: ()=>{},\n    setShortClosedTableData: ()=>{},\n    setLongClosedTableData: ()=>{},\n    updateLongStatus: ()=>{},\n    updateShortStatus: ()=>{}\n});\nfunction ExcelDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [shortOpenTableData, setShortOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortLoadedTableData, setShortLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longOpenTableData, setLongOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longLoadedTableData, setLongLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortClosedTableData, setShortClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longClosedTableData, setLongClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelDataProvider.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem(\"shortOpenTableData\", JSON.stringify(shortOpenTableData));\n                localStorage.setItem(\"shortLoadedTableData\", JSON.stringify(shortLoadedTableData));\n                localStorage.setItem(\"longOpenTableData\", JSON.stringify(longOpenTableData));\n                localStorage.setItem(\"longLoadedTableData\", JSON.stringify(longLoadedTableData));\n                localStorage.setItem(\"shortClosedTableData\", JSON.stringify(shortClosedTableData));\n                localStorage.setItem(\"longClosedTableData\", JSON.stringify(longClosedTableData));\n            }\n        }\n    }[\"ExcelDataProvider.useEffect\"], [\n        shortOpenTableData,\n        shortLoadedTableData,\n        shortClosedTableData,\n        longOpenTableData,\n        longLoadedTableData,\n        longClosedTableData\n    ]);\n    // Move a long row from any table to any other table, like the old system but for all tables\n    const updateLongStatus = (rowId, newStatus)=>{\n        // Try to find the row in all tables\n        let row = longLoadedTableData.find((r)=>r.id === rowId) || longOpenTableData.find((r)=>r.id === rowId) || longClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        // Remove from all tables\n        setLongLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        // Add to the right table\n        if (newStatus === \"WB_LoadedPairs\") {\n            setLongLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setLongOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setLongClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    // Move a short row from any table to any other table, like the old system but for all tables\n    const updateShortStatus = (rowId, newStatus)=>{\n        let row = shortLoadedTableData.find((r)=>r.id === rowId) || shortOpenTableData.find((r)=>r.id === rowId) || shortClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        setShortLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        if (newStatus === \"WB_LoadedPairs\") {\n            setShortLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setShortOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setShortClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    const updateShortClosedStatus = (rowId, newStatus)=>{\n        setShortOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setShortClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    const updateLongClosedStatus = (rowId, newStatus)=>{\n        setLongOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setLongClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelDataContext.Provider, {\n        value: {\n            shortOpenTableData,\n            shortLoadedTableData,\n            longOpenTableData,\n            longLoadedTableData,\n            shortClosedTableData,\n            longClosedTableData,\n            setShortOpenTableData,\n            setShortLoadedTableData,\n            setLongOpenTableData,\n            setLongLoadedTableData,\n            setShortClosedTableData,\n            setLongClosedTableData,\n            updateLongStatus,\n            updateShortStatus,\n            updateShortClosedStatus,\n            updateLongClosedStatus\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcelDataProvider, \"E1LSBqPBGk1KTN7p7jAqQ4cSOp0=\");\n_c = ExcelDataProvider;\nfunction useExcelData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ExcelDataContext);\n}\n_s1(useExcelData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction ExcelInput() {\n    _s2();\n    const [pasteData, setPasteData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [updateStatus, setUpdateStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [redirectAfterSubmit, setRedirectAfterSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [savedSymbols, setSavedSymbols] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData, shortClosedTableData, longClosedTableData } = useExcelData();\n    // Load saved symbols on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelInput.useEffect\": ()=>{\n            const loadSavedSymbols = {\n                \"ExcelInput.useEffect.loadSavedSymbols\": async ()=>{\n                    try {\n                        const response = await fetch('/api/get-saved-symbols');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setSavedSymbols(data.symbolsArray || []);\n                            console.log('Loaded saved symbols:', data.symbolsArray);\n                        }\n                    } catch (error) {\n                        console.error('Error loading saved symbols:', error);\n                    }\n                }\n            }[\"ExcelInput.useEffect.loadSavedSymbols\"];\n            loadSavedSymbols();\n        }\n    }[\"ExcelInput.useEffect\"], []);\n    const handleChange = (e)=>{\n        setPasteData(e.target.value);\n    };\n    const handleSubmit = async ()=>{\n        const rows = pasteData.split(/\\r?\\n/).filter((row)=>row.trim() !== \"\");\n        const parsedData = rows.map((row)=>row.split(\"\\t\"));\n        const existingDataShort = shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length || 0;\n        const existingDataLong = longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length || 0;\n        const shortData = parsedData.map((row, index)=>{\n            const obj = {};\n            shortKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataShort + index).toString()\n            };\n        });\n        const longData = parsedData.map((row, index)=>{\n            const obj = {};\n            longKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx + 5] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataLong + index).toString()\n            };\n        });\n        setShortLoadedTableData((prev)=>[\n                ...prev,\n                ...shortData\n            ]);\n        setLongLoadedTableData((prev)=>[\n                ...prev,\n                ...longData\n            ]);\n        setPasteData(\"\");\n        // Display status message\n        setUpdateStatus(\"Data processed successfully. Pairs will be saved to database when created.\");\n        // Redirect to dashboard if option is enabled\n        if (redirectAfterSubmit) {\n            // Wait a moment to ensure data is saved to localStorage\n            setTimeout(()=>{\n                window.location.href = \"/Strategies/WB/dashboard\";\n            }, 500);\n        }\n    };\n    // Function to update stock symbols on the server\n    const updateStockSymbols = async ()=>{\n        try {\n            setUpdateStatus(\"Update in progress...\");\n            // Collect all ticker symbols from both short and long data\n            const shortTickers = [\n                ...shortLoadedTableData,\n                ...shortOpenTableData,\n                ...shortClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            const longTickers = [\n                ...longLoadedTableData,\n                ...longOpenTableData,\n                ...longClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            // Combine and remove duplicates\n            const allTickers = [\n                ...new Set([\n                    ...shortTickers,\n                    ...longTickers\n                ])\n            ];\n            if (allTickers.length === 0) {\n                setUpdateStatus(\"No symbols found to update\");\n                return;\n            }\n            console.log(\"Symbols to send to server:\", allTickers);\n            // First, test if the server is responding\n            try {\n                const testUrl = \"https://localhost:3001/test\";\n                setUpdateStatus(\"Verifying server connection: \".concat(testUrl, \"...\"));\n                const testResponse = await fetch(testUrl);\n                if (!testResponse.ok) {\n                    setUpdateStatus(\"Error: The server is not responding correctly. Code: \".concat(testResponse.status));\n                    return;\n                }\n                const testData = await testResponse.json();\n                console.log(\"Test server response:\", testData);\n                setUpdateStatus(\"Server connected. Current symbols: \".concat(testData.currentSymbols, \". Sending new symbols...\"));\n            } catch (testError) {\n                console.error(\"Error in connection test:\", testError);\n                setUpdateStatus(\"Connection error: \".concat(testError.message, \". Make sure the server is running on http://localhost:3001\"));\n                return;\n            }\n            // Send to server\n            const url = \"https://localhost:3001/update-stock-symbols\";\n            try {\n                var _sessionData_user;\n                const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                // Get user session for email\n                const session = await fetch('/api/auth/session');\n                const sessionData = await session.json();\n                const userEmail = sessionData === null || sessionData === void 0 ? void 0 : (_sessionData_user = sessionData.user) === null || _sessionData_user === void 0 ? void 0 : _sessionData_user.email;\n                if (!userEmail) {\n                    console.warn('No user email found in session, symbols will not be associated with user account');\n                }\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        symbols: allTickers,\n                        token: accessToken,\n                        clientCustomerId: customerId,\n                        clientCorrelId: correlId,\n                        userEmail: userEmail\n                    }),\n                    credentials: \"include\"\n                });\n                // Check if response is JSON\n                const contentType = response.headers.get(\"content-type\");\n                if (!contentType || !contentType.includes(\"application/json\")) {\n                    const textResponse = await response.text();\n                    console.error(\"Server returned non-JSON response:\", textResponse);\n                    setUpdateStatus(\"Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001\");\n                    return;\n                }\n                const data = await response.json();\n                if (response.ok) {\n                    setUpdateStatus(\"Symbols updated successfully: \".concat(data.symbols));\n                    // Update the saved symbols display\n                    setSavedSymbols(data.symbols.split(','));\n                } else {\n                    setUpdateStatus(\"Error: \".concat(data.error || 'Unknown error'));\n                }\n            } catch (fetchError) {\n                console.error(\"Error in fetch request:\", fetchError);\n                setUpdateStatus(\"Connection error: \".concat(fetchError.message, \". Make sure the server is running on http://localhost:3001\"));\n            }\n        } catch (error) {\n            console.error(\"Error updating symbols:\", error);\n            setUpdateStatus(\"Error: \".concat(error.message || 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Paste Data from Excel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            onChange: handleChange,\n                            value: pasteData,\n                            placeholder: \"Paste Excel data here (tabular format)\",\n                            rows: \"10\",\n                            className: \"w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                onClick: handleSubmit,\n                                className: \"bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                tooltipText: \"Process the pasted Excel data and update the tables\",\n                                children: \"Process Data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            savedSymbols.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\",\n                                        children: [\n                                            \"Currently Saved Symbols (\",\n                                            savedSymbols.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-700 dark:text-blue-300 break-words\",\n                                        children: savedSymbols.join(', ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                onClick: updateStockSymbols,\n                                className: \"bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                tooltipText: \"Update stock symbols on the server for real-time data\",\n                                children: \"Update Symbols on Server\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    updateStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-md \".concat(updateStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800'),\n                        children: updateStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 425,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 366,\n        columnNumber: 5\n    }, this);\n}\n_s2(ExcelInput, \"3pu7fML+lOyH2fWTWG9569dVCXw=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c1 = ExcelInput;\nfunction ClearButton() {\n    _s3();\n    const { setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData, setShortClosedTableData, setLongClosedTableData } = useExcelData();\n    const [clearStatus, setClearStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const clearData = ()=>{\n        setShortOpenTableData([]);\n        setShortLoadedTableData([]);\n        setLongOpenTableData([]);\n        setLongLoadedTableData([]);\n        setShortClosedTableData([]);\n        setLongClosedTableData([]);\n        if (true) {\n            localStorage.removeItem(\"shortOpenTableData\");\n            localStorage.removeItem(\"shortLoadedTableData\");\n            localStorage.removeItem(\"longOpenTableData\");\n            localStorage.removeItem(\"longLoadedTableData\");\n            localStorage.removeItem(\"shortClosedTableData\");\n            localStorage.removeItem(\"longClosedTableData\");\n        }\n        setClearStatus(\"All data has been cleared\");\n        setTimeout(()=>setClearStatus(\"\"), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Data Management\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Clear all Excel data from memory and local storage. This action cannot be undone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: clearData,\n                                        className: \"bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                        tooltipText: \"Clear all Excel data from memory and local storage\",\n                                        children: \"Clear All Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 text-red-200 dark:text-red-900\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this),\n                    clearStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-md bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800\",\n                        children: clearStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 465,\n        columnNumber: 5\n    }, this);\n}\n_s3(ClearButton, \"rVGbK71ktCLVokc7Lo2a2EMXKWA=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c2 = ClearButton;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 shadow-md dark:shadow-indigo-950 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"WB Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mt-2\",\n                            children: \"Configure your WB trading strategy settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelInput, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClearButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, this);\n}\n_c3 = Home;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ExcelDataProvider\");\n$RefreshReg$(_c1, \"ExcelInput\");\n$RefreshReg$(_c2, \"ClearButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/Strategies/WB/configuration/page.js\n"));

/***/ })

});