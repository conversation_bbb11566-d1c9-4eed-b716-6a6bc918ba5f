"use client";

import { useState } from "react";
import ProtectedRoute from "../../components/ProtectedRoute";
import { useExcelData } from "../Strategies/WB/configuration/page";
import TooltipButton from "@/components/TooltipButton";

export default function FileHandler() {

  const {
    shortOpenTableData,
    shortLoadedTableData,
    longOpenTableData,
    longLoadedTableData,
    shortClosedTableData,
    longClosedTableData,
    setShortOpenTableData,
    setShortLoadedTableData,
    setLongOpenTableData,
    setLongLoadedTableData,
    setShortClosedTableData,
    setLongClosedTableData,
  } = useExcelData();

  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState("");

  const [loadStatus, setLoadStatus] = useState("");
  const [dbStatus, setDbStatus] = useState("");
  const [isLoadingFromDb, setIsLoadingFromDb] = useState(false);
  const [activeTab, setActiveTab] = useState("excelData");



  // Function to save Excel data to a file
  const saveExcelData = () => {
    const excelData = {
      shortOpenTableData,
      shortLoadedTableData,
      longOpenTableData,
      longLoadedTableData,
      shortClosedTableData,
      longClosedTableData,
    };

    const a = document.createElement("a");
    const file = new Blob([JSON.stringify(excelData, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(file);
    a.href = url;
    a.download = `excel_data_backup_${new Date().toISOString().slice(0, 10)}.json`;
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 0);
  };



  // Function to load Excel data from a file
  const loadExcelData = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = JSON.parse(event.target.result);

        // Validate the data structure
        if (!data.shortOpenTableData || !data.shortLoadedTableData ||
            !data.longOpenTableData || !data.longLoadedTableData ||
            !data.shortClosedTableData || !data.longClosedTableData) {
          throw new Error("Invalid Excel data format");
        }

        // Update the Excel data
        setShortOpenTableData(data.shortOpenTableData);
        setShortLoadedTableData(data.shortLoadedTableData);
        setLongOpenTableData(data.longOpenTableData);
        setLongLoadedTableData(data.longLoadedTableData);
        setShortClosedTableData(data.shortClosedTableData);
        setLongClosedTableData(data.longClosedTableData);

        setLoadStatus("Excel data loaded successfully");
        setTimeout(() => setLoadStatus(""), 3000);
      } catch (error) {
        console.error("Error loading Excel data:", error);
        setLoadStatus(`Error: ${error.message}`);
        setTimeout(() => setLoadStatus(""), 5000);
      }
    };
    reader.readAsText(file);
  };



  // Function to handle clicking the load button
  const handleLoadClick = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = loadExcelData;
    input.click();
  };

  // Function to load data from database
  const loadFromDatabase = async () => {
    try {
      setIsLoadingFromDb(true);
      setDbStatus("Loading data from database...");

      // Load Excel data first
      const excelResponse = await fetch('/api/mongodb/excel');

      if (excelResponse.ok) {
        const excelData = await excelResponse.json();

        if (excelData.success && excelData.excelData) {
          // Update Excel data
          setShortOpenTableData(excelData.excelData.shortOpenTableData || []);
          setShortLoadedTableData(excelData.excelData.shortLoadedTableData || []);
          setLongOpenTableData(excelData.excelData.longOpenTableData || []);
          setLongLoadedTableData(excelData.excelData.longLoadedTableData || []);
          setShortClosedTableData(excelData.excelData.shortClosedTableData || []);
          setLongClosedTableData(excelData.excelData.longClosedTableData || []);

          setDbStatus("Excel data loaded successfully from database");
        } else {
          setDbStatus("No Excel data found in database");
        }
      } else {
        console.error("Failed to fetch Excel data:", excelResponse.statusText);
        setDbStatus(`Error loading Excel data: ${excelResponse.statusText}`);
      }

      setTimeout(() => setDbStatus(""), 3000);
    } catch (error) {
      console.error("Error loading data from database:", error);
      setDbStatus(`Error: ${error.message}`);
      setTimeout(() => setDbStatus(""), 5000);
    } finally {
      setIsLoadingFromDb(false);
    }
  };



  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950">
        {/* Header section - Reduced height */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-3 px-4 shadow-md dark:shadow-indigo-950 mb-3">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold">File Handling & Backup</h1>
            <p className="text-blue-100 text-sm">Manage your data files and database backups</p>
          </div>
        </div>

        <main className="max-w-4xl mx-auto px-4">
          {/* Tab Navigation */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900 mb-6 p-4">
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`py-2 px-4 font-medium ${activeTab === 'excelData' ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}`}
                onClick={() => setActiveTab('excelData')}
              >
                Excel Data
              </button>
              <button
                className={`py-2 px-4 font-medium ${activeTab === 'database' ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}`}
                onClick={() => setActiveTab('database')}
              >
                Database
              </button>
            </div>
          </div>



        {/* Excel Data Tab */}
        {activeTab === 'excelData' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6">
              <h2 className="text-xl font-semibold text-white">Excel Data Backup</h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Save and load Excel data (shortOpenTableData, shortLoadedTableData, longOpenTableData, longLoadedTableData, shortClosedTableData, longClosedTableData)
              </p>

            <div className="flex flex-col gap-3 mb-6">
              <TooltipButton
                onClick={saveExcelData}
                className="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors"
                tooltipText="Save Excel data to a JSON file for backup purposes"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Save Excel Data to File
              </TooltipButton>

              <TooltipButton
                onClick={handleLoadClick}
                className="bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors"
                tooltipText="Load Excel data from a previously saved JSON file"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm7-14a1 1 0 01-1 1H5.414l4.293 4.293a1 1 0 01-1.414 1.414l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 0L13.586 3H9a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                Load Excel Data from File
              </TooltipButton>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4">
              <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Excel Data Summary</h3>
              <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400">
                <li>Short Open Pairs: {shortOpenTableData.length}</li>
                <li>Short Loaded Pairs: {shortLoadedTableData.length}</li>
                <li>Long Open Pairs: {longOpenTableData.length}</li>
                <li>Long Loaded Pairs: {longLoadedTableData.length}</li>
                <li>Short Closed Pairs: {shortClosedTableData.length}</li>
                <li>Long Closed Pairs: {longClosedTableData.length}</li>
                <li>Total Excel Entries: {shortOpenTableData.length + shortLoadedTableData.length + longOpenTableData.length + longLoadedTableData.length + shortClosedTableData.length + longClosedTableData.length}</li>
              </ul>
            </div>
            </div>
          </div>
        )}
        {/* Database Tab */}
        {activeTab === 'database' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-600 dark:from-purple-600 dark:to-indigo-700 py-3 px-6">
              <h2 className="text-xl font-semibold text-white">Database Operations</h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Save current data to database or load data from database
              </p>

            <div className="flex flex-col gap-3 mb-6">
              <TooltipButton
                onClick={async () => {
                  try {
                    setIsSaving(true);
                    setSaveStatus("Saving Excel data to database...");

                    // Prepare the Excel data
                    const excelData = {
                      shortOpenTableData,
                      shortLoadedTableData,
                      longOpenTableData,
                      longLoadedTableData,
                      shortClosedTableData,
                      longClosedTableData,
                    };

                    // Save Excel data to database
                    const response = await fetch('/api/mongodb/excel', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify(excelData),
                    });

                    const data = await response.json();

                    if (!response.ok) {
                      throw new Error(data.error || 'Failed to save Excel data');
                    }

                    setSaveStatus("Excel data saved successfully to database");
                    setTimeout(() => setSaveStatus(""), 3000);
                  } catch (error) {
                    console.error("Error saving data to database:", error);
                    setSaveStatus(`Error: ${error.message}`);
                    setTimeout(() => setSaveStatus(""), 5000);
                  } finally {
                    setIsSaving(false);
                  }
                }}
                disabled={isSaving}
                className={`${
                  isSaving ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed' : 'bg-purple-500 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-800'
                } text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors`}
                tooltipText="Save Excel data to the database for persistent storage"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                </svg>
                {isSaving ? 'Saving...' : 'Save Excel Data to Database'}
              </TooltipButton>

              <TooltipButton
                onClick={loadFromDatabase}
                disabled={isLoadingFromDb}
                className={`${
                  isLoadingFromDb ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed' : 'bg-indigo-500 hover:bg-indigo-700 dark:bg-indigo-600 dark:hover:bg-indigo-800'
                } text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors`}
                tooltipText="Load Excel data from the database, replacing current data"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                </svg>
                {isLoadingFromDb ? 'Loading...' : 'Load Excel Data from Database'}
              </TooltipButton>
            </div>
            </div>
          </div>
        )}

        {/* Status Messages */}
        {(loadStatus || saveStatus || dbStatus) && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6">
              <h2 className="text-xl font-semibold text-white">Status Messages</h2>
            </div>
            <div className="p-6">
              {loadStatus && (
                <div className={`p-4 mb-4 rounded-md ${loadStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'}`}>
                  {loadStatus}
                </div>
              )}

              {saveStatus && (
                <div className={`p-4 mb-4 rounded-md ${saveStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'}`}>
                  {saveStatus}
                </div>
              )}

              {dbStatus && (
                <div className={`p-4 mb-4 rounded-md ${dbStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'}`}>
                  {dbStatus}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Warning */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6">
          <div className="bg-gradient-to-r from-yellow-500 to-amber-600 dark:from-yellow-600 dark:to-amber-700 py-3 px-6">
            <h2 className="text-xl font-semibold text-white">Important Notice</h2>
          </div>
          <div className="p-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-yellow-500 dark:text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-gray-700 dark:text-gray-300">
                  This page is for backup and recovery purposes only. Use with caution as loading data will overwrite your current data.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      </div>
    </ProtectedRoute>
  );
}
