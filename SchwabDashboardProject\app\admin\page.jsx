"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { ShieldCheck, Users, Settings } from "lucide-react";

export default function AdminPage() {
  const { data: session, status } = useSession();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [role, setRole] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    if (session?.user?.role?.toLowerCase() !== "admin") return;
    fetch("/api/admin/users")
      .then((res) => res.json())
      .then((data) => {
        setUsers(data.users || []);
        setLoading(false);
      })
      .catch(() => {
        setError("Failed to load users");
        setLoading(false);
      });
  }, [session]);

  if (status === "loading") return <div className="p-8 text-center">Loading...</div>;
  if (!session || session.user.role?.toLowerCase() !== "admin") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldCheck className="w-16 h-16 text-blue-500 mb-4" />
        <h2 className="text-2xl font-bold mb-2">Admin Access Only</h2>
        <p className="text-gray-500">You must be an admin to view this page.</p>
      </div>
    );
  }

  const handleRoleChange = async (userId, newRole) => {
    setError("");
    setSuccess("");
    const res = await fetch("/api/admin/role", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ userId, role: newRole }),
    });
    if (res.ok) {
      setUsers((prev) => prev.map((u) => (u.id === userId ? { ...u, role: newRole } : u)));
      setSuccess("Role updated successfully");
    } else {
      setError("Failed to update role");
    }
  };



  return (
    <div className="max-w-5xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold flex items-center gap-2 mb-8">
        <Settings className="w-7 h-7 text-blue-500" /> Admin Panel
      </h1>
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-8 min-h-[60vh] flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <Users className="w-6 h-6 text-blue-400" /> Manage Users
          </h2>

        </div>
        {loading ? (
          <div>Loading users...</div>
        ) : error ? (
          <div className="text-red-500">{error}</div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {users.length === 0 ? (
              <li className="py-8 text-center text-gray-500 dark:text-gray-400">No users found.</li>
            ) : (
              users.map((user) => (
                <li key={user.id} className="py-4 flex items-center justify-between">
                  <div>
                    <div className="font-medium text-lg">{user.name || user.email}</div>
                    <div className="text-xs text-gray-500">{user.email}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <select
                      className="border rounded px-2 py-1 text-sm dark:bg-gray-800 dark:text-white"
                      value={user.role}
                      onChange={(e) => handleRoleChange(user.id, e.target.value)}
                    >
                      <option value="user">User</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                </li>
              ))
            )}
          </ul>
        )}
      </div>


    </div>
  );
}
