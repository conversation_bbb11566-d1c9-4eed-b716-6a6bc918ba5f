"use client";

import * as React from "react";
import { useContext } from "react";
import { AppContext } from "../../../../components/AppContext";
import {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export const columns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    accessorKey: "strategy",
    header: () => <div className="text-right">Strategy</div>,
    cell: ({ row }) => {
      const strategy = row.getValue("strategy");

      return <div className="text-right font-medium">{strategy}</div>;
    },
  },
  {
    accessorKey: "pair_id",
    header: () => <div className="text-right">Pair ID</div>,
    cell: ({ row }) => {
      const pairId = row.getValue("pair_id");

      return <div className="text-right font-medium">{pairId}</div>;
    },
  },
  {
    accessorKey: "symbol",
    header: () => <div className="text-right">Symbol</div>,
    cell: ({ row }) => {
      const symbol = row.getValue("symbol");

      return <div className="text-right font-medium">{symbol}</div>;
    },
  },
  {
    accessorKey: "side",
    header: () => <div className="text-right">Side</div>,
    cell: ({ row }) => {
      const side = row.getValue("side");

      return <div className="text-right font-medium">{side}</div>;
    },
  },
  {
    accessorKey: "quantity",
    header: () => <div className="text-right">Quantity</div>,
    cell: ({ row }) => {
      const quantity = row.getValue("quantity");

      return <div className="text-right font-medium">{quantity}</div>;
    },
  },
  {
    accessorKey: "ex_div_date",
    header: () => <div className="text-right">Ex-Div Date</div>,
    cell: ({ row }) => {
      const exDivDate = row.getValue("ex_div_date");

      return <div className="text-right font-medium">{exDivDate}</div>;
    },
  },
  {
    accessorKey: "spread",
    header: () => <div className="text-right">Spread</div>,
    cell: ({ row }) => {
      const spread = row.getValue("spread");

      return <div className="text-right font-medium">{spread}</div>;
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const deleteRow = () => {
        setData(
          data.StrategyConfig.pairCandidates.filter(
            (r) => r.id !== row.original.id
          )
        );
      };
      const payment = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={deleteRow}>Delete</DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(payment.id)}
            >
              Copy payment ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View customer</DropdownMenuItem>
            <DropdownMenuItem>View payment details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function WBConfigurationTable() {
  const { data, setData } = useContext(AppContext);
  const [sorting, setSorting] = React.useState([]);
  const [columnFilters, setColumnFilters] = React.useState([]);
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // const addRow = () => {
  //   const newRow = {
  //     id: `new_${Date.now()}`,
  //     strategy: "",
  //     pair_id: "",
  //     symbol: "",
  //     side: "",
  //     quantity: 0,
  //     ex_div_date: "",
  //     spread: 0,
  //   };
  //   setData([...data, newRow]);
  // };

  // const deleteRow = (id) => () => {
  //   setData(data.filter((row) => row.id !== id));
  // };

  const addRow = () => {
    const newRow = {
      id: `new_${Date.now()}`,
      strategy: "",
      pair_id: "",
      symbol: "",
      side: "",
      quantity: 0,
      ex_div_date: "",
      spread: 0,
    };
    setData((prevData) => ({
      ...prevData,
      StrategyConfig: {
        ...prevData.StrategyConfig,
        pairCandidates: [
          ...prevData.StrategyConfig.configuration.pairCandidates,
          newRow,
        ],
      },
    }));
    setData([...data, newRow]);
  };

  const deleteRow = (id) => () => {
    setData((prevData) => ({
      ...prevData,
      StrategyConfig: {
        ...prevData.StrategyConfig,

        pairCandidates:
          prevData.StrategyConfig.configuration.pairCandidates.filter(
            (row) => row.id !== id
          ),
      },
    }));
    setData(data.StrategyConfig.pairCandidates.filter((row) => row.id !== id));
  };

  const exportToCSV = () => {
    const csvContent = [
      Object.keys(initialData[0]).join(","),
      ...data.StrategyConfig.pairCandidates.map((row) =>
        Object.values(row).join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "WB Config.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full">
      <div className="flex items-center py-2 justify-between">
        <Button onClick={addRow}>Add Row</Button>
        <Button onClick={exportToCSV} className="ml-auto">
          Export to CSV
        </Button>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      className="border px-0 py-0 mx-0 my-0 h-10 text-sm place-items-center"
                      key={header.id}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => {
                    return (
                      <TableCell
                        className="border px-0 py-0 mx-0 my-0 h-8 text-sm truncate place-items-center"
                        key={cell.id}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length + 1}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
