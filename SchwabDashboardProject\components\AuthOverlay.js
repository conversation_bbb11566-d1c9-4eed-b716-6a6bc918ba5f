"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";

export default function AuthOverlay() {
  const { status } = useSession();
  const pathname = usePathname();
  const [isVisible, setIsVisible] = useState(true);
  
  // Pages that don't need the overlay
  const publicPages = ["/generalLogin"];
  const isPublicPage = publicPages.includes(pathname);
  
  useEffect(() => {
    console.log("AuthOverlay session status:", status);

    // If we're on a public page, hide the overlay
    if (isPublicPage) {
      setIsVisible(false);
      return;
    }
    
    // Only hide overlay if status is "authenticated"
    if (status === "authenticated") {
      // Add a small delay to ensure smooth transition
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300);
      
      return () => clearTimeout(timer);
    }
    
    // If not authenticated, show the overlay
    setIsVisible(true);
  }, [status, isPublicPage]);
  
  // If the overlay is not visible, don't render anything
  if (!isVisible) return null;
  
  // If we're on a public page, don't render the overlay
  if (isPublicPage) return null;
  
  return (
    <div 
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "white",
        zIndex: 9999,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <div 
        style={{
          width: "50px",
          height: "50px",
          border: "5px solid #f3f3f3",
          borderTop: "5px solid #3498db",
          borderRadius: "50%",
        }}
        className="animate-spin"
      />
    </div>
  );
}
