import { createContext, useContext, useEffect, useState } from "react";
import { getLinkedAccounts, getAllOrders, getTransactions} from "../actions/schwabTraderAPIactions";
import { accountObserver, orderObserver, transactionObserver } from "../utils/Observer";
import moment from "moment";
const EndpointAppContext = createContext();

export const EndpointAppProvider = ({ children }) => {
  const [accountData, setAccountData] = useState(null);
  const [transactionsData, setTransactionsData] = useState(null);
  const [ordersData, setOrdersData] = useState(null);

  // Date range states for filtering
  const [transactionsDateRange, setTransactionsDateRange] = useState({
    key: 'last7days',
    label: 'Last 7 Days',
    startDate: moment().subtract(7, 'days').startOf('day').toISOString(),
    endDate: moment().endOf('day').toISOString()
  });

  const [ordersDateRange, setOrdersDateRange] = useState({
    key: 'last7days',
    label: 'Last 7 Days',
    startDate: moment().subtract(7, 'days').startOf('day').toISOString(),
    endDate: moment().endOf('day').toISOString()
  });

  const accountNumber = process.env.NEXT_PUBLIC_ACCOUNT_NUMBER;; // we need to change this


  // Function to fetch transactions with date range
  const fetchTransactions = async (dateRange = transactionsDateRange) => {
    try {
      const transactions = await getTransactions(accountNumber, dateRange.startDate, dateRange.endDate, 'TRADE');
      setTransactionsData(transactions);
      transactionObserver.notify(transactions);
    } catch (error) {
      console.error("error fetching transactions:", error);
    }
  };

  // Function to fetch orders with date range
  const fetchOrders = async (dateRange = ordersDateRange) => {
    try {
      const orders = await getAllOrders(dateRange.startDate, dateRange.endDate);
      setOrdersData(orders);
      orderObserver.notify(orders);
    } catch (error) {
      console.error("error fetching orders:", error);
    }
  };

  // Function to update transactions date range and refetch data
  const updateTransactionsDateRange = (newRange) => {
    setTransactionsDateRange(newRange);
    fetchTransactions(newRange);
  };

  // Function to update orders date range and refetch data
  const updateOrdersDateRange = (newRange) => {
    setOrdersDateRange(newRange);
    fetchOrders(newRange);
  };

  useEffect(() => {
    // Fetch account data (no date filtering needed)
    getLinkedAccounts()
    .then((positions) => {
        console.log("Dati aggiornati:", positions);
        setAccountData(positions);
        accountObserver.notify(positions);
    })
    .catch((error) => {
        console.error("error fetching account data:", error);
    });

    // Fetch initial transactions and orders with default ranges
    fetchTransactions();
    fetchOrders();
  }, []);

  return (
    <EndpointAppContext.Provider value={{
      accountData,
      setAccountData,
      transactionsData,
      setTransactionsData,
      ordersData,
      setOrdersData,
      transactionsDateRange,
      ordersDateRange,
      updateTransactionsDateRange,
      updateOrdersDateRange
    }}>
      {children}
    </EndpointAppContext.Provider>
  );
};

export const useEndpointAppContext = () => useContext(EndpointAppContext);
