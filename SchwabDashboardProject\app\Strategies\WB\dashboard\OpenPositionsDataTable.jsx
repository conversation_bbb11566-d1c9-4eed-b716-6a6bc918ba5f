"use client";

import * as React from "react";
import {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useMarketData } from "../../../testingWebsocket/MarketDataContext";

// trying to use data from context

const data = [
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "AAPL",
    sh_b: 25.23, //BID
    sh_a: 25.24, //ASK
    sh_l: 25.24, //LAST
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: 54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "GOOG",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "NVDA",
    sh_b: 0,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: -54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "AAPL",
    sh_b: 25.23, 
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: 54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "KIM",
    sh_b: 25.23,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: -54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "KIM",
    sh_b: 25.23,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: -54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "KIM",
    sh_b: 25.23,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: 100,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "KIM",
    sh_b: 25.23,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: -54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
  {
    id: "m5gr84i9",
    sh_cost: 25.0,
    sh_amt: -150,
    sh_sym: "KIM",
    sh_b: 25.23,
    sh_a: 25.24,
    sh_l: 25.24,
    sh_chg: 0.6,
    sh_vol: 6000,
    sh_d: 0.25,
    sh_pl: 34,
    sh_ex: 12 / 5 / 2024,
    sh_spr: 0.15,
    sh_partial: "35 @ 21.22",
    sh_orders: "B 50 @ 21.20",
    pl: -54,
    l_orders: "S 200 @ 24.12",
    l_partial: "25 @ 24.11",
    l_spr: 0.17,
    l_ex: 12 / 5 / 2024,
    l_cost: 3.19,
    l_amt: 72,
    l_sym: "SB",
    l_b: 3.85,
    l_a: 3.86,
    l_l: 3.85,
    l_chg: -0.3,
    l_vol: 546176,
    l_d: 0.27,
    l_pl: 28,
  },
];

export const columns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "sh_cost",
    header: () => <div className="text-right">Cost</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_cost"));

      // Format the amount as a number with 2 decimal places
      const formatted = amount.toFixed(2);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },

  {
    accessorKey: "sh_amt",
    header: () => <div className="text-right">Amt</div>,

    // header: ({ column }) => {
    //   return (
    //     <Button
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Amt
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_amt"));

      // Format the amount as a whole number with no decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        maximumFractionDigits: 0,
      }).format(amount);

      const isNegative = amount < 0;
      const color = isNegative ? "text-red-500" : "text-green-500";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "sh_sym",
    header: () => <div className="text-right">Sym</div>,
    cell: ({ row }) => {
      const value = row.getValue("sh_sym");
      const color = "text-yellow-500";

      return <div className={`${color} text-right font-medium`}>{value}</div>;
    },
  },
  {
    accessorKey: "sh_b",
    header: () => <div className="text-right">B</div>,
    cell: ({ row }) => {
    const { filteredData } = useMarketData();

      const ticker = row.getValue("sh_sym");
      const dynamicValue = filteredData[ticker]?.bid_prc
      ? filteredData[ticker]?.bid_prc
      : row.getValue("sh_b");

    const amount = parseFloat(dynamicValue);

      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "sh_a",
    header: () => <div className="text-right">A</div>,
    cell: ({ row }) => {
      const { filteredData } = useMarketData();

    // Supponiamo che ogni riga abbia una proprietà "sh_sym" che funge da ticker
      const ticker = row.getValue("sh_sym");
    // Se nel context esiste un valore aggiornato per questo ticker, usalo; altrimenti usa il valore originale
      const dynamicValue = filteredData[ticker] && filteredData[ticker]?.ask_prc
      ? filteredData[ticker].ask_prc
      : row.getValue("sh_a");

      // Format the amount as a whole number with no decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(dynamicValue);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "sh_l",
    header: () => <div className="text-right">L</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_l"));

      const color = "text-yellow-500";

      // Format the amount as a whole number with no decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return (
        <div className={`${color} text-right font-medium`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "sh_chg",
    header: () => <div className="text-right">Chg</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_chg"));

      // Format the amount as a percentage
      const formatted = new Intl.NumberFormat("en-US", {
        style: "percent",
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
      }).format(amount / 100);

      const color =
        amount > 0 ? "text-green-500" : amount < 0 ? "text-red-500" : "";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "sh_vol",
    header: () => <div className="text-right text-xs">Vol/1k</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_vol"));

      // Format the amount as a dollar amount
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount / 1000);

      return <div className="text-right text-xs">{formatted}</div>;
    },
  },

  {
    accessorKey: "sh_d",
    header: () => <div className="text-right">D</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_d"));

      // Format the amount as a dollar amount
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return <div className="text-right text-xs">{formatted}</div>;
    },
  },

  {
    accessorKey: "sh_pl",
    header: () => <div className="text-right">P/L</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_pl"));

      // Format the amount as a whole number
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);

      const color =
        amount > 0 ? "text-green-500" : amount < 0 ? "text-red-500" : "";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },

  {
    accessorKey: "sh_ex",
    header: () => <div className="text-right">EX</div>,
    cell: ({ row }) => {
      const date = new Date(row.getValue("sh_ex"));

      // Format the date as a date
      const formatted = new Intl.DateTimeFormat("en-US", {
        month: "2-digit",
        day: "2-digit",
      }).format(date);

      const color = "";
      return (
        // <div className={`${color} text-right font-medium`}>{formatted}</div>
        <div className="text-right font-medium">{formatted}</div>
      );
    },
  },
  {
    accessorKey: "sh_spr",
    header: () => <div className="text-right">SPR</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("sh_spr"));

      // Format the amount as a whole number with 2 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      const color = "";
      return (
        <div className={`${color} text-right font-medium`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "sh_partial",
    header: () => <div className="text-right">Partial</div>,
    cell: ({ row }) => {
      const value = row.getValue("sh_partial");
      return <div className="text-right text-xs">{value}</div>;
    },
  },
  {
    accessorKey: "sh_orders",
    header: () => <div className="text-right">Orders</div>,
    cell: ({ row }) => {
      const value = row.getValue("sh_orders");
      const color = "text-green-500";

      return <div className={`text-right text-xs ${color}`}>{value}</div>;
    },
  },

  {
    accessorKey: "sh_buy_button",
    header: () => <div className="text-right">Buy</div>,
    cell: () => {
      return (
        <div className="text-right">
          <button className="bg-green-500 text-white text-xs py-1 px-2 rounded">
            B
          </button>
        </div>
      );
    },
  },

  {
    accessorKey: "pl",
    header: () => <div className="text-right">P/L</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("pl"));

      // Format the amount as a whole number with 0 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);

      const color =
        amount > 0 ? "text-green-500" : amount < 0 ? "text-red-500" : "";

      return <div className={`text-right text-xl  ${color}`}>{formatted}</div>;
    },
  },

  {
    accessorKey: "l_sell_button",
    header: () => <div className="text-right">Sell</div>,
    cell: () => {
      return (
        <div className="text-right">
          <button className="bg-red-500 text-white text-xs py-1 px-2 rounded">
            S
          </button>
        </div>
      );
    },
  },

  {
    accessorKey: "l_orders",
    header: () => <div className="text-right">Orders</div>,
    cell: ({ row }) => {
      const value = row.getValue("l_orders");
      const color = "text-red-500";

      return <div className={`text-right text-xs ${color}`}>{value}</div>;
    },
  },
  {
    accessorKey: "l_partial",
    header: () => <div className="text-right">Partial</div>,
    cell: ({ row }) => {
      const value = row.getValue("l_partial");
      return <div className="text-right text-xs">{value}</div>;
    },
  },
  {
    accessorKey: "l_spr",
    header: () => <div className="text-right">SPR</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_spr"));

      // Format the amount as a whole number with 2 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      const color = "";
      return (
        <div className={`${color} text-right font-medium`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "l_ex",
    header: () => <div className="text-right">EX</div>,
    cell: ({ row }) => {
      const date = new Date(row.getValue("l_ex"));

      // Format the date as a date
      const formatted = new Intl.DateTimeFormat("en-US", {
        month: "2-digit",
        day: "2-digit",
      }).format(date);

      const color = "";
      return (
        <div className={`${color} text-right font-medium`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "l_cost",
    header: () => <div className="text-right">Cost</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_cost"));

      // Format the amount as a number with 2 decimal places
      const formatted = amount.toFixed(2);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "l_amt",
    header: () => <div className="text-right">Amt</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_amt"));

      // Format the amount as a whole number with no decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        maximumFractionDigits: 0,
      }).format(amount);

      const isNegative = amount < 0;
      const color = isNegative ? "text-red-500" : "text-green-500";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "l_sym",
    header: () => <div className="text-right">Sym</div>,
    cell: ({ row }) => {
      const value = row.getValue("l_sym");
      const color = "text-yellow-500";

      return <div className={`${color} text-right font-medium`}>{value}</div>;
    },
  },
  {
    accessorKey: "l_b",
    header: () => <div className="text-right">B</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_b"));

      // Format the amount as a whole number with 2 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "l_a",
    header: () => <div className="text-right">A</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_a"));

      // Format the amount as a whole number with 2 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "l_l",
    header: () => <div className="text-right">L</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_l"));

      const color = "text-yellow-500";

      // Format the amount as a whole number with 2 decimal places
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumIntegerDigits: 1,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return (
        <div className={`${color} text-right font-medium`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "l_chg",
    header: () => <div className="text-right">Chg</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_chg"));

      // Format the amount as a percentage
      const formatted = new Intl.NumberFormat("en-US", {
        style: "percent",
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
      }).format(amount / 100);

      const color =
        amount > 0 ? "text-green-500" : amount < 0 ? "text-red-500" : "";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },
  {
    accessorKey: "l_vol",
    header: () => <div className="text-right">Vol/1k</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_vol"));

      // Format the amount as a dollar amount
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount / 1000);

      return <div className="text-right text-xs">{formatted}</div>;
    },
  },
  {
    accessorKey: "l_d",
    header: () => <div className="text-right">D</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_d"));

      // Format the amount as a dollar amount
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return <div className="text-right text-xs">{formatted}</div>;
    },
  },
  {
    accessorKey: "l_pl",
    header: () => <div className="text-right">P/L</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("l_pl"));

      // Format the amount as a whole number
      const formatted = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);

      const color =
        amount > 0 ? "text-green-500" : amount < 0 ? "text-red-500" : "";

      return (
        <div className={`text-right font-medium ${color}`}>{formatted}</div>
      );
    },
  },

  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const payment = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(payment.id)}
            >
              Copy payment ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View customer</DropdownMenuItem>
            <DropdownMenuItem>View payment details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function OpenPositionsDataTable() {
  const [sorting, setSorting] = React.useState([]);
  const [columnFilters, setColumnFilters] = React.useState([]);
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      {/* <div className="flex items-center py-2">
        <Input
          placeholder="Filter emails..."
          value={table.getColumn("email")?.getFilterValue() ?? ""}
          onChange={(event) =>
            table.getColumn("email")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div> */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      className="border px-0 py-0 mx-0 my-0 h-10 text-sm place-items-center"
                      key={header.id}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => {
                    const bgColor = [
                      "sh_ex",
                      "sh_spr",
                      "l_ex",
                      "l_spr",
                    ].includes(cell.column.id)
                      ? "bg-slate-900"
                      : "";

                    return (
                      <TableCell
                        className={`border px-0 py-0 mx-0 my-0 h-8 text-sm truncate place-items-center ${bgColor}`}
                        key={cell.id}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div> */}
    </div>
  );
}
