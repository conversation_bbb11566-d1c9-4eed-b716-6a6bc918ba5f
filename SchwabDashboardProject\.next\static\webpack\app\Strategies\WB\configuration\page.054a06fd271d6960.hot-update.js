"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/Strategies/WB/configuration/page",{

/***/ "(app-pages-browser)/./app/Strategies/WB/configuration/page.js":
/*!*************************************************!*\
  !*** ./app/Strategies/WB/configuration/page.js ***!
  \*************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExcelDataProvider: () => (/* binding */ ExcelDataProvider),\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   useExcelData: () => (/* binding */ useExcelData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TooltipButton */ \"(app-pages-browser)/./components/TooltipButton.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ ExcelDataProvider,useExcelData,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst shortKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst longKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst ExcelDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    shortOpenTableData: [],\n    shortLoadedTableData: [],\n    longOpenTableData: [],\n    longLoadedTableData: [],\n    shortClosedTableData: [],\n    longClosedTableData: [],\n    setShortOpenTableData: ()=>{},\n    setShortLoadedTableData: ()=>{},\n    setLongOpenTableData: ()=>{},\n    setLongLoadedTableData: ()=>{},\n    setShortClosedTableData: ()=>{},\n    setLongClosedTableData: ()=>{},\n    updateLongStatus: ()=>{},\n    updateShortStatus: ()=>{}\n});\nfunction ExcelDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [shortOpenTableData, setShortOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortLoadedTableData, setShortLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longOpenTableData, setLongOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longLoadedTableData, setLongLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortClosedTableData, setShortClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longClosedTableData, setLongClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelDataProvider.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem(\"shortOpenTableData\", JSON.stringify(shortOpenTableData));\n                localStorage.setItem(\"shortLoadedTableData\", JSON.stringify(shortLoadedTableData));\n                localStorage.setItem(\"longOpenTableData\", JSON.stringify(longOpenTableData));\n                localStorage.setItem(\"longLoadedTableData\", JSON.stringify(longLoadedTableData));\n                localStorage.setItem(\"shortClosedTableData\", JSON.stringify(shortClosedTableData));\n                localStorage.setItem(\"longClosedTableData\", JSON.stringify(longClosedTableData));\n            }\n        }\n    }[\"ExcelDataProvider.useEffect\"], [\n        shortOpenTableData,\n        shortLoadedTableData,\n        shortClosedTableData,\n        longOpenTableData,\n        longLoadedTableData,\n        longClosedTableData\n    ]);\n    // Move a long row from any table to any other table, like the old system but for all tables\n    const updateLongStatus = (rowId, newStatus)=>{\n        // Try to find the row in all tables\n        let row = longLoadedTableData.find((r)=>r.id === rowId) || longOpenTableData.find((r)=>r.id === rowId) || longClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        // Remove from all tables\n        setLongLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        // Add to the right table\n        if (newStatus === \"WB_LoadedPairs\") {\n            setLongLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setLongOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setLongClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    // Move a short row from any table to any other table, like the old system but for all tables\n    const updateShortStatus = (rowId, newStatus)=>{\n        let row = shortLoadedTableData.find((r)=>r.id === rowId) || shortOpenTableData.find((r)=>r.id === rowId) || shortClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        setShortLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        if (newStatus === \"WB_LoadedPairs\") {\n            setShortLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setShortOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setShortClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    const updateShortClosedStatus = (rowId, newStatus)=>{\n        setShortOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setShortClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    const updateLongClosedStatus = (rowId, newStatus)=>{\n        setLongOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setLongClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelDataContext.Provider, {\n        value: {\n            shortOpenTableData,\n            shortLoadedTableData,\n            longOpenTableData,\n            longLoadedTableData,\n            shortClosedTableData,\n            longClosedTableData,\n            setShortOpenTableData,\n            setShortLoadedTableData,\n            setLongOpenTableData,\n            setLongLoadedTableData,\n            setShortClosedTableData,\n            setLongClosedTableData,\n            updateLongStatus,\n            updateShortStatus,\n            updateShortClosedStatus,\n            updateLongClosedStatus\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcelDataProvider, \"E1LSBqPBGk1KTN7p7jAqQ4cSOp0=\");\n_c = ExcelDataProvider;\nfunction useExcelData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ExcelDataContext);\n}\n_s1(useExcelData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction ExcelInput() {\n    _s2();\n    const [pasteData, setPasteData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [updateStatus, setUpdateStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [redirectAfterSubmit, setRedirectAfterSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // ✅ Disabled auto-redirect\n    const [savedSymbols, setSavedSymbols] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData, shortClosedTableData, longClosedTableData } = useExcelData();\n    // Load saved symbols on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelInput.useEffect\": ()=>{\n            const loadSavedSymbols = {\n                \"ExcelInput.useEffect.loadSavedSymbols\": async ()=>{\n                    try {\n                        const response = await fetch('/api/get-saved-symbols');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setSavedSymbols(data.symbolsArray || []);\n                            console.log('Loaded saved symbols:', data.symbolsArray);\n                        }\n                    } catch (error) {\n                        console.error('Error loading saved symbols:', error);\n                    }\n                }\n            }[\"ExcelInput.useEffect.loadSavedSymbols\"];\n            loadSavedSymbols();\n        }\n    }[\"ExcelInput.useEffect\"], []);\n    const handleChange = (e)=>{\n        setPasteData(e.target.value);\n    };\n    const handleSubmit = async ()=>{\n        const rows = pasteData.split(/\\r?\\n/).filter((row)=>row.trim() !== \"\");\n        const parsedData = rows.map((row)=>row.split(\"\\t\"));\n        const existingDataShort = shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length || 0;\n        const existingDataLong = longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length || 0;\n        const shortData = parsedData.map((row, index)=>{\n            const obj = {};\n            shortKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataShort + index).toString()\n            };\n        });\n        const longData = parsedData.map((row, index)=>{\n            const obj = {};\n            longKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx + 5] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataLong + index).toString()\n            };\n        });\n        setShortLoadedTableData((prev)=>[\n                ...prev,\n                ...shortData\n            ]);\n        setLongLoadedTableData((prev)=>[\n                ...prev,\n                ...longData\n            ]);\n        setPasteData(\"\");\n        // Display status message\n        setUpdateStatus(\"Data processed successfully. Pairs will be saved to database when created.\");\n        // Redirect to dashboard if option is enabled\n        if (redirectAfterSubmit) {\n            // Wait a moment to ensure data is saved to localStorage\n            setTimeout(()=>{\n                window.location.href = \"/Strategies/WB/dashboard\";\n            }, 500);\n        }\n    };\n    // Function to update stock symbols on the server\n    const updateStockSymbols = async ()=>{\n        try {\n            setUpdateStatus(\"Update in progress...\");\n            // Collect all ticker symbols from both short and long data\n            const shortTickers = [\n                ...shortLoadedTableData,\n                ...shortOpenTableData,\n                ...shortClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            const longTickers = [\n                ...longLoadedTableData,\n                ...longOpenTableData,\n                ...longClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            // Combine and remove duplicates\n            const allTickers = [\n                ...new Set([\n                    ...shortTickers,\n                    ...longTickers\n                ])\n            ];\n            if (allTickers.length === 0) {\n                setUpdateStatus(\"No symbols found to update\");\n                return;\n            }\n            console.log(\"Symbols to send to server:\", allTickers);\n            // First, test if the server is responding\n            try {\n                const testUrl = \"https://localhost:3001/test\";\n                setUpdateStatus(\"Verifying server connection: \".concat(testUrl, \"...\"));\n                const testResponse = await fetch(testUrl);\n                if (!testResponse.ok) {\n                    setUpdateStatus(\"Error: The server is not responding correctly. Code: \".concat(testResponse.status));\n                    return;\n                }\n                const testData = await testResponse.json();\n                console.log(\"Test server response:\", testData);\n                setUpdateStatus(\"Server connected. Current symbols: \".concat(testData.currentSymbols, \". Sending new symbols...\"));\n            } catch (testError) {\n                console.error(\"Error in connection test:\", testError);\n                setUpdateStatus(\"Connection error: \".concat(testError.message, \". Make sure the server is running on http://localhost:3001\"));\n                return;\n            }\n            // Send to server\n            const url = \"https://localhost:3001/update-stock-symbols\";\n            try {\n                var _sessionData_user;\n                const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                // Get user session for email\n                const session = await fetch('/api/auth/session');\n                const sessionData = await session.json();\n                const userEmail = sessionData === null || sessionData === void 0 ? void 0 : (_sessionData_user = sessionData.user) === null || _sessionData_user === void 0 ? void 0 : _sessionData_user.email;\n                if (!userEmail) {\n                    console.warn('No user email found in session, symbols will not be associated with user account');\n                }\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        symbols: allTickers,\n                        token: accessToken,\n                        clientCustomerId: customerId,\n                        clientCorrelId: correlId,\n                        userEmail: userEmail\n                    }),\n                    credentials: \"include\"\n                });\n                // Check if response is JSON\n                const contentType = response.headers.get(\"content-type\");\n                if (!contentType || !contentType.includes(\"application/json\")) {\n                    const textResponse = await response.text();\n                    console.error(\"Server returned non-JSON response:\", textResponse);\n                    setUpdateStatus(\"Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001\");\n                    return;\n                }\n                const data = await response.json();\n                if (response.ok) {\n                    setUpdateStatus(\"Symbols updated successfully: \".concat(data.symbols));\n                    // Update the saved symbols display\n                    setSavedSymbols(data.symbols.split(','));\n                } else {\n                    setUpdateStatus(\"Error: \".concat(data.error || 'Unknown error'));\n                }\n            } catch (fetchError) {\n                console.error(\"Error in fetch request:\", fetchError);\n                setUpdateStatus(\"Connection error: \".concat(fetchError.message, \". Make sure the server is running on http://localhost:3001\"));\n            }\n        } catch (error) {\n            console.error(\"Error updating symbols:\", error);\n            setUpdateStatus(\"Error: \".concat(error.message || 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Paste Data from Excel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            onChange: handleChange,\n                            value: pasteData,\n                            placeholder: \"Paste Excel data here (tabular format)\",\n                            rows: \"10\",\n                            className: \"w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: handleSubmit,\n                                        className: \"bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Process the pasted Excel data and update the tables\",\n                                        children: \"Process Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: updateStockSymbols,\n                                        className: \"bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Update stock symbols on the server for real-time data\",\n                                        children: \"Update Symbols\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            savedSymbols.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-blue-800 dark:text-blue-200 flex items-center gap-2\",\n                                            children: [\n                                                \"Currently Saved Symbols\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-600 rounded-full\",\n                                                    children: savedSymbols.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1.5\",\n                                        children: savedSymbols.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-1 text-xs font-medium text-blue-800 dark:text-blue-200 bg-blue-100 dark:bg-blue-800/30 rounded-full border border-blue-200 dark:border-blue-700\",\n                                                children: symbol\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    updateStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-xl shadow-sm border \".concat(updateStatus.includes('Error') ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800' : 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: updateStatus.includes('Error') ? '❌' : '✅'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: updateStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 366,\n        columnNumber: 5\n    }, this);\n}\n_s2(ExcelInput, \"0eRAS1VQxr67spwKsqYS5u8B+pM=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c1 = ExcelInput;\nfunction ClearButton() {\n    _s3();\n    const { setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData, setShortClosedTableData, setLongClosedTableData } = useExcelData();\n    const [clearStatus, setClearStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const clearData = ()=>{\n        setShortOpenTableData([]);\n        setShortLoadedTableData([]);\n        setLongOpenTableData([]);\n        setLongLoadedTableData([]);\n        setShortClosedTableData([]);\n        setLongClosedTableData([]);\n        if (true) {\n            localStorage.removeItem(\"shortOpenTableData\");\n            localStorage.removeItem(\"shortLoadedTableData\");\n            localStorage.removeItem(\"longOpenTableData\");\n            localStorage.removeItem(\"longLoadedTableData\");\n            localStorage.removeItem(\"shortClosedTableData\");\n            localStorage.removeItem(\"longClosedTableData\");\n        }\n        setClearStatus(\"All data has been cleared\");\n        setTimeout(()=>setClearStatus(\"\"), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Data Management\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Clear all Excel data from memory and local storage. This action cannot be undone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: clearData,\n                                        className: \"bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n                                        tooltipText: \"Clear all Excel data from memory and local storage\",\n                                        children: \"Clear All Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 text-red-200 dark:text-red-900\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this),\n                    clearStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-xl shadow-sm border bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: \"✅\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: clearStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 515,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 490,\n        columnNumber: 5\n    }, this);\n}\n_s3(ClearButton, \"rVGbK71ktCLVokc7Lo2a2EMXKWA=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c2 = ClearButton;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 shadow-md dark:shadow-indigo-950 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"WB Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mt-2\",\n                            children: \"Configure your WB trading strategy settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelInput, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClearButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 540,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 529,\n        columnNumber: 5\n    }, this);\n}\n_c3 = Home;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ExcelDataProvider\");\n$RefreshReg$(_c1, \"ExcelInput\");\n$RefreshReg$(_c2, \"ClearButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/Strategies/WB/configuration/page.js\n"));

/***/ })

});