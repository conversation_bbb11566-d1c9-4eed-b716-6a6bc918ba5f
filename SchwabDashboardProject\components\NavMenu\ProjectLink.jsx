import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const ProjectLink = ({ children, name, setSelectedProject, showName }) => {
  const handleClick = () => {
    setSelectedProject(null);
    setTimeout(() => {
      setSelectedProject(name);
    }, 250);
  };
  return (
    <TooltipProvider  delayDuration={20}>
      <Tooltip>
        <TooltipTrigger asChild>
          <a
            onClick={handleClick}
            className="flex p-1 rounded cursor-pointer stroke-[0.75] hover:stroke-neutral-100 dark:hover:stroke-white stroke-neutral-400 dark:stroke-neutral-300 text-neutral-400 dark:text-neutral-300 hover:text-neutral-100 dark:hover:text-white place-items-center gap-2 hover:bg-neutral-700/30 dark:hover:bg-neutral-600/30 transition-colors duration-100"
          >
            {children}
            <div className="flex overflow-clip place-items-center justify-between w-full">
              {showName && (
                <p className="text-inherit truncate whitespace-nowrap tracking-wide">
                  {name}
                </p>
              )}
              <ChevronRightIcon className="stroke-inherit stroke-[0.75] min-w-8 w-8" />
            </div>
          </a>
        </TooltipTrigger>
        {!showName && (
          <TooltipContent side="right">
            <p>{name}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};

export default ProjectLink;
