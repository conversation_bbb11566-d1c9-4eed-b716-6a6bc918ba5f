"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ProtectedRoute */ \"(app-pages-browser)/./components/ProtectedRoute.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    var _session_user, _session_user_email, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isSchwabCallback, setIsSchwabCallback] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isLoggedInToSchwab, setIsLoggedInToSchwab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            console.log(\"HomePage session:\", session, \"status:\", status);\n        }\n    }[\"HomePage.useEffect\"], [\n        session,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if we're on the callback URL\n            const url = new URL(window.location.href);\n            const code = url.searchParams.get(\"code\");\n            if (code) {\n                setIsSchwabCallback(true);\n            }\n            // Always check Schwab login status on page load\n            checkSchwabLogin();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const checkSchwabLogin = async ()=>{\n        try {\n            // Chiedi lo stato Schwab al backend (cookie httpOnly)\n            const res = await fetch(\"/api/schwab-status\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            const data = await res.json();\n            setIsLoggedInToSchwab(data.loggedIn);\n            if (data.loggedIn) {\n                localStorage.setItem('schwabLoggedIn', 'true');\n            } else {\n                localStorage.removeItem('schwabLoggedIn');\n            }\n        } catch (error) {\n            console.error(\"Error checking Schwab login status:\", error);\n        }\n    };\n    const handleLogInToSchwab = async ()=>{\n        try {\n            console.log(\"Getting Auth URL...\");\n            const url = new URL(window.location.href);\n            let code = url.searchParams.get(\"code\");\n            if (!code) {\n                // Set a flag to indicate we're attempting to log in\n                localStorage.setItem('schwabLoginAttempt', 'true');\n                window.location.href = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeURL)();\n                return;\n            }\n            // If we have a code, we're logged in\n            localStorage.setItem('schwabLoggedIn', 'true');\n            setIsLoggedInToSchwab(true);\n        } catch (error) {\n            console.error(\"Error getting OAuth Token:\", error);\n        }\n    };\n    const handleLogOutFromSchwab = ()=>{\n        try {\n            // Clear Schwab-related cookies\n            document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_correlId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            document.cookie = 'client_customerId=; Max-Age=0; path=/; domain=' + window.location.hostname;\n            // Clear localStorage flags\n            localStorage.removeItem('schwabLoggedIn');\n            localStorage.removeItem('schwabLoginAttempt');\n            // Update state\n            setIsLoggedInToSchwab(false);\n            // Refresh the page to ensure all state is reset\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error logging out from Schwab:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        localStorage.clear();\n        sessionStorage.clear();\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/generalLogin\"\n        });\n    // window.location.href = \"/generalLogin\";\n    };\n    // Professional navigation cards with SVG icons\n    const navigationCards = [\n        {\n            title: \"Account Summary\",\n            description: \"Comprehensive portfolio overview and account balances\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this),\n            href: \"/accountsSummary\",\n            bgColor: \"bg-gradient-to-br from-slate-700 to-slate-800\",\n            shadowColor: \"shadow-slate-500/20\",\n            hoverEffect: \"hover:shadow-slate-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Dashboard\",\n            description: \"Real-time trading pair monitoring and management\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/dashboard\",\n            bgColor: \"bg-gradient-to-br from-blue-700 to-blue-800\",\n            shadowColor: \"shadow-blue-500/20\",\n            hoverEffect: \"hover:shadow-blue-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"WB Configuration\",\n            description: \"Advanced strategy setup and parameter optimization\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            href: \"/Strategies/WB/configuration\",\n            bgColor: \"bg-gradient-to-br from-indigo-700 to-indigo-800\",\n            shadowColor: \"shadow-indigo-500/20\",\n            hoverEffect: \"hover:shadow-indigo-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Saved Pairs\",\n            description: \"Curated collection of trading pair configurations\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            href: \"/savedPairs\",\n            bgColor: \"bg-gradient-to-br from-emerald-700 to-emerald-800\",\n            shadowColor: \"shadow-emerald-500/20\",\n            hoverEffect: \"hover:shadow-emerald-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"File Handler\",\n            description: \"Data import and file management utilities\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            href: \"/fileHandler\",\n            bgColor: \"bg-gradient-to-br from-gray-700 to-gray-800\",\n            shadowColor: \"shadow-gray-500/20\",\n            hoverEffect: \"hover:shadow-gray-500/40 hover:-translate-y-1\"\n        },\n        {\n            title: \"Account Activity\",\n            description: \"Transaction history and detailed activity logs\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            href: \"/testingAccountActivity\",\n            bgColor: \"bg-gradient-to-br from-purple-700 to-purple-800\",\n            shadowColor: \"shadow-purple-500/20\",\n            hoverEffect: \"hover:shadow-purple-500/40 hover:-translate-y-1\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm border border-white/30\",\n                                            children: \"✨ Professional Trading Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n                                        children: isSchwabCallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"\\uD83C\\uDF89 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300\",\n                                                    children: \"Connected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" Successfully!\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Your Trading \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 34\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                                    children: \"Command Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto mb-10 leading-relaxed\",\n                                        children: isSchwabCallback ? \"🚀 Your Schwab connection is live! Access real-time market data and manage your investment strategies with confidence.\" : \"🎯 Streamline your investment workflow with advanced analytics, real-time data integration, and comprehensive portfolio management tools.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold rounded-xl shadow-2xl hover:shadow-white/25 transition-all duration-300 transform hover:scale-105 hover:bg-blue-50\",\n                                        onClick: ()=>handleLogInToSchwab(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-2xl\",\n                                                children: \"\\uD83D\\uDD17\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Connect to Schwab\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-5 w-5 ml-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full backdrop-blur-sm animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-20 right-10 w-16 h-16 bg-yellow-300/20 rounded-full backdrop-blur-sm animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-20 w-12 h-12 bg-green-300/20 rounded-full backdrop-blur-sm animate-ping\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4 sm:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mr-4 backdrop-blur-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDC64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: [\n                                                                \"Welcome back, \",\n                                                                ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.split('@')[0]),\n                                                                \"! \\uD83D\\uDC4B\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2 \".concat(isLoggedInToSchwab ? 'bg-green-400' : 'bg-red-400', \" animate-pulse\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-100 text-sm\",\n                                                                    children: isLoggedInToSchwab ? \"🟢 Schwab Connected • Real-time data active\" : \"🔴 Schwab Disconnected • Limited access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                session && !isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogInToSchwab(),\n                                                    className: \"px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                                    children: \"\\uD83D\\uDD17 Connect Schwab\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoggedInToSchwab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogOutFromSchwab,\n                                                    className: \"px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10\",\n                                                    children: \"Disconnect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleLogout(),\n                                                    className: \"px-4 py-2 text-white/80 hover:text-white transition-colors text-sm border border-white/30 rounded-lg hover:bg-white/10\",\n                                                    children: \"\\uD83D\\uDEAA Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-gray-50 dark:bg-gray-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                                    children: \"\\uD83D\\uDCCA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Dashboard Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"✅ Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                                    children: \"⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Data Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"✅ Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                    children: \"Market Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-medium \".concat(isLoggedInToSchwab ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'),\n                                                    children: isLoggedInToSchwab ? '✅ Live' : '❌ Offline'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"\\uD83D\\uDE80 Platform Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto\",\n                                    children: \"Access all your trading tools and analytics from one powerful dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: navigationCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                    href: card.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative overflow-hidden rounded-2xl \".concat(card.bgColor, \" \").concat(card.shadowColor, \" shadow-2xl \").concat(card.hoverEffect, \" transition-all duration-300 cursor-pointer\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl\",\n                                                                children: card.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-6 w-6\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold mb-3 group-hover:text-yellow-200 transition-colors duration-300\",\n                                                        children: card.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm leading-relaxed mb-6\",\n                                                        children: card.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: \"Access Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-0.5 bg-white/60 group-hover:w-8 transition-all duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"Ready to Optimize Your Trading?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Access real-time market data, advanced analytics, and powerful trading tools all in one place.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/dashboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors\",\n                                                children: \"Go to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__, {\n                                            href: \"/Strategies/WB/configuration\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors backdrop-blur-sm\",\n                                                children: \"Configure Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\page.js\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vggbpo6EXw96Kx48ex04Bhn8Jac=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});