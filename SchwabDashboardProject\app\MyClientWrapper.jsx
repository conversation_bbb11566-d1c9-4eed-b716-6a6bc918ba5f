"use client";

import { Providers } from "@/components/providers.js";
import { MarketDataProvider } from "./testingWebsocket/MarketDataContext.js";
import NavMenu from "../components/NavMenu/NavMenu.jsx";
import { ExcelDataProvider } from "./Strategies/WB/configuration/page.js";
import { EndpointAppProvider } from "../components/EndpointAppContext.js";
import { PairArrayProvider } from "./pairArray/PairArray.js";
import { ComponentArrayProvider } from "./pairArray/PairComponent.js";
import { AccountDataProvider } from "./testingWebsocket/AccountDataContext.js";
import ProtectedRoute from "../components/ProtectedRoute";
import AuthOverlay from "../components/AuthOverlay";
import { usePathname } from "next/navigation";

import ThemeToggle from "../components/ThemeToggle.jsx";
import FabOrderEntry from "../components/FabOrderEntry.jsx";

export default function MyClientWrapper({ children }) {
  const pathname = usePathname();

  // Exclude login page from protection
  const isLoginPage = pathname === "/generalLogin";

  // Wrap content with ProtectedRoute if not on login page
  const wrappedContent = isLoginPage ? children : <ProtectedRoute>{children}</ProtectedRoute>;

  return (
    <Providers>
      <MarketDataProvider>
        <AccountDataProvider>
          <ExcelDataProvider>
            <EndpointAppProvider>
              <ComponentArrayProvider>
                <PairArrayProvider>
                  {/* Add the overlay at the top level */}
                  <AuthOverlay />
                  <div className="flex h-screen bg-white dark:bg-gray-950">
                    <div className="flex flex-col w-[80px]">
                      <NavMenu />
                    </div>
                    <div className="flex-1">
                      <main className="dark:text-gray-100">{wrappedContent}</main>
                      <ThemeToggle floating={true} />
                      {/* Floating Order Entry Button (FAB) visible on all pages for signed-in users */}
                      <FabOrderEntry />
                    </div>
                  </div>
                </PairArrayProvider>
              </ComponentArrayProvider>
            </EndpointAppProvider>
          </ExcelDataProvider>
        </AccountDataProvider>
      </MarketDataProvider>
    </Providers>
  );
}

