"use client";

import { Card } from "@/components/ui/card";

export function BalanceCard({ balance }) {
  return (
    <Card className="p-6 dark:bg-gray-800 dark:border-gray-700">
      <h2 className="text-xl font-semibold mb-4 dark:text-gray-100">Account Balance</h2>
      {balance ? (
        <p className="text-3xl font-bold dark:text-gray-50">${balance.toLocaleString()}</p>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">Unable to fetch balance</p>
      )}
    </Card>
  );
}
