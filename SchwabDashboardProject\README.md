**Project Name:** Schwab Dashboard

**Description:**
This is a Next.js project built with Tailwind CSS and Prisma, designed to interact with the Schwab API for account and market data. The application features secure authentication, robust admin/user management, and real-time data streaming via WebSockets.

**Getting Started:**

1. Clone the repository:  
   `git clone https://github.com/SavZappone/SchwabDashboardProject.git`
2. Install dependencies:  
   `npm install` or `yarn install`
3. Run the Next.js development server:  
   `npm run dev` or `yarn dev`
4. Open your browser and navigate to [https://localhost:3000](https://localhost:3000) to use the dashboard (HTTPS is required for Schwab API integration).

**WebSocket Server for Schwab Streaming:**

- The file `server.js` is an Express/Socket.io server that manages the WebSocket connection to Schwab’s streaming API and relays real-time data to the dashboard.
- You must run this server separately from the Next.js app:
  1. In a new terminal, run:  
     `node server.js`
  2. The server will start on [https://localhost:3001](https://localhost:3001) and handle WebSocket and REST endpoints for Schwab data.
- The dashboard will automatically connect to this server for live updates.

**Build and Deployment:**

1. To build the project, run:  
   `npm run build` or `yarn build`
2. To deploy, use a platform like Vercel, Netlify, or your preferred Node.js hosting for both the Next.js app and the `server.js` backend.

**Security & Best Practices:**

- All authentication and session cookies are set to Secure, HttpOnly, and SameSite=Strict.
- User self-registration is disabled; user management is admin-only.
- Strong HTTP security headers are enforced in both Next.js and Express servers.
- Rate limiting is applied to sensitive endpoints (e.g., login).
- Sensitive tokens are never stored in localStorage.

