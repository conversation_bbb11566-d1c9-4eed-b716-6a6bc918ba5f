"use client";

import * as React from "react";
import { useContext } from "react";
import { AppContext } from "../../../../components/AppContext";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  CheckIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useState } from "react";

export function WBConfigurationTable() {
  const { data, setData } = useContext(AppContext);

  const [editing, setEditing] = useState(null);
  const [newData, setNewData] = useState(data.StrategyConfig.pairCandidates);
  const [tempData, setTempData] = useState(data.StrategyConfig.pairCandidates);

  const handleDelete = (id) => {
    const newData = newData.filter((row) => row.id !== id);
    setNewData(newData);
    // setNewData((prevData) => prevData.filter((row) => row.id !== id));
  };

  const handleAdd = () => {
    const newRow = Object.keys(newData[0]).reduce(
      (acc, key) => {
        acc[key] = "";
        return acc;
      },
      { id: newData.length + 1 }
    );
    setNewData((prevData) => [...prevData, newRow]);
    setTempData((prevData) => [...prevData, newRow]);
  };

  const columns = React.useMemo(
    () => [
      {
        id: "actions",
        header: () => <div className="text-right">Actions</div>,
        cell: ({ row }) => (
          <div className="flex space-x-2">
            {editing === row.id ? (
              <>
                <CheckIcon
                  className="cursor-pointer text-blue-500 h-6 w-6"
                  onClick={() => {
                    setEditing(null);
                    setNewData(tempData);
                  }}
                />
                <XMarkIcon
                  className="cursor-pointer text-blue-500 h-6 w-6"
                  onClick={() => {
                    setEditing(null);
                    setTempData(newData);
                  }}
                />
              </>
            ) : (
              <PencilIcon
                className="cursor-pointer text-blue-500 h-6 w-6"
                onClick={() => setEditing(row.id)}
              />
            )}
            <TrashIcon
              className="cursor-pointer text-blue-500 h-6 w-6"
              onClick={() => handleDelete(row.id)}
            />
          </div>
        ),
      },
      ...Object.keys(newData[0]).map((key) => ({
        accessorKey: key,
        header: () => <div className="text-right">{key}</div>,
        cell: ({ row }) => (
          <div className="text-right font-medium">
            {editing === row.id ? (
              <input
                type="text"
                className="w-full h-8 px-2"
                value={tempData[row.index][key]}
                onChange={(e) =>
                  console.log(e.target.value) &&
                  setTempData(
                    tempData.map((item, index) =>
                      index === row.index
                        ? { ...item, [key]: e.target.value }
                        : item
                    )
                  )
                }
              />
            ) : (
              <span>{newData[row.index][key]}</span>
            )}
          </div>
        ),
      })),
    ],
    [newData, editing, tempData]
  );

  const table = useReactTable({
    data: newData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      editing,
    },
    onEditingChange: setEditing,
  });

  React.useEffect(() => {
    if (editing === null) {
      setData((prev) => ({
        ...prev,
        StrategyConfig: {
          ...prev.StrategyConfig,
          pairCandidates: newData,
        },
      }));
    }
  }, [newData, setData, editing]);

  return (
    <div className="w-full">
      <div className="flex justify-end py-2">
        <Button
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded flex items-center"
          onClick={handleAdd}
        >
          Add Row
        </Button>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    className="border px-0 py-0 mx-0 my-0 h-10 text-sm place-items-center"
                    key={header.id}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className="border px-0 py-0 mx-0 my-0 h-8 text-sm truncate place-items-center"
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
