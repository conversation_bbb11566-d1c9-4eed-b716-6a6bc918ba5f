import React from 'react';
import moment from 'moment';

const DateRangeSelector = ({ selectedRange, onRangeChange, className = "" }) => {
  const ranges = [
    {
      key: 'today',
      label: 'Today',
      getStartDate: () => moment().startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last3days',
      label: 'Last 3 Days',
      getStartDate: () => moment().subtract(3, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last7days',
      label: 'Last 7 Days',
      getStartDate: () => moment().subtract(7, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last14days',
      label: 'Last 14 Days',
      getStartDate: () => moment().subtract(14, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last30days',
      label: 'Last 30 Days',
      getStartDate: () => moment().subtract(30, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last60days',
      label: 'Last 60 Days',
      getStartDate: () => moment().subtract(60, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    },
    {
      key: 'last90days',
      label: 'Last 90 Days',
      getStartDate: () => moment().subtract(90, 'days').startOf('day'),
      getEndDate: () => moment().endOf('day')
    }
  ];

  const handleRangeSelect = (e) => {
    const selectedKey = e.target.value;
    const range = ranges.find(r => r.key === selectedKey);

    if (range) {
      const startDate = range.getStartDate().toISOString();
      const endDate = range.getEndDate().toISOString();

      onRangeChange({
        key: range.key,
        label: range.label,
        startDate,
        endDate
      });
    }
  };

  return (
    <select
      value={selectedRange?.key || 'last7days'}
      onChange={handleRangeSelect}
      className={`px-2 py-1 text-xs rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 ${className}`}
    >
      {ranges.map((range) => (
        <option key={range.key} value={range.key}>
          {range.label}
        </option>
      ))}
    </select>
  );
};

export default DateRangeSelector;
