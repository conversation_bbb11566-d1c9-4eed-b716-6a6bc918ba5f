"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/Strategies/WB/configuration/page.js":
/*!*************************************************!*\
  !*** ./app/Strategies/WB/configuration/page.js ***!
  \*************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExcelDataProvider: () => (/* binding */ ExcelDataProvider),\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   useExcelData: () => (/* binding */ useExcelData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TooltipButton */ \"(app-pages-browser)/./components/TooltipButton.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ ExcelDataProvider,useExcelData,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst shortKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst longKeys = [\n    \"ticker\",\n    \"shares\",\n    \"sector\",\n    \"spread\",\n    \"volume\",\n    \"status\",\n    \"dividend\"\n];\nconst ExcelDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    shortOpenTableData: [],\n    shortLoadedTableData: [],\n    longOpenTableData: [],\n    longLoadedTableData: [],\n    shortClosedTableData: [],\n    longClosedTableData: [],\n    setShortOpenTableData: ()=>{},\n    setShortLoadedTableData: ()=>{},\n    setLongOpenTableData: ()=>{},\n    setLongLoadedTableData: ()=>{},\n    setShortClosedTableData: ()=>{},\n    setLongClosedTableData: ()=>{},\n    updateLongStatus: ()=>{},\n    updateShortStatus: ()=>{}\n});\nfunction ExcelDataProvider(param) {\n    let { children } = param;\n    _s();\n    const [shortOpenTableData, setShortOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortLoadedTableData, setShortLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longOpenTableData, setLongOpenTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longOpenTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longLoadedTableData, setLongLoadedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longLoadedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [shortClosedTableData, setShortClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"shortClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    const [longClosedTableData, setLongClosedTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"longClosedTableData\");\n                return data ? JSON.parse(data) : [];\n            }\n            return [];\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    // Nuovo stato per i dati di mercato (dividend e ex-date)\n    const [marketDataCache, setMarketDataCache] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"ExcelDataProvider.useState\": ()=>{\n            if (true) {\n                const data = localStorage.getItem(\"schwabMarketDataCache\");\n                return data ? JSON.parse(data) : {};\n            }\n            return {};\n        }\n    }[\"ExcelDataProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelDataProvider.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem(\"shortOpenTableData\", JSON.stringify(shortOpenTableData));\n                localStorage.setItem(\"shortLoadedTableData\", JSON.stringify(shortLoadedTableData));\n                localStorage.setItem(\"longOpenTableData\", JSON.stringify(longOpenTableData));\n                localStorage.setItem(\"longLoadedTableData\", JSON.stringify(longLoadedTableData));\n                localStorage.setItem(\"shortClosedTableData\", JSON.stringify(shortClosedTableData));\n                localStorage.setItem(\"longClosedTableData\", JSON.stringify(longClosedTableData));\n                localStorage.setItem(\"schwabMarketDataCache\", JSON.stringify(marketDataCache));\n            }\n        }\n    }[\"ExcelDataProvider.useEffect\"], [\n        shortOpenTableData,\n        shortLoadedTableData,\n        shortClosedTableData,\n        longOpenTableData,\n        longLoadedTableData,\n        longClosedTableData,\n        marketDataCache\n    ]);\n    // Move a long row from any table to any other table, like the old system but for all tables\n    const updateLongStatus = (rowId, newStatus)=>{\n        // Try to find the row in all tables\n        let row = longLoadedTableData.find((r)=>r.id === rowId) || longOpenTableData.find((r)=>r.id === rowId) || longClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        // Remove from all tables\n        setLongLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setLongClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        // Add to the right table\n        if (newStatus === \"WB_LoadedPairs\") {\n            setLongLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setLongOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setLongClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    // Move a short row from any table to any other table, like the old system but for all tables\n    const updateShortStatus = (rowId, newStatus)=>{\n        let row = shortLoadedTableData.find((r)=>r.id === rowId) || shortOpenTableData.find((r)=>r.id === rowId) || shortClosedTableData.find((r)=>r.id === rowId);\n        if (!row) return;\n        const updated = {\n            ...row,\n            status: newStatus\n        };\n        setShortLoadedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortOpenTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        setShortClosedTableData((prev)=>prev.filter((r)=>r.id !== rowId));\n        if (newStatus === \"WB_LoadedPairs\") {\n            setShortLoadedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_OpenPositions\") {\n            setShortOpenTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        } else if (newStatus === \"WB_ClosedPositions\") {\n            setShortClosedTableData((prev)=>[\n                    ...prev,\n                    updated\n                ]);\n        }\n    };\n    const updateShortClosedStatus = (rowId, newStatus)=>{\n        setShortOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setShortClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    const updateLongClosedStatus = (rowId, newStatus)=>{\n        setLongOpenTableData((prev)=>{\n            const row = prev.find((r)=>r.id === rowId);\n            if (!row) return prev;\n            const updatedRow = {\n                ...row,\n                status: newStatus\n            };\n            setLongClosedTableData((closedPrev)=>{\n                if (closedPrev.some((r)=>r.id === rowId)) {\n                    return closedPrev;\n                }\n                return [\n                    ...closedPrev,\n                    updatedRow\n                ];\n            });\n            return prev.filter((r)=>r.id !== rowId);\n        });\n    };\n    // Funzioni per gestire i dati di mercato (dividend e ex-date)\n    const updateMarketData = (ticker, data)=>{\n        setMarketDataCache((prev)=>({\n                ...prev,\n                [ticker]: {\n                    ...prev[ticker],\n                    ...data,\n                    lastUpdated: new Date().toISOString()\n                }\n            }));\n    };\n    const getMarketData = (ticker)=>{\n        return marketDataCache[ticker] || null;\n    };\n    const clearMarketDataCache = ()=>{\n        setMarketDataCache({});\n        if (true) {\n            localStorage.removeItem(\"schwabMarketDataCache\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelDataContext.Provider, {\n        value: {\n            shortOpenTableData,\n            shortLoadedTableData,\n            longOpenTableData,\n            longLoadedTableData,\n            shortClosedTableData,\n            longClosedTableData,\n            setShortOpenTableData,\n            setShortLoadedTableData,\n            setLongOpenTableData,\n            setLongLoadedTableData,\n            setShortClosedTableData,\n            setLongClosedTableData,\n            updateLongStatus,\n            updateShortStatus,\n            updateShortClosedStatus,\n            updateLongClosedStatus,\n            // Nuove funzioni per i dati di mercato\n            marketDataCache,\n            updateMarketData,\n            getMarketData,\n            clearMarketDataCache\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcelDataProvider, \"EhmNOFFD7ayNnBzEWg+ivH/WxF4=\");\n_c = ExcelDataProvider;\nfunction useExcelData() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ExcelDataContext);\n}\n_s1(useExcelData, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction ExcelInput() {\n    _s2();\n    const [pasteData, setPasteData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [updateStatus, setUpdateStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [redirectAfterSubmit, setRedirectAfterSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [savedSymbols, setSavedSymbols] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { setShortLoadedTableData, setLongLoadedTableData, shortLoadedTableData, longLoadedTableData, shortOpenTableData, longOpenTableData, shortClosedTableData, longClosedTableData } = useExcelData();\n    // Load saved symbols on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExcelInput.useEffect\": ()=>{\n            const loadSavedSymbols = {\n                \"ExcelInput.useEffect.loadSavedSymbols\": async ()=>{\n                    try {\n                        const response = await fetch('/api/get-saved-symbols');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setSavedSymbols(data.symbolsArray || []);\n                            console.log('Loaded saved symbols:', data.symbolsArray);\n                        }\n                    } catch (error) {\n                        console.error('Error loading saved symbols:', error);\n                    }\n                }\n            }[\"ExcelInput.useEffect.loadSavedSymbols\"];\n            loadSavedSymbols();\n        }\n    }[\"ExcelInput.useEffect\"], []);\n    const handleChange = (e)=>{\n        setPasteData(e.target.value);\n    };\n    const handleSubmit = async ()=>{\n        const rows = pasteData.split(/\\r?\\n/).filter((row)=>row.trim() !== \"\");\n        const parsedData = rows.map((row)=>row.split(\"\\t\"));\n        const existingDataShort = shortLoadedTableData.length + shortOpenTableData.length + shortClosedTableData.length || 0;\n        const existingDataLong = longLoadedTableData.length + longOpenTableData.length + longClosedTableData.length || 0;\n        const shortData = parsedData.map((row, index)=>{\n            const obj = {};\n            shortKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataShort + index).toString()\n            };\n        });\n        const longData = parsedData.map((row, index)=>{\n            const obj = {};\n            longKeys.forEach((key, idx)=>{\n                if (key === \"status\") {\n                    obj[key] = \"WB_LoadedPairs\";\n                } else if (key === \"dividend\") {\n                    obj[key] = \"0\";\n                } else {\n                    obj[key] = row[idx + 5] || \"\";\n                }\n            });\n            return {\n                ...obj,\n                id: (existingDataLong + index).toString()\n            };\n        });\n        setShortLoadedTableData((prev)=>[\n                ...prev,\n                ...shortData\n            ]);\n        setLongLoadedTableData((prev)=>[\n                ...prev,\n                ...longData\n            ]);\n        setPasteData(\"\");\n        // Display status message\n        setUpdateStatus(\"Data processed successfully. Pairs will be saved to database when created.\");\n        // Redirect to dashboard if option is enabled\n        if (redirectAfterSubmit) {\n            // Wait a moment to ensure data is saved to localStorage\n            setTimeout(()=>{\n                window.location.href = \"/Strategies/WB/dashboard\";\n            }, 500);\n        }\n    };\n    // Function to update stock symbols on the server\n    const updateStockSymbols = async ()=>{\n        try {\n            setUpdateStatus(\"Update in progress...\");\n            // Collect all ticker symbols from both short and long data\n            const shortTickers = [\n                ...shortLoadedTableData,\n                ...shortOpenTableData,\n                ...shortClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            const longTickers = [\n                ...longLoadedTableData,\n                ...longOpenTableData,\n                ...longClosedTableData\n            ].map((item)=>item.ticker).filter((ticker)=>ticker && ticker.trim() !== \"\");\n            // Combine and remove duplicates\n            const allTickers = [\n                ...new Set([\n                    ...shortTickers,\n                    ...longTickers\n                ])\n            ];\n            if (allTickers.length === 0) {\n                setUpdateStatus(\"No symbols found to update\");\n                return;\n            }\n            console.log(\"Symbols to send to server:\", allTickers);\n            // First, test if the server is responding\n            try {\n                const testUrl = \"https://localhost:3001/test\";\n                setUpdateStatus(\"Verifying server connection: \".concat(testUrl, \"...\"));\n                const testResponse = await fetch(testUrl);\n                if (!testResponse.ok) {\n                    setUpdateStatus(\"Error: The server is not responding correctly. Code: \".concat(testResponse.status));\n                    return;\n                }\n                const testData = await testResponse.json();\n                console.log(\"Test server response:\", testData);\n                setUpdateStatus(\"Server connected. Current symbols: \".concat(testData.currentSymbols, \". Sending new symbols...\"));\n            } catch (testError) {\n                console.error(\"Error in connection test:\", testError);\n                setUpdateStatus(\"Connection error: \".concat(testError.message, \". Make sure the server is running on http://localhost:3001\"));\n                return;\n            }\n            // Send to server\n            const url = \"https://localhost:3001/update-stock-symbols\";\n            try {\n                var _sessionData_user;\n                const accessToken = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveAccessToken)();\n                const customerId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCustomerId)();\n                const correlId = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.retrieveCorrelId)();\n                // Get user session for email\n                const session = await fetch('/api/auth/session');\n                const sessionData = await session.json();\n                const userEmail = sessionData === null || sessionData === void 0 ? void 0 : (_sessionData_user = sessionData.user) === null || _sessionData_user === void 0 ? void 0 : _sessionData_user.email;\n                if (!userEmail) {\n                    console.warn('No user email found in session, symbols will not be associated with user account');\n                }\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        symbols: allTickers,\n                        token: accessToken,\n                        clientCustomerId: customerId,\n                        clientCorrelId: correlId,\n                        userEmail: userEmail\n                    }),\n                    credentials: \"include\"\n                });\n                // Check if response is JSON\n                const contentType = response.headers.get(\"content-type\");\n                if (!contentType || !contentType.includes(\"application/json\")) {\n                    const textResponse = await response.text();\n                    console.error(\"Server returned non-JSON response:\", textResponse);\n                    setUpdateStatus(\"Error: The server returned an invalid response. Make sure the server is running on http://localhost:3001\");\n                    return;\n                }\n                const data = await response.json();\n                if (response.ok) {\n                    setUpdateStatus(\"Symbols updated successfully: \".concat(data.symbols));\n                    // Update the saved symbols display\n                    setSavedSymbols(data.symbols.split(','));\n                } else {\n                    setUpdateStatus(\"Error: \".concat(data.error || 'Unknown error'));\n                }\n            } catch (fetchError) {\n                console.error(\"Error in fetch request:\", fetchError);\n                setUpdateStatus(\"Connection error: \".concat(fetchError.message, \". Make sure the server is running on http://localhost:3001\"));\n            }\n        } catch (error) {\n            console.error(\"Error updating symbols:\", error);\n            setUpdateStatus(\"Error: \".concat(error.message || 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Paste Data from Excel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            onChange: handleChange,\n                            value: pasteData,\n                            placeholder: \"Paste Excel data here (tabular format)\",\n                            rows: \"10\",\n                            className: \"w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                onClick: handleSubmit,\n                                className: \"bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                tooltipText: \"Process the pasted Excel data and update the tables\",\n                                children: \"Process Data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, this),\n                            savedSymbols.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\",\n                                        children: [\n                                            \"Currently Saved Symbols (\",\n                                            savedSymbols.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-700 dark:text-blue-300 break-words\",\n                                        children: savedSymbols.join(', ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                onClick: updateStockSymbols,\n                                className: \"bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                tooltipText: \"Update stock symbols on the server for real-time data\",\n                                children: \"Update Symbols on Server\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"redirectToggle\",\n                                checked: redirectAfterSubmit,\n                                onChange: ()=>setRedirectAfterSubmit(!redirectAfterSubmit),\n                                className: \"mr-2 h-4 w-4 text-blue-600 dark:text-blue-500 focus:ring-blue-500 dark:focus:ring-blue-600 dark:bg-gray-700 dark:border-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"redirectToggle\",\n                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                children: \"Redirect to dashboard after processing data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    updateStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-md \".concat(updateStatus.includes('Error') ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800'),\n                        children: updateStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 403,\n        columnNumber: 5\n    }, this);\n}\n_s2(ExcelInput, \"3pu7fML+lOyH2fWTWG9569dVCXw=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c1 = ExcelInput;\nfunction ClearButton() {\n    _s3();\n    const { setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData, setShortClosedTableData, setLongClosedTableData, clearMarketDataCache } = useExcelData();\n    const [clearStatus, setClearStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const clearData = ()=>{\n        setShortOpenTableData([]);\n        setShortLoadedTableData([]);\n        setLongOpenTableData([]);\n        setLongLoadedTableData([]);\n        setShortClosedTableData([]);\n        setLongClosedTableData([]);\n        clearMarketDataCache(); // Pulisce anche i dati di mercato\n        if (true) {\n            localStorage.removeItem(\"shortOpenTableData\");\n            localStorage.removeItem(\"shortLoadedTableData\");\n            localStorage.removeItem(\"longOpenTableData\");\n            localStorage.removeItem(\"longLoadedTableData\");\n            localStorage.removeItem(\"shortClosedTableData\");\n            localStorage.removeItem(\"longClosedTableData\");\n            localStorage.removeItem(\"schwabMarketDataCache\");\n        }\n        setClearStatus(\"All data has been cleared (including market data)\");\n        setTimeout(()=>setClearStatus(\"\"), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 overflow-hidden mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 py-3 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Data Management\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Clear all Excel data from memory and local storage. This action cannot be undone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TooltipButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        onClick: clearData,\n                                        className: \"bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                                        tooltipText: \"Clear all Excel data from memory and local storage\",\n                                        children: \"Clear All Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 text-red-200 dark:text-red-900\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    clearStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 rounded-md bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800\",\n                        children: clearStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 505,\n        columnNumber: 5\n    }, this);\n}\n_s3(ClearButton, \"B2XivFPLEmwygnUrmavRKdgFai4=\", false, function() {\n    return [\n        useExcelData\n    ];\n});\n_c2 = ClearButton;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 text-white py-6 px-4 shadow-md dark:shadow-indigo-950 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"WB Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mt-2\",\n                            children: \"Configure your WB trading strategy settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                    lineNumber: 544,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 543,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExcelInput, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClearButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                        lineNumber: 552,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabDashboardProject\\\\app\\\\Strategies\\\\WB\\\\configuration\\\\page.js\",\n        lineNumber: 541,\n        columnNumber: 5\n    }, this);\n}\n_c3 = Home;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ExcelDataProvider\");\n$RefreshReg$(_c1, \"ExcelInput\");\n$RefreshReg$(_c2, \"ClearButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/Strategies/WB/configuration/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a281b601eece\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWxsZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxEYXNoYm9hcmRcXFNjaHdhYkRhc2hib2FyZFByb2plY3RcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhMjgxYjYwMWVlY2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});